// Version: 1.2 - Last updated with Search Dropdown
console.log('dairy.js loaded - ' + new Date().toISOString());

/**
 * Search Dropdown Manager for Dairy Page
 * Handles the dropdown menu for the Search button with inline input fields
 */
class DairySearchDropdownManager {
    constructor() {
        this.dropdownContainer = null;
        this.isVisible = false;
        this.currentSearchField = null;
        this.currentSearchTerm = '';
    }

    /**
     * Show search dropdown menu
     * @param {HTMLElement} buttonElement - Search button element
     */
    showDropdown(buttonElement) {
        // Hide any existing dropdown
        this.hideDropdown();

        // Add active class to search container
        const searchContainer = buttonElement.closest('.search-dropdown-container');
        if (searchContainer) {
            searchContainer.classList.add('active');
        }

        // Create dropdown
        this.createDropdown(buttonElement);
        this.showDropdownWithAnimation();
    }

    /**
     * Create search dropdown menu
     * @param {HTMLElement} buttonElement - Search button element
     */
    createDropdown(buttonElement) {
        this.dropdownContainer = document.createElement('div');
        this.dropdownContainer.className = 'search-dropdown-menu-dynamic';

        const buttonRect = buttonElement.getBoundingClientRect();

        this.dropdownContainer.innerHTML = `
            <div class="search-option-dynamic" data-search="code">
                <span class="search-icon-dynamic">🏷️</span>
                <span>By Code</span>
            </div>
            <div class="search-input-container-dynamic" id="codeSearchContainerDynamic" style="display: none;">
                <input type="text" class="search-input-dynamic" id="codeSearchInputDynamic" placeholder="Enter dairy animal code...">
                <div class="search-buttons-dynamic">
                    <button class="search-btn-small-dynamic" data-search-type="code">Search</button>
                    <button class="search-btn-cancel-dynamic">Cancel</button>
                </div>
            </div>

            <div class="search-option-dynamic" data-search="herdNumber">
                <span class="search-icon-dynamic">🏠</span>
                <span>By Herd Number</span>
            </div>
            <div class="search-input-container-dynamic" id="herdNumberSearchContainerDynamic" style="display: none;">
                <input type="text" class="search-input-dynamic" id="herdNumberSearchInputDynamic" placeholder="Enter herd number...">
                <div class="search-buttons-dynamic">
                    <button class="search-btn-small-dynamic" data-search-type="herdNumber">Search</button>
                    <button class="search-btn-cancel-dynamic">Cancel</button>
                </div>
            </div>

            <div class="search-option-dynamic" data-search="milkProduction">
                <span class="search-icon-dynamic">🥛</span>
                <span>By Milk Production</span>
            </div>
            <div class="search-input-container-dynamic" id="milkProductionSearchContainerDynamic" style="display: none;">
                <input type="text" class="search-input-dynamic" id="milkProductionSearchInputDynamic" placeholder="Enter milk production (e.g., 25L)...">
                <div class="search-buttons-dynamic">
                    <button class="search-btn-small-dynamic" data-search-type="milkProduction">Search</button>
                    <button class="search-btn-cancel-dynamic">Cancel</button>
                </div>
            </div>

            <div class="search-option-dynamic" data-search="clear">
                <span class="search-icon-dynamic">🔄</span>
                <span>Clear Search</span>
            </div>
        `;

        // Position dropdown
        this.dropdownContainer.style.cssText = `
            position: fixed;
            top: ${buttonRect.bottom + 5}px;
            left: ${buttonRect.left}px;
            z-index: 9999;
            background-color: #c1d8b9;
            border: 2px solid #a8c49a;
            border-radius: 14px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 0;
            min-width: 280px;
            overflow: hidden;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        `;

        // Add styles
        this.addSearchDropdownStyles();

        // Add event listeners
        this.setupSearchDropdownEventListeners();

        // Add to document
        document.body.appendChild(this.dropdownContainer);
    }

    /**
     * Add CSS styles for search dropdown (matching dairy page style)
     */
    addSearchDropdownStyles() {
        if (!document.querySelector('#search-dropdown-styles-dairy')) {
            const style = document.createElement('style');
            style.id = 'search-dropdown-styles-dairy';
            style.textContent = `
                .search-option-dynamic {
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    gap: 10px;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                    color: #a9a9a9;
                }

                .search-option-dynamic:last-child {
                    border-bottom: none;
                }

                .search-option-dynamic:hover {
                    background-color: #a8c49a;
                    color: #ffffff;
                }

                .search-option-dynamic.active {
                    background-color: #a8c49a;
                    color: #ffffff;
                    font-weight: bold;
                }

                .search-icon-dynamic {
                    font-size: 18px;
                    display: inline-block;
                    transition: all 0.3s ease;
                }

                .search-option-dynamic span {
                    font-size: 16px;
                    font-weight: 500;
                    color: inherit;
                }

                /* Search input containers */
                .search-input-container-dynamic {
                    padding: 12px 16px;
                    background-color: #f8f9fa;
                    border-top: 1px solid #e0e0e0;
                    border-bottom: 1px solid #e0e0e0;
                }

                .search-input-dynamic {
                    width: 100%;
                    padding: 8px 12px;
                    border: 2px solid #c1d8b9;
                    border-radius: 8px;
                    font-size: 14px;
                    margin-bottom: 8px;
                    outline: none;
                    transition: border-color 0.3s ease;
                }

                .search-input-dynamic:focus {
                    border-color: #a8c49a;
                    box-shadow: 0 0 0 2px rgba(193, 216, 185, 0.2);
                }

                .search-input-dynamic::placeholder {
                    color: #999;
                    font-style: italic;
                }

                .search-buttons-dynamic {
                    display: flex;
                    gap: 8px;
                    justify-content: flex-end;
                }

                .search-btn-small-dynamic {
                    padding: 6px 12px;
                    background-color: #c1d8b9;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .search-btn-small-dynamic:hover {
                    background-color: #a8c49a;
                    transform: translateY(-1px);
                }

                .search-btn-cancel-dynamic {
                    padding: 6px 12px;
                    background-color: #e0e0e0;
                    color: #666;
                    border: none;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .search-btn-cancel-dynamic:hover {
                    background-color: #d0d0d0;
                    transform: translateY(-1px);
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Setup event listeners for search dropdown
     */
    setupSearchDropdownEventListeners() {
        if (!this.dropdownContainer) return;

        // Search option clicks
        const searchOptions = this.dropdownContainer.querySelectorAll('.search-option-dynamic');
        searchOptions.forEach(option => {
            option.addEventListener('click', () => {
                const searchType = option.getAttribute('data-search');
                this.selectSearchOption(option, searchType);
            });
        });

        // Search buttons
        const searchButtons = this.dropdownContainer.querySelectorAll('.search-btn-small-dynamic');
        searchButtons.forEach(button => {
            button.addEventListener('click', () => {
                const searchType = button.getAttribute('data-search-type');
                this.performSearch(searchType);
            });
        });

        // Cancel buttons
        const cancelButtons = this.dropdownContainer.querySelectorAll('.search-btn-cancel-dynamic');
        cancelButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.cancelSearch();
            });
        });

        // Enter key in input fields
        const inputFields = this.dropdownContainer.querySelectorAll('.search-input-dynamic');
        inputFields.forEach(input => {
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    const searchType = input.id.replace('SearchInputDynamic', '').replace('code', 'code').replace('herdNumber', 'herdNumber').replace('milkProduction', 'milkProduction');
                    this.performSearch(searchType);
                }
            });
        });

        // Click outside to close
        document.addEventListener('click', this.handleSearchOutsideClick.bind(this));
    }

    /**
     * Select search option and show input field
     * @param {HTMLElement} optionElement - Selected option element
     * @param {string} searchType - Type of search
     */
    selectSearchOption(optionElement, searchType) {
        // Remove active class from all options
        this.dropdownContainer.querySelectorAll('.search-option-dynamic').forEach(opt => {
            opt.classList.remove('active');
        });

        // Hide all input containers
        this.dropdownContainer.querySelectorAll('.search-input-container-dynamic').forEach(container => {
            container.style.display = 'none';
        });

        // Add active class to selected option
        optionElement.classList.add('active');

        // Execute search based on type
        if (searchType === 'clear') {
            this.clearSearch();
            this.hideDropdown();
        } else {
            // Show the corresponding input container
            const inputContainer = this.dropdownContainer.querySelector(`#${searchType}SearchContainerDynamic`);
            if (inputContainer) {
                inputContainer.style.display = 'block';

                // Focus on the input field
                const inputField = this.dropdownContainer.querySelector(`#${searchType}SearchInputDynamic`);
                if (inputField) {
                    setTimeout(() => inputField.focus(), 100);
                }
            }
        }
    }

    /**
     * Perform search with input value
     * @param {string} searchType - Type of search
     */
    performSearch(searchType) {
        const inputField = this.dropdownContainer.querySelector(`#${searchType}SearchInputDynamic`);
        if (inputField) {
            const searchTerm = inputField.value.trim();
            if (searchTerm) {
                console.log(`🔍 Dairy search: ${searchType} = "${searchTerm}"`);
                this.currentSearchTerm = searchTerm;
                this.currentSearchField = searchType;
                this.searchDairyAnimals(searchTerm, searchType);
                this.hideDropdown();
                // Clear the input field
                inputField.value = '';
            } else {
                // Show error or highlight empty field
                inputField.style.borderColor = '#ff6b6b';
                setTimeout(() => {
                    inputField.style.borderColor = '#c1d8b9';
                }, 1000);
            }
        }
    }

    /**
     * Cancel search and close input
     */
    cancelSearch() {
        // Clear all input fields
        this.dropdownContainer.querySelectorAll('.search-input-dynamic').forEach(input => {
            input.value = '';
        });

        // Hide all input containers
        this.dropdownContainer.querySelectorAll('.search-input-container-dynamic').forEach(container => {
            container.style.display = 'none';
        });

        // Remove active class from all options
        this.dropdownContainer.querySelectorAll('.search-option-dynamic').forEach(opt => {
            opt.classList.remove('active');
        });
    }

    /**
     * Clear search and show all animals
     */
    clearSearch() {
        console.log('🔄 Clearing dairy search');
        this.currentSearchTerm = '';
        this.currentSearchField = null;
        window.loadDairyAnimals(); // Reload all animals
    }

    /**
     * Search dairy animals by specific field
     * @param {string} searchTerm - Search term
     * @param {string} searchField - Field to search in
     */
    searchDairyAnimals(searchTerm, searchField) {
        const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
        const term = searchTerm.toLowerCase();

        let filteredAnimals = dairyAnimals.filter(animal => {
            switch (searchField) {
                case 'code':
                    return animal.code && animal.code.toLowerCase().includes(term);
                case 'herdNumber':
                    return animal.herdNumber && animal.herdNumber.toLowerCase().includes(term);
                case 'milkProduction':
                    return animal.lastMilkProduction && animal.lastMilkProduction.toString().toLowerCase().includes(term);
                default:
                    // Fallback to general search
                    return (animal.code && animal.code.toLowerCase().includes(term)) ||
                           (animal.herdNumber && animal.herdNumber.toLowerCase().includes(term));
            }
        });

        this.displaySearchResults(filteredAnimals, searchTerm, searchField);
    }

    /**
     * Display search results in the table
     * @param {Array} animals - Filtered animals
     * @param {string} searchTerm - Search term
     * @param {string} searchField - Search field
     */
    displaySearchResults(animals, searchTerm, searchField) {
        const tableBody = document.querySelector('tbody');
        tableBody.innerHTML = '';

        
        if (animals.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="11" style="text-align: center;">
                    No dairy animals found for ${searchField}: "${searchTerm}"
                </td>
            `;
            tableBody.appendChild(row);
            return;
        }

        // Add each filtered animal to the table
        animals.forEach((animal, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input class="checkbox" type="checkbox" data-index="${index}">${animal.code}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${animal.type === 'milch' ? 'Drying' : (animal.type === 'drying' ? 'Drying' : (animal.type === 'dairy' ? 'Dairy' : (animal.type || 'N/A')))}</td>
                <td>${animal.herdNumber || 'N/A'}</td>
                <td>${animal.lastMilkProduction ? animal.lastMilkProduction + 'L' : 'N/A'}</td>
                <td>${animal.fatPercentage ? animal.fatPercentage + '%' : 'N/A'}</td>
                <td>${animal.weight || 'N/A'}</td>
                <td>${this.formatDate(animal.weightDate) || 'N/A'}</td>
                <td>${animal.artificialInseminationStatus || 'N/A'}</td>
                <td>${this.formatDate(animal.artificialInseminationDate) || 'N/A'}</td>
                <td>${this.formatDate(animal.expectedCalfingDate) || 'N/A'}</td>
                <td>${animal.healthcareNotes || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });

        console.log(`📊 Dairy search results: ${animals.length} animals found`);
    }

    /**
     * Format date for display
     * @param {string} dateString - Date string
     * @returns {string} - Formatted date
     */
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    /**
     * Handle click outside search dropdown
     * @param {Event} e - Click event
     */
    handleSearchOutsideClick(e) {
        if (this.isVisible && this.dropdownContainer && !this.dropdownContainer.contains(e.target)) {
            this.hideDropdown();
        }
    }

    /**
     * Show dropdown with animation
     */
    showDropdownWithAnimation() {
        if (this.dropdownContainer) {
            this.isVisible = true;
            setTimeout(() => {
                this.dropdownContainer.style.opacity = '1';
                this.dropdownContainer.style.transform = 'translateY(0)';
            }, 10);
        }
    }

    /**
     * Hide dropdown
     */
    hideDropdown() {
        if (this.dropdownContainer && this.isVisible) {
            this.dropdownContainer.style.opacity = '0';
            this.dropdownContainer.style.transform = 'translateY(-10px)';

            // Remove active class from search container
            const searchContainer = document.querySelector('.search-dropdown-container.active');
            if (searchContainer) {
                searchContainer.classList.remove('active');
            }

            setTimeout(() => {
                if (this.dropdownContainer && this.dropdownContainer.parentNode) {
                    this.dropdownContainer.parentNode.removeChild(this.dropdownContainer);
                    this.dropdownContainer = null;
                }
            }, 300);

            this.isVisible = false;

            // Remove event listener
            document.removeEventListener('click', this.handleSearchOutsideClick.bind(this));
        }
    }
}

/**
 * Filter Dropdown Manager for Dairy Page
 * Handles the dropdown menu for the Filter button with form controls
 */
class DairyFilterDropdownManager {
    constructor() {
        this.dropdownContainer = null;
        this.isVisible = false;
        this.currentFilters = {};
    }

    /**
     * Show filter dropdown menu
     * @param {HTMLElement} buttonElement - Filter button element
     */
    showDropdown(buttonElement) {
        // Hide any existing dropdown
        this.hideDropdown();

        // Add active class to filter container
        const filterContainer = buttonElement.closest('.filter-dropdown-container');
        if (filterContainer) {
            filterContainer.classList.add('active');
        }

        // Create dropdown
        this.createDropdown(buttonElement);
        this.showDropdownWithAnimation();
    }

    /**
     * Create filter dropdown menu
     * @param {HTMLElement} buttonElement - Filter button element
     */
    createDropdown(buttonElement) {
        this.dropdownContainer = document.createElement('div');
        this.dropdownContainer.className = 'filter-dropdown-menu-dynamic';

        const buttonRect = buttonElement.getBoundingClientRect();

        this.dropdownContainer.innerHTML = `
            <div class="filter-section-dynamic">
                <label for="typeFilterDynamic">Type:</label>
                <select class="filter-select-dynamic" id="typeFilterDynamic" data-field="type">
                    <option value="all">All Types</option>
                    <option value="dairy">Dairy</option>
                    <option value="drying">Drying</option>
                </select>
            </div>

            <div class="filter-section-dynamic">
                <label for="herdNumberFilterDynamic">Herd Number:</label>
                <select class="filter-select-dynamic" id="herdNumberFilterDynamic" data-field="herdNumber">
                    <option value="all">All Herds</option>
                </select>
            </div>

            <div class="filter-section-dynamic">
                <label for="milkProductionFilterDynamic">Milk Production:</label>
                <select class="filter-select-dynamic" id="milkProductionFilterDynamic" data-field="milkProduction">
                    <option value="all">All Production Levels</option>
                    <option value="high">High (>30L)</option>
                    <option value="medium">Medium (20-30L)</option>
                    <option value="low">Low (<20L)</option>
                </select>
            </div>

            <div class="filter-section-dynamic">
                <label for="aiStatusFilterDynamic">AI Status:</label>
                <select class="filter-select-dynamic" id="aiStatusFilterDynamic" data-field="artificialInseminationStatus">
                    <option value="all">All Statuses</option>
                    <option value="yes">Yes</option>
                    <option value="no">No</option>
                    <option value="pregnant">Pregnant</option>
                </select>
            </div>

            <div class="filter-actions-dynamic">
                <button class="filter-apply-btn-dynamic">Apply Filters</button>
                <button class="filter-clear-btn-dynamic">Clear All</button>
            </div>
        `;

        // Position dropdown
        this.dropdownContainer.style.cssText = `
            position: fixed;
            top: ${buttonRect.bottom + 5}px;
            left: ${buttonRect.left}px;
            z-index: 9999;
            background: #aedf32;
            border-radius: 14px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 2px solid #8fb728;
            padding: 16px;
            min-width: 280px;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        `;

        // Add styles
        this.addFilterDropdownStyles();

        // Populate dynamic options
        this.populateHerdNumbers();

        // Add event listeners
        this.setupFilterDropdownEventListeners();

        // Set current filter values
        this.setCurrentFilterValues();

        // Add to document
        document.body.appendChild(this.dropdownContainer);
    }

    /**
     * Add CSS styles for filter dropdown (matching animal page style)
     */
    addFilterDropdownStyles() {
        if (!document.querySelector('#filter-dropdown-styles-dairy')) {
            const style = document.createElement('style');
            style.id = 'filter-dropdown-styles-dairy';
            style.textContent = `
                .filter-section-dynamic {
                    margin-bottom: 14px;
                }

                .filter-section-dynamic label {
                    display: block;
                    font-size: 14px;
                    font-weight: bold;
                    color: #ffffff;
                    margin-bottom: 6px;
                }

                .filter-select-dynamic {
                    width: 100%;
                    padding: 8px 12px;
                    border: 2px solid #8fb728;
                    border-radius: 8px;
                    font-size: 14px;
                    background-color: #ffffff;
                    color: #333;
                    outline: none;
                    transition: border-color 0.3s ease;
                }

                .filter-select-dynamic:focus {
                    outline: none;
                    border-color: #8fb728;
                    box-shadow: 0 0 0 2px rgba(174, 223, 50, 0.2);
                }

                .filter-actions-dynamic {
                    display: flex;
                    gap: 10px;
                    margin-top: 18px;
                }

                .filter-apply-btn-dynamic, .filter-clear-btn-dynamic {
                    flex: 1;
                    padding: 10px 14px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }

                .filter-apply-btn-dynamic {
                    background: #aedf32;
                    color: #ffffff;
                    border: 2px solid #8fb728;
                }

                .filter-apply-btn-dynamic:hover {
                    background: #8fb728;
                    transform: translateY(-1px);
                }

                .filter-clear-btn-dynamic {
                    background: #ffffff;
                    color: #8fb728;
                    border: 2px solid #8fb728;
                }

                .filter-clear-btn-dynamic:hover {
                    background: #8fb728;
                    color: #ffffff;
                    transform: translateY(-1px);
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Populate herd numbers dynamically
     */
    populateHerdNumbers() {
        const herdSelect = this.dropdownContainer.querySelector('#herdNumberFilterDynamic');
        if (!herdSelect) return;

        const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
        const herdNumbers = [...new Set(dairyAnimals.map(animal => animal.herdNumber).filter(Boolean))];

        // Clear existing options except "All Herds"
        herdSelect.innerHTML = '<option value="all">All Herds</option>';

        // Add unique herd numbers
        herdNumbers.forEach(herdNumber => {
            const option = document.createElement('option');
            option.value = herdNumber;
            option.textContent = herdNumber;
            herdSelect.appendChild(option);
        });
    }

    /**
     * Setup event listeners for filter dropdown
     */
    setupFilterDropdownEventListeners() {
        if (!this.dropdownContainer) return;

        // Apply filters button
        const applyBtn = this.dropdownContainer.querySelector('.filter-apply-btn-dynamic');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.applyFilters();
                this.hideDropdown();
            });
        }

        // Clear filters button
        const clearBtn = this.dropdownContainer.querySelector('.filter-clear-btn-dynamic');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearFilters();
                this.hideDropdown();
            });
        }

        // Click outside to close
        document.addEventListener('click', this.handleFilterOutsideClick.bind(this));
    }

    /**
     * Set current filter values in dropdown
     */
    setCurrentFilterValues() {
        if (!this.dropdownContainer) return;

        const selects = this.dropdownContainer.querySelectorAll('.filter-select-dynamic');
        selects.forEach(select => {
            const field = select.getAttribute('data-field');
            if (this.currentFilters[field]) {
                select.value = this.currentFilters[field];
            }
        });
    }

    /**
     * Apply filters
     */
    applyFilters() {
        const selects = this.dropdownContainer.querySelectorAll('.filter-select-dynamic');
        const filters = {};

        selects.forEach(select => {
            const field = select.getAttribute('data-field');
            const value = select.value;
            if (value !== 'all') {
                filters[field] = value;
            }
        });

        this.currentFilters = filters;
        this.filterDairyAnimals(filters);

        const filterCount = Object.keys(filters).length;
        console.log(`🔽 Applied ${filterCount} dairy filter(s):`, filters);
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        this.currentFilters = {};
        window.loadDairyAnimals(); // Reload all animals
        console.log('🔄 Cleared all dairy filters');
    }

    /**
     * Filter dairy animals based on criteria
     * @param {Object} filters - Filter criteria
     */
    filterDairyAnimals(filters) {
        const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');

        let filteredAnimals = dairyAnimals.filter(animal => {
            // Type filter
            if (filters.type && animal.type !== filters.type) {
                return false;
            }

            // Herd number filter
            if (filters.herdNumber && animal.herdNumber !== filters.herdNumber) {
                return false;
            }

            // Milk production filter
            if (filters.milkProduction) {
                const production = parseFloat(animal.lastMilkProduction || 0);
                switch (filters.milkProduction) {
                    case 'high':
                        if (production <= 30) return false;
                        break;
                    case 'medium':
                        if (production < 20 || production > 30) return false;
                        break;
                    case 'low':
                        if (production >= 20) return false;
                        break;
                }
            }

            // AI status filter
            if (filters.artificialInseminationStatus &&
                animal.artificialInseminationStatus !== filters.artificialInseminationStatus) {
                return false;
            }

            return true;
        });

        this.displayFilteredResults(filteredAnimals, filters);
    }

    /**
     * Display filtered results in the table
     * @param {Array} animals - Filtered animals
     * @param {Object} filters - Applied filters
     */
    displayFilteredResults(animals, filters) {
        const tableBody = document.querySelector('tbody');
        tableBody.innerHTML = '';

        if (animals.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="11" style="text-align: center;">
                    No dairy animals match the selected filters
                </td>
            `;
            tableBody.appendChild(row);
            return;
        }

        // Add each filtered animal to the table
        animals.forEach((animal, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input class="checkbox" type="checkbox" data-index="${index}">${animal.code}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${animal.type === 'milch' ? 'Drying' : (animal.type === 'drying' ? 'Drying' : (animal.type === 'dairy' ? 'Dairy' : (animal.type || 'N/A')))}</td>
                <td>${animal.herdNumber || 'N/A'}</td>
                <td>${animal.lastMilkProduction ? animal.lastMilkProduction + 'L' : 'N/A'}</td>
                <td>${animal.fatPercentage ? animal.fatPercentage + '%' : 'N/A'}</td>
                <td>${animal.weight || 'N/A'}</td>
                <td>${this.formatDate(animal.weightDate) || 'N/A'}</td>
                <td>${animal.artificialInseminationStatus || 'N/A'}</td>
                <td>${this.formatDate(animal.artificialInseminationDate) || 'N/A'}</td>
                <td>${this.formatDate(animal.expectedCalfingDate) || 'N/A'}</td>
                <td>${animal.healthcareNotes || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });

        console.log(`📊 Dairy filter results: ${animals.length} animals found`);
    }

    /**
     * Format date for display
     * @param {string} dateString - Date string
     * @returns {string} - Formatted date
     */
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    /**
     * Handle click outside filter dropdown
     * @param {Event} e - Click event
     */
    handleFilterOutsideClick(e) {
        if (this.isVisible && this.dropdownContainer && !this.dropdownContainer.contains(e.target)) {
            this.hideDropdown();
        }
    }

    /**
     * Show dropdown with animation
     */
    showDropdownWithAnimation() {
        if (this.dropdownContainer) {
            this.isVisible = true;
            setTimeout(() => {
                this.dropdownContainer.style.opacity = '1';
                this.dropdownContainer.style.transform = 'translateY(0)';
            }, 10);
        }
    }

    /**
     * Hide dropdown
     */
    hideDropdown() {
        if (this.dropdownContainer && this.isVisible) {
            this.dropdownContainer.style.opacity = '0';
            this.dropdownContainer.style.transform = 'translateY(-10px)';

            // Remove active class from filter container
            const filterContainer = document.querySelector('.filter-dropdown-container.active');
            if (filterContainer) {
                filterContainer.classList.remove('active');
            }

            setTimeout(() => {
                if (this.dropdownContainer && this.dropdownContainer.parentNode) {
                    this.dropdownContainer.parentNode.removeChild(this.dropdownContainer);
                    this.dropdownContainer = null;
                }
            }, 300);

            this.isVisible = false;

            // Remove event listener
            document.removeEventListener('click', this.handleFilterOutsideClick.bind(this));
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');
     // Initialize popup manager
    const popupManager = new DairyPopupManager();
    window.dairyPopupManager = popupManager;

    // Initialize search dropdown manager
    const searchDropdownManager = new DairySearchDropdownManager();

    // Initialize filter dropdown manager
    const filterDropdownManager = new DairyFilterDropdownManager();

    // Initialize popup components
    if (typeof PopupComponents !== 'undefined' && PopupComponents.init) {
        PopupComponents.init();
    }

    // Get elements
    const recordMilkBtn = document.querySelector('.frame-9:nth-child(1)');
    const addNewAnimalBtn = document.querySelector('.frame-9:nth-child(2)');
    const updateDataBtn = document.querySelector('.frame-9:nth-child(3)');
    const searchBtn = document.querySelector('.search');
    const filterBtn = document.querySelector('#filterButton');
    const deleteAnimalBtn = document.querySelector('#deleteAnimalBtn');
    const saveDataBtn = document.querySelector('#saveDataBtn');
    const tableBody = document.querySelector('tbody');

    // Debug - log if elements are found
    console.log('Elements found:', {
        recordMilkBtn: !!recordMilkBtn,
        addNewAnimalBtn: !!addNewAnimalBtn,
        updateDataBtn: !!updateDataBtn,
        searchBtn: !!searchBtn,
        filterBtn: !!filterBtn,
        deleteAnimalBtn: !!deleteAnimalBtn,
        saveDataBtn: !!saveDataBtn,
        tableBody: !!tableBody
    });

    // Define loadDairyAnimals function first, then call it
    // (function definition moved below)

    // Function to load dairy animals from localStorage
    window.loadDairyAnimals = function() {
        console.log('Loading dairy animals table...');

        // Clear existing table rows
        tableBody.innerHTML = '';

        // Get dairy animals from localStorage
        const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
        console.log('Dairy animals loaded:', dairyAnimals.length);

        if (dairyAnimals.length === 0) {
            // If no animals, display a message
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="11" style="text-align: center;">No dairy animals found</td>
            `;
            tableBody.appendChild(row);
            return;
        }

        // Add each dairy animal to the table
        dairyAnimals.forEach((animal, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input class="checkbox" type="checkbox" data-index="${index}">${animal.code}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${animal.type === 'milch' ? 'dairy' : (animal.type || 'N/A')}</td>
                <td>${animal.herdNumber || 'N/A'}</td>
                <td>${animal.lastMilkProduction ? animal.lastMilkProduction + 'L' : 'N/A'}</td>
                <td>${animal.fatPercentage ? animal.fatPercentage + '%' : 'N/A'}</td>
                <td>${animal.weight || 'N/A'}</td>
                <td>${formatDate(animal.weightDate) || 'N/A'}</td>
                <td>${animal.artificialInseminationStatus || 'N/A'}</td>
                <td>${formatDate(animal.artificialInseminationDate) || 'N/A'}</td>
                <td>${formatDate(animal.expectedCalfingDate) || 'N/A'}</td>
                <td>${animal.healthcareNotes || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });

        // Add event listeners to checkboxes
        const checkboxes = document.querySelectorAll('.checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleCheckboxChange);
        });
    }

    // Now call the function to load the dairy animals
    window.loadDairyAnimals();

    // Format date for display
    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    // Handle checkbox selection
    function handleCheckboxChange(e) {
        const checkboxes = document.querySelectorAll('.checkbox');
        const selectedCount = document.querySelectorAll('.checkbox:checked').length;

        // Enable or disable delete button based on selection
        if (selectedCount > 0) {
            deleteAnimalBtn.disabled = false;
            deleteAnimalBtn.style.opacity = 1;
        } else {
            deleteAnimalBtn.disabled = true;
            deleteAnimalBtn.style.opacity = 0.5;
        }
    }

       // Add event listeners to buttons
    recordMilkBtn.addEventListener('click', function() {
        // Show the record milk production popup
        popupManager.showPopup('recordmilk');
    });

    addNewAnimalBtn.addEventListener('click', function() {
        // Show the add new dairy animal popup
        popupManager.showPopup('addnewmilch');
    });

    updateDataBtn.addEventListener('click', function() {
        // Show the choose update dairy popup
        popupManager.showPopup('updatemilchdata');
    });

    searchBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('🔍 Search button clicked - showing dropdown');
        searchDropdownManager.showDropdown(searchBtn);
    });

    filterBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('🔽 Filter button clicked - showing dropdown');
        filterDropdownManager.showDropdown(filterBtn);
    });

    // Add event listener for delete button with debounce to prevent multiple clicks
    let deleteTimeout = null;
    deleteAnimalBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        // Prevent multiple rapid clicks
        if (deleteTimeout) {
            clearTimeout(deleteTimeout);
        }

        // Disable the button temporarily to prevent multiple clicks
        deleteAnimalBtn.disabled = true;

        console.log('Delete button clicked');
        deleteSelectedAnimals();

        // Re-enable the button after a short delay
        deleteTimeout = setTimeout(() => {
            deleteAnimalBtn.disabled = false;
        }, 1000);
    });

    saveDataBtn.addEventListener('click', function() {
        saveDairyData();
    });

    // Function to search dairy animals
    function searchDairyAnimals(searchTerm) {
        const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
        const filteredAnimals = dairyAnimals.filter(animal =>
            animal.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
            animal.herdNumber.toLowerCase().includes(searchTerm.toLowerCase())
        );

        // Clear existing table rows
        tableBody.innerHTML = '';

        if (filteredAnimals.length === 0) {
            // If no matching animals, display a message
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="11" style="text-align: center;">No matching dairy animals found</td>
            `;
            tableBody.appendChild(row);
            return;
        }

        // Add each filtered animal to the table
        filteredAnimals.forEach((animal, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input class="checkbox" type="checkbox" data-index="${index}">${animal.code}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${animal.type === 'milch' ? 'Drying' : (animal.type === 'drying' ? 'Drying' : (animal.type === 'dairy' ? 'Dairy' : (animal.type || 'N/A')))}</td>
                <td>${animal.herdNumber || 'N/A'}</td>
                <td>${animal.lastMilkProduction ? animal.lastMilkProduction + 'L' : 'N/A'}</td>
                <td>${animal.fatPercentage ? animal.fatPercentage + '%' : 'N/A'}</td>
                <td>${animal.weight || 'N/A'}</td>
                <td>${formatDate(animal.weightDate) || 'N/A'}</td>
                <td>${animal.artificialInseminationStatus || 'N/A'}</td>
                <td>${formatDate(animal.artificialInseminationDate) || 'N/A'}</td>
                <td>${formatDate(animal.expectedCalfingDate) || 'N/A'}</td>
                <td>${animal.healthcareNotes || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });
    }

    // Function to show filter options
    function showFilterOptions() {
        const filterOptions = [
            'Dairy Type',
            'Herd Number',
            'Milk Production (High to Low)',
            'Milk Production (Low to High)',
            'Fat Percentage (High to Low)',
            'Weight (High to Low)'
        ];

        let optionsHTML = 'Select filter option:<br>';
        filterOptions.forEach((option, index) => {
            optionsHTML += `<button onclick="applyFilter(${index})">${option}</button><br>`;
        });

        // Create a simple modal for filter options
        const modal = document.createElement('div');
        modal.style.position = 'fixed';
        modal.style.top = '50%';
        modal.style.left = '50%';
        modal.style.transform = 'translate(-50%, -50%)';
        modal.style.backgroundColor = 'white';
        modal.style.padding = '20px';
        modal.style.borderRadius = '10px';
        modal.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';
        modal.style.zIndex = '1000';
        modal.innerHTML = optionsHTML;

        // Add close button
        const closeBtn = document.createElement('button');
        closeBtn.textContent = 'Close';
        closeBtn.style.marginTop = '10px';
        closeBtn.addEventListener('click', () => document.body.removeChild(modal));
        modal.appendChild(closeBtn);

        document.body.appendChild(modal);

        // Add filter function to window object to make it accessible from the modal
        window.applyFilter = function(filterIndex) {
            filterDairyAnimals(filterIndex);
            document.body.removeChild(modal);
        };
    }

    // Function to filter dairy animals
    function filterDairyAnimals(filterIndex) {
        const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
        let filteredAnimals = [...dairyAnimals];

        switch(filterIndex) {
            case 0: // Dairy Type
                filteredAnimals.sort((a, b) => a.type.localeCompare(b.type));
                break;
            case 1: // Herd Number
                filteredAnimals.sort((a, b) => a.herdNumber.localeCompare(b.herdNumber));
                break;
            case 2: // Milk Production (High to Low)
                filteredAnimals.sort((a, b) => parseFloat(b.lastMilkProduction || 0) - parseFloat(a.lastMilkProduction || 0));
                break;
            case 3: // Milk Production (Low to High)
                filteredAnimals.sort((a, b) => parseFloat(a.lastMilkProduction || 0) - parseFloat(b.lastMilkProduction || 0));
                break;
            case 4: // Fat Percentage (High to Low)
                filteredAnimals.sort((a, b) => parseFloat(b.fatPercentage || 0) - parseFloat(a.fatPercentage || 0));
                break;
            case 5: // Weight (High to Low)
                filteredAnimals.sort((a, b) => parseFloat(b.weight || 0) - parseFloat(a.weight || 0));
                break;
        }

        // Clear existing table rows
        tableBody.innerHTML = '';

        // Add each filtered animal to the table
        filteredAnimals.forEach((animal, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input class="checkbox" type="checkbox" data-index="${index}">${animal.code}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${animal.type === 'milch' ? 'Drying' : (animal.type === 'drying' ? 'Drying' : (animal.type === 'dairy' ? 'Dairy' : (animal.type || 'N/A')))}</td>
                <td>${animal.herdNumber || 'N/A'}</td>
                <td>${animal.lastMilkProduction ? animal.lastMilkProduction + 'L' : 'N/A'}</td>
                <td>${animal.fatPercentage ? animal.fatPercentage + '%' : 'N/A'}</td>
                <td>${animal.weight || 'N/A'}</td>
                <td>${formatDate(animal.weightDate) || 'N/A'}</td>
                <td>${animal.artificialInseminationStatus || 'N/A'}</td>
                <td>${formatDate(animal.artificialInseminationDate) || 'N/A'}</td>
                <td>${formatDate(animal.expectedCalfingDate) || 'N/A'}</td>
                <td>${animal.healthcareNotes || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });
    }

    // Function to delete selected animals
    function deleteSelectedAnimals() {
        console.log('Delete function called');
        const selectedCheckboxes = document.querySelectorAll('.checkbox:checked');

        if (selectedCheckboxes.length === 0) {
            alert('Please select at least one animal to delete');
            return;
        }

        // Use a single confirm dialog
        const confirmDelete = confirm(`Are you sure you want to delete ${selectedCheckboxes.length} dairy animal(s)?`);

        if (confirmDelete) {
            console.log('Delete confirmed');
            try {
                const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');

                // Get the codes of selected animals
                const selectedCodes = Array.from(selectedCheckboxes).map(checkbox => {
                    return checkbox.parentElement.textContent.trim();
                });

                console.log('Selected codes:', selectedCodes);

                // Filter out the selected animals
                const updatedAnimals = dairyAnimals.filter(animal => !selectedCodes.includes(animal.code));

                // Update localStorage
                localStorage.setItem('dairyAnimals', JSON.stringify(updatedAnimals));

                // Show success message before reloading the table
                alert('Selected dairy animal(s) deleted successfully');

                // Reload the table
                loadDairyAnimals();
            } catch (error) {
                console.error('Error deleting animals:', error);
                alert('An error occurred while deleting animals. Please try again.');
            }
        }
    }

    // Function to save dairy data
    function saveDairyData() {
        const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');

        if (dairyAnimals.length === 0) {
            alert('No dairy data to save');
            return;
        }

        // Create a CSV string from the dairy animals data
        let csvContent = 'Code,Type,Herd Number,Milk Production,Fat Percentage,Weight,Date of Weight,Artificial Insemination Status,Artificial Insemination Date,Expected Calfing Date,Healthcare\n';

        dairyAnimals.forEach(animal => {
            csvContent += `${animal.code || ''},${animal.type || ''},${animal.herdNumber || ''},${animal.lastMilkProduction || ''},${animal.fatPercentage || ''},${animal.weight || ''},${animal.weightDate || ''},${animal.artificialInseminationStatus || ''},${animal.artificialInseminationDate || ''},${animal.expectedCalfingDate || ''},${animal.healthcareNotes || ''}\n`;
        });

        // Create a Blob with the CSV data
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

        // Create a download link and trigger the download
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', 'dairy_data.csv');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        alert('Dairy data saved successfully');
    }

    // Add event listeners for milk production popup buttons
    document.addEventListener('click', function(e) {
        // Check if the milk production popup is active
        const recordMilkPopup = document.getElementById('recordMilkPopup');
        if (recordMilkPopup && recordMilkPopup.classList.contains('active')) {
            // Handle save button click
            if (e.target.id === 'saveRecordMilkBtn' || e.target.closest('#saveRecordMilkBtn')) {
                console.log('Save milk production button clicked');
                PopupComponents.recordMilkProduction.save();
            }

            // Handle cancel button click
            if (e.target.id === 'cancelRecordMilkBtn' || e.target.closest('#cancelRecordMilkBtn')) {
                console.log('Cancel milk production button clicked');
                PopupComponents.hidePopup('recordMilkPopup');
            }

            // Handle close button click
            if (e.target.id === 'closeRecordMilkPopupBtn' || e.target.closest('#closeRecordMilkPopupBtn')) {
                console.log('Close milk production button clicked');
                PopupComponents.hidePopup('recordMilkPopup');
            }
        }

        // Check if the add animal popup is active
        const addAnimalPopup = document.getElementById('addAnimalPopup');
        if (addAnimalPopup && addAnimalPopup.classList.contains('active')) {
            // We're handling the save button directly in the HTML with onclick
            // Only handle back and close buttons here

            // Handle back button click
            if (e.target.id === 'backBtn' || e.target.closest('#backBtn')) {
                console.log('Back button clicked from global handler');
                PopupComponents.hidePopup('addAnimalPopup');
            }

            // Handle close button click
            if (e.target.id === 'closePopupBtn' || e.target.closest('#closePopupBtn')) {
                console.log('Close button clicked from global handler');
                PopupComponents.hidePopup('addAnimalPopup');
            }
        }

        // Check if the choose update dairy popup is active
        const chooseUpdateDairyPopup = document.getElementById('chooseUpdateDairyPopup');
        if (chooseUpdateDairyPopup && chooseUpdateDairyPopup.classList.contains('active')) {
            // Handle go to update button click
            if (e.target.id === 'goToUpdateDairyBtn' || e.target.closest('#goToUpdateDairyBtn')) {
                console.log('Go to Update button clicked from global handler');
                PopupComponents.chooseUpdateDairy.goToUpdate();
            }

            // Handle cancel button click
            if (e.target.id === 'cancelChooseUpdateDairyBtn' || e.target.closest('#cancelChooseUpdateDairyBtn')) {
                console.log('Cancel button clicked from global handler');
                PopupComponents.hidePopup('chooseUpdateDairyPopup');
            }

            // Handle close button click
            if (e.target.id === 'closeChooseUpdateDairyPopupBtn' || e.target.closest('#closeChooseUpdateDairyPopupBtn')) {
                console.log('Close button clicked from global handler');
                PopupComponents.hidePopup('chooseUpdateDairyPopup');
            }
        }

        // Check if the update dairy data popup is active
        const updateDairyDataPopup = document.getElementById('updateDairyDataPopup');
        if (updateDairyDataPopup && updateDairyDataPopup.classList.contains('active')) {
            // We're handling all buttons directly in the component now
            // No need to handle any buttons here
            console.log('updateDairyDataPopup is active - buttons handled by component');
        }
    });

    // Initialize the page
    deleteAnimalBtn.disabled = true;
    deleteAnimalBtn.style.opacity = 0.5;

    // Add event listener to enable/disable delete button based on checkbox selection
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('checkbox')) {
            const selectedCheckboxes = document.querySelectorAll('.checkbox:checked');
            if (selectedCheckboxes.length > 0) {
                deleteAnimalBtn.disabled = false;
                deleteAnimalBtn.style.opacity = 1;
            } else {
                deleteAnimalBtn.disabled = true;
                deleteAnimalBtn.style.opacity = 0.5;
            }
        }
    });

    // Make dropdown managers globally accessible
    window.dairySearchDropdownManager = searchDropdownManager;
    window.dairyFilterDropdownManager = filterDropdownManager;
});

// ==================== GLOBAL FUNCTIONS FOR HTML ONCLICK HANDLERS ====================

/**
 * Global function for dairy search (called from HTML onclick)
 * @param {string} searchType - Type of search
 */
window.performDairySearch = function(searchType) {
    console.log(`🔍 performDairySearch called with type: ${searchType}`);

    if (window.dairySearchDropdownManager) {
        window.dairySearchDropdownManager.performSearch(searchType);
    } else {
        console.error('❌ Dairy Search Dropdown Manager not available');
    }
};

/**
 * Global function to cancel dairy search (called from HTML onclick)
 */
window.cancelDairySearch = function() {
    console.log('🔄 cancelDairySearch called');

    if (window.dairySearchDropdownManager) {
        window.dairySearchDropdownManager.cancelSearch();
    } else {
        console.error('❌ Dairy Search Dropdown Manager not available');
    }
};

/**
 * Global function to apply dairy filters (called from HTML onclick)
 */
window.applyDairyFilters = function() {
    console.log('🔽 applyDairyFilters called');

    if (window.dairyFilterDropdownManager) {
        window.dairyFilterDropdownManager.applyFilters();
        window.dairyFilterDropdownManager.hideDropdown();
    } else {
        console.error('❌ Dairy Filter Dropdown Manager not available');
    }
};

/**
 * Global function to clear dairy filters (called from HTML onclick)
 */
window.clearDairyFilters = function() {
    console.log('🔄 clearDairyFilters called');

    if (window.dairyFilterDropdownManager) {
        window.dairyFilterDropdownManager.clearFilters();
        window.dairyFilterDropdownManager.hideDropdown();
    } else {
        console.error('❌ Dairy Filter Dropdown Manager not available');
    }
};