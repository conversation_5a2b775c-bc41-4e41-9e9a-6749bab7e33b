@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0%;
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}

.choose-update-milch {

    background-color: #f1f1f1;
    display: flex;
    flex-direction: row;
    justify-content: center;
    /* padding: 2%; */
    width: 100%;
    height: 100%;
}










.choose-update-milch .frame-4 {
    position: absolute;
    width: 40%;
    height: 60%;
    top:21%;
    left: 24%;
    background-color: #ffffff;
    border-radius: 16px;
    display: flex;
    direction: column;

    align-items: flex-start;
}

.choose-update-milch .frame-5 {
    display: flex;
    width: 10%;
    height: 15%;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    padding: 10px;
    position: relative;
    top: 5px;
    left: 1%;
    border-radius: 20px;
}

 .update {
    position: relative;
    width: 35px;
    height: 35px;

}


 .text-wrapper-5 {



    font-weight:bold;
    color: #0b291a;
    font-size: 20px;
    letter-spacing: var(--highlights-letter-spacing);
    line-height: var(--highlights-line-height);
    white-space: nowrap;
    font-style: var(--highlights-font-style);
}

.choose-update-milch .frame-6 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 85%;
    align-items: center;
    position: absolute;
    top: 15%;
}

 .frame-7 {
    display: flex;
    align-items: center;

    padding: 0px 10px;
    position: relative;
    width: 100%;
    height: 50%;

}


 .frame-71 {
    display: flex;
    align-items: center;
    justify-content: center;

    padding: 0px 10px;
    height: 55%;
    width: 70%;
    font-size: 20px;
    background-color: #f1f0f0;
   border-radius: 12px;
}


.choose-update-milch .text-wrapper-6 {
    position: relative;
    width: 20%;

    font-family: "Roboto-Medium", Helvetica;

    color: #000000;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 18px;
    white-space: nowrap;
    left: 10%;

}

.choose-update-milch .data-filled {
    position:absolute;
    width: 25%;
    height: 40px;
    background-color:#f1f1f1;
    border-radius: 12px;
   border: hidden;
    left: 60%;

}


.choose-update-milch .frame-8 {
    display: flex;
    align-items: center;

    padding: 0px 10px;
    position: relative;
    width: 100%;
    height: 50%;
}


.choose-update-milch .frame-9 {
    position: absolute;
   left: 40%;
    display: flex;
   width: 45%;
    height: 50px;
    align-items: center;
    justify-content: center;
   border: hidden;

    background-color: #aedf32;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.frame-9:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.frame-9:not(:disabled):hover {
    background-color: #99c522;
    transform: translateY(-1px);
}

.frame-9:not(:disabled):active {
    transform: translateY(1px);
}

.btntext{
    font-size: 18px;
    font-weight: 40px;
    color: #ffffff;
}

.dropdown-container {
    display: flex;
    align-items: center;
    position: relative;
    width: 100%;
    position: relative;
    width: 60%;
    margin-left: 2%;
}

.dropbtn {
    width: 100%;
    height: 50px;
    background-color: #f1f1f1;
    border-radius: 12px;
    border: none;
    padding: 0 15px;
    text-align: left;
    cursor: pointer;
    font-size: 16px;
    color: #333;
}

.dropdown-content {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background-color:#aedf32;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 250px;
    overflow-y: auto;
}

.dropdown-content a {
    color: #fff;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
    cursor: pointer;
    transition: background-color 0.2s;
}

.dropdown-content a:hover {
    background-color: #99c522;
    border-radius: 8px;
}

/* Show the dropdown when active */
.dropdown-content.show {
    display: block;
}

.custom-select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    background-color: #aedf32;
    color: white;
    padding: 8px 30px 8px 10px;
    border: none;
    border-radius: 12px;
    width: 200px;
    cursor: pointer;
    font-size: 16px;
    position: absolute;
    left: 60%;
}

.custom-select:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(174, 223, 50, 0.3);
}

.custom-select option {
    background-color: #aedf32;
    color: white;
    padding: 10px;
}

.custom-select option:hover,
.custom-select option:focus {
    background-color: #739419;
}

.text-wrapper-6 {
    cursor: pointer;
}

.text-wrapper-6:hover {
    opacity: 0.9;
}

.dropdown-container::after {
    content: '';
    position: absolute;
    left: calc(60% + 170px);
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 6px solid white;
    pointer-events: none;
    transition: transform 0.2s ease;
}

.custom-select:focus + .dropdown-container::after {
    transform: translateY(-50%) rotate(180deg);
}

#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.notification {
    padding: 12px 24px;
    margin-bottom: 10px;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    opacity: 0;
    transform: translateX(100%);
    animation: slideIn 0.5s forwards;
}

.notification.success {
    background-color:#aedf32 ;
}

.notification.error {
    background-color: #e7aeae;
    border: 1px solid red;
}
 
@keyframes slideIn {
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeOut {
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

