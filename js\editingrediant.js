document.addEventListener('DOMContentLoaded', () => {
    // Get form elements by ID
    const nameInput = document.getElementById('ingredientName');
    const proteinInput = document.getElementById('ingredientProteins');
    const cfInput = document.getElementById('ingredientCF');
    const tdnInput = document.getElementById('ingredientTDN');
    const meInput = document.getElementById('ingredientME');
    const typeBtn = document.getElementById('ingredientTypeBtn');
    const dropContent = document.getElementById('ingredientTypeDropdown');
    const saveButton = document.querySelector('.frame-9');

    // Initialize dropdown functionality
    typeBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        dropContent.classList.toggle('show');
    });

    dropContent.querySelectorAll('a').forEach(option => {
        option.addEventListener('click', (e) => {
            e.preventDefault();
            typeBtn.textContent = option.textContent;
            dropContent.classList.remove('show');
        });
    });

    document.addEventListener('click', (e) => {
        if (!e.target.closest('.dropdown-container')) {
            dropContent.classList.remove('show');
        }
    });

    // Get ingredient data from URL
    const urlParams = new URLSearchParams(window.location.search);
    const ingredientName = urlParams.get('name');

    if (!ingredientName) {
        showNotification('No ingredient specified for editing', 'error');
        setTimeout(() => window.location.href = 'ingrediants.html', 2000);
        return;
    }

    // Load and populate ingredient data
    const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
    const ingredient = ingredients.find(i => i.name === ingredientName);

    if (!ingredient) {
        showNotification('Ingredient not found', 'error');
        setTimeout(() => window.location.href = 'ingrediants.html', 2000);
        return;
    }

    // Populate form fields
    nameInput.value = ingredient.name;
    typeBtn.textContent = ingredient.type;
    proteinInput.value = ingredient.proteins;
    cfInput.value = ingredient.crudeFiber;
    tdnInput.value = ingredient.tdn;
    meInput.value = ingredient.me;

    // Handle save functionality
    saveButton.addEventListener('click', async () => {
        try {
            // Show loading state
            saveButton.disabled = true;
            saveButton.innerHTML = `
                <img class="update" src="icons/save.png" style="opacity: 0.7">
                <div class="btntext">Saving...</div>
            `;

            // Get form values
            const name = nameInput.value.trim();
            const type = typeBtn.textContent.trim();
            const proteins = proteinInput.value.trim();
            const crudeFiber = cfInput.value.trim();
            const tdn = tdnInput.value.trim();
            const me = meInput.value.trim();

            // Validate required fields
            if (!name || type === 'Select Ingredient Type' || !proteins || !crudeFiber || !tdn || !me) {
                showNotification('Please fill in all fields', 'error');
                return;
            }

            // Validate numeric inputs
            if (!validateNumericInput(proteins, 'proteins') ||
                !validateNumericInput(crudeFiber, 'crude fiber') ||
                !validateNumericInput(tdn, 'TDN') ||
                !validateNumericInput(me, 'ME')) {
                return;
            }

            // Check for duplicate name (excluding current ingredient)
            const nameExists = ingredients.some(i => i.name === name && i.name !== ingredientName);
            if (nameExists) {
                showNotification('An ingredient with this name already exists', 'error');
                nameInput.focus();
                return;
            }

            // Update ingredient in array
            const index = ingredients.findIndex(i => i.name === ingredientName);
            if (index !== -1) {
                ingredients[index] = {
                    ...ingredient,
                    name: name,
                    type: type,
                    proteins: parseFloat(proteins),
                    crudeFiber: parseFloat(crudeFiber),
                    tdn: parseFloat(tdn),
                    me: parseFloat(me),
                    lastUpdated: new Date().toISOString()
                };

                // Save to localStorage
                localStorage.setItem('ingredients', JSON.stringify(ingredients));

                // Store updated name for highlighting on return
                sessionStorage.setItem('recentlyUpdatedIngredient', name);

                showNotification('Ingredient updated successfully', 'success');
                
                // Redirect after delay
                setTimeout(() => window.location.href = 'ingrediants.html', 2000);
            }
        } catch (error) {
            console.error('Error updating ingredient:', error);
            showNotification('Failed to update ingredient', 'error');
        } finally {
            // Reset button state
            saveButton.disabled = false;
            saveButton.innerHTML = `
                <img class="update" src="icons/save.png">
                <div class="btntext">Save Edit</div>
            `;
        }
    });
});

// Notification function for user feedback
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    const container = document.getElementById('notification-container');
    container.appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'fadeOut 0.5s forwards';
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 3000);
}

// Function to validate numeric input
function validateNumericInput(value, fieldName) {
    const num = parseFloat(value);
    if (isNaN(num) || num < 0) {
        showNotification(`Please enter a valid positive number for ${fieldName}`, 'error');
        return false;
    }
    return true;
}

document.addEventListener('DOMContentLoaded', function() {
    const dropBtn = document.getElementById('ingredientTypeBtn');
    const dropContent = document.getElementById('ingredientTypeDropdown');
    let selectedValue = '';

    // Toggle dropdown on button click
    dropBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        dropContent.classList.toggle('show');
    });

    // Handle option selection
    dropContent.querySelectorAll('a').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            selectedValue = this.getAttribute('data-value');
            dropBtn.textContent = this.textContent;
            dropContent.classList.remove('show');
            // Dispatch change event to notify of selection
            const event = new CustomEvent('valueChanged', { detail: selectedValue });
            dropBtn.dispatchEvent(event);
        });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-container')) {
            dropContent.classList.remove('show');
        }
    });

    // Get the ingredient ID from URL if editing
    const params = new URLSearchParams(window.location.search);
    const ingredientId = params.get('id');
    
    if (ingredientId) {
        // Load ingredient data from localStorage
        const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
        const ingredient = ingredients.find(ing => ing.id === ingredientId);
        
        if (ingredient) {
            // Set the dropdown value
            dropBtn.textContent = ingredient.type;
            selectedValue = ingredient.type.toLowerCase().replace(/\s+/g, '');
        }
    }
});