// Show notification helper function
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.getElementById('notification-container').appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'fadeOut 0.5s forwards';
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 3000);
}

// Helper function to validate form data
function validateFormData() {
    // Get all required form elements
    const feedNameInput = document.querySelector('input[placeholder="feed name"]');
    const growthRateInput = document.querySelector('input[placeholder="Growth rate"]');
    const weightInput = document.querySelector('input[placeholder="Weight"]');    // Find the animal dropdown
    const animalFrame = Array.from(document.querySelectorAll('.frame-7')).find(frame => {
        const label = frame.querySelector('.text-wrapper-6');
        return label && label.textContent.includes('Animal');
    });
    const animalDropdown = animalFrame ? animalFrame.querySelector('.dropbtn') : null;
    const genderDropdown = findDropdownButton('Gender');
    const feedTypeDropdown = findDropdownButton('Feed Type');
    const seasonDropdown = findDropdownButton('Season');

    // Validate feed name
    if (!feedNameInput || !feedNameInput.value.trim()) {
        showNotification('Please enter a feed name', 'error');
        return false;
    }

    // Validate growth rate
    if (!growthRateInput || !growthRateInput.value.trim()) {
        showNotification('Please enter a growth rate', 'error');
        return false;
    }

    // Validate weight
    if (!weightInput || !weightInput.value.trim()) {
        showNotification('Please enter a weight', 'error');
        return false;
    }    // Validate dropdowns
    if (!animalDropdown || ['Select Animal Type', "Select Animal's Type"].includes(animalDropdown.textContent.trim())) {
        showNotification('Please select an animal type', 'error');
        return false;
    }

    if (!genderDropdown || genderDropdown.textContent === 'Select Gender') {
        showNotification('Please select a gender', 'error');
        return false;
    }

    if (!feedTypeDropdown || feedTypeDropdown.textContent === 'Select Feed Type') {
        showNotification('Please select a feed type', 'error');
        return false;
    }

    if (!seasonDropdown || seasonDropdown.textContent === 'Select Season') {
        showNotification('Please select a season', 'error');
        return false;
    }

    return true;
}

// Helper function to validate ingredients
function validateIngredients() {
    const ingredientRows = document.querySelectorAll('table tbody tr');
    
    // Check minimum ingredients requirement (now 3)
    if (ingredientRows.length < 3) {
        showNotification('Please add at least 3 ingredients to the feed', 'error');
        return false;
    }

    // Check that each ingredient has a price
    const invalidIngredients = Array.from(ingredientRows).filter(row => {
        const price = row.querySelector('td:nth-child(2) .data-filled').textContent;
        return !price || isNaN(parseFloat(price));
    });

    if (invalidIngredients.length > 0) {
        showNotification('All ingredients must have a valid price', 'error');
        return false;
    }

    return true;
}

// Helper function to collect and save ingredients data
function collectIngredientsData() {
    const ingredientRows = document.querySelectorAll('table tbody tr');
    const ingredients = Array.from(ingredientRows).map(row => ({
        name: row.querySelector('td:first-child .data-filled').textContent,
        pricePerKg: parseFloat(row.querySelector('td:nth-child(2) .data-filled').textContent)
    }));
    
    return ingredients;
}

// Helper function to load ingredients from localStorage
function loadIngredientsFromStorage() {
    return JSON.parse(localStorage.getItem('ingredients') || '[]');
}

// Helper function to close all dropdowns
function closeAllDropdowns() {
    document.querySelectorAll('.dropdown-content').forEach(dropdown => {
        dropdown.style.display = 'none';
    });
}

// Dropdown functionality
function toggleDropdown(dropdownId) {
    console.log('Toggling dropdown:', dropdownId); // Debug log
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) {
        console.error('Dropdown not found:', dropdownId);
        return;
    }

    // Close all other dropdowns first
    const allDropdowns = document.getElementsByClassName('dropdown-content');
    Array.from(allDropdowns).forEach(d => {
        if (d.id !== dropdownId) {
            d.style.display = 'none';
        }
    });

    // Toggle current dropdown
    const currentDisplay = dropdown.style.display;
    dropdown.style.display = currentDisplay === 'block' ? 'none' : 'block';
    
    if (dropdown.style.display === 'block') {
        // Position the dropdown below its button
        const button = dropdown.previousElementSibling;
        if (button) {
            const rect = button.getBoundingClientRect();
            dropdown.style.position = 'absolute';
            dropdown.style.top = '100%';
            dropdown.style.left = '0';
            dropdown.style.width = '100%';
            dropdown.style.zIndex = '1000';
        }
    }
}

// Enhanced dropdown functionality
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    const dropdowns = document.getElementsByClassName("dropdown-content");
    const isVisible = dropdown.style.display === "block";
    
    // Close all dropdowns
    Array.from(dropdowns).forEach(d => {
        d.style.display = "none";
    });

    // Only show the clicked dropdown if it wasn't visible before
    if (!isVisible && dropdown) {
        dropdown.style.display = "block";
    }
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
    if (!event.target.matches('.dropbtn') && !event.target.closest('.dropdown-content')) {
        const dropdowns = document.getElementsByClassName("dropdown-content");
        Array.from(dropdowns).forEach(dropdown => {
            dropdown.style.display = "none";
        });
    }
});

// Close dropdowns with ESC key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const dropdowns = document.getElementsByClassName("dropdown-content");
        Array.from(dropdowns).forEach(dropdown => {
            dropdown.style.display = "none";
        });
    }
});

// Helper function to toggle specific dropdown
function toggleDropdown(dropdownContent) {
    const isVisible = dropdownContent.style.display === 'block';
    closeAllDropdowns();
    if (!isVisible) {
        dropdownContent.style.display = 'block';
    }
}

// Helper function to handle dropdown selection
function handleDropdownSelection(type, value) {
    // Find the button by its current text content
    const buttons = document.querySelectorAll('.dropbtn');
    buttons.forEach(btn => {
        if (btn.textContent.trim() === type) {
            btn.textContent = value;
        }
    });
    closeAllDropdowns();
}

// Selection handlers for dropdowns
function selectGender(gender) {
    handleDropdownSelection('Select Gender', gender);
}

function selectAnimalType(type) {
    // Find the dropdown within the frame containing "Animal"
    const animalFrame = Array.from(document.querySelectorAll('.frame-7')).find(frame => {
        const label = frame.querySelector('.text-wrapper-6');
        return label && label.textContent.includes('Animal');
    });

    if (animalFrame) {
        const dropdownBtn = animalFrame.querySelector('.dropbtn');
        if (dropdownBtn) {
            dropdownBtn.textContent = type;
        }
    }
    closeAllDropdowns();
}

function selectFeedType(type) {
    handleDropdownSelection('Select Feed Type', type);
}

function selectSeason(season) {
    handleDropdownSelection('Select Season', season);
}

function selectIngredientType(type) {
    console.log('Selecting ingredient type:', type);
    
    // Update the ingredient type button text
    const buttons = document.querySelectorAll('.dropbtn');
    buttons.forEach(btn => {
        if (btn.textContent.trim() === 'Select Ingredient Type') {
            btn.textContent = type;
        }
    });

    // Get ingredients from localStorage
    const allIngredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
    console.log('All ingredients:', allIngredients);

    // Normalize the selected type
    const normalizeType = (t) => {
        // Convert to lowercase and trim
        t = t.toLowerCase().trim();
        // Handle variations of "by product" and "byproduct"
        t = t.replace(/\s*by\s*product\s*$/i, 'byproduct');
        t = t.replace(/\s+/g, ''); // Remove remaining spaces
        return t;
    };

    // Filter ingredients by type
    const filteredIngredients = allIngredients.filter(ingredient => {
        if (!ingredient.type) return false;
        
        const ingType = normalizeType(ingredient.type);
        const selectedType = normalizeType(type);
        
        console.log(`Comparing: "${ingType}" with "${selectedType}"`); // Debug log
        return ingType === selectedType;
    });

    console.log('Filtered ingredients:', filteredIngredients);

    // Update ingredients dropdown
    const ingredientsList = document.getElementById('ingredientsList');
    const ingredientsDropdown = document.getElementById('ingredientsDropdown');

    if (!ingredientsList || !ingredientsDropdown) {
        console.error('Could not find ingredients dropdown elements');
        return;
    }

    // Reset the ingredients selection
    ingredientsDropdown.textContent = 'Select Ingredient';
    ingredientsList.innerHTML = '';

    // Add filtered ingredients to dropdown
    if (filteredIngredients.length === 0) {
        const noIngredients = document.createElement('a');
        noIngredients.href = '#';
        noIngredients.textContent = 'No ingredients found';
        noIngredients.style.color = '#999';
        noIngredients.onclick = e => e.preventDefault();
        ingredientsList.appendChild(noIngredients);
    } else {
        filteredIngredients.forEach(ingredient => {
            const option = document.createElement('a');
            option.href = '#';
            option.textContent = ingredient.name;
            option.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                // Update dropdown text
                ingredientsDropdown.textContent = ingredient.name;
                
                // Auto-fill price if available
                const priceInput = document.querySelector('input[placeholder="price per kg"]');
                if (priceInput && ingredient.price) {
                    priceInput.value = ingredient.price;
                }
                
                // Store selected ingredient data
                window.selectedIngredient = ingredient;
                
                // Close dropdown
                ingredientsList.style.display = 'none';
            };
            ingredientsList.appendChild(option);
        });
    }

    // Close all dropdowns
    document.querySelectorAll('.dropdown-content').forEach(dropdown => {
        dropdown.style.display = 'none';
    });
}

// Function to update ingredients dropdown based on selected type
function selectIngredientType(type) {
    console.log('Selected type:', type);
    
    // Get ingredients from localStorage (populated from ingredients page)
    const allIngredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
    console.log('All ingredients:', allIngredients);

    // Filter ingredients by selected type
    const filteredIngredients = allIngredients.filter(ing => {
        const ingType = (ing.type || '').toLowerCase().trim();
        const selectedType = type.toLowerCase().trim();
        
        // Handle 'by Product' case
        const normalizedIngType = ingType.replace(/\s+by\s+product$/i, 'byproduct');
        const normalizedSelectedType = selectedType.replace(/\s+by\s+product$/i, 'byproduct');
        
        return normalizedIngType === normalizedSelectedType;
    });

    // Update ingredients dropdown
    const ingredientsList = document.getElementById('ingredientsList');
    if (!ingredientsList) {
        console.error('Could not find ingredientsList element');
        return;
    }

    // Clear existing options
    ingredientsList.innerHTML = '';

    if (filteredIngredients.length === 0) {
        const noIngredientsOption = document.createElement('a');
        noIngredientsOption.href = '#';
        noIngredientsOption.textContent = 'No ingredients found';
        noIngredientsOption.style.color = '#999';
        ingredientsList.appendChild(noIngredientsOption);
    } else {
        filteredIngredients.forEach(ing => {
            const option = document.createElement('a');
            option.href = '#';
            option.textContent = ing.name;
            option.onclick = (e) => {
                e.preventDefault();
                document.getElementById('ingredientsDropdown').textContent = ing.name;
                // Auto-fill price if available
                const priceInput = document.querySelector('input[placeholder="price per kg"]');
                if (priceInput) {
                    priceInput.value = ing.price || '';
                }
                closeAllDropdowns();
            };
            ingredientsList.appendChild(option);
        });
    }

    // Update type button text and close dropdowns
    const typeBtn = document.querySelector('.dropbtn:contains("Select Ingredient Type")');
    if (typeBtn) {
        typeBtn.textContent = type;
    }
    closeAllDropdowns();
}

// Helper function to update dropdown text
function updateDropdownText(placeholder, newText) {
    const dropbtns = document.querySelectorAll('.dropbtn');
    dropbtns.forEach(btn => {
        if (btn.textContent.includes(placeholder)) {
            btn.textContent = newText;
        }
    });
}

// Enhanced ingredient filtering and selection
function updateIngredientsBasedOnType(type) {
    const allIngredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
    
    const filteredIngredients = allIngredients.filter(ingredient => {
        const ingredientType = ingredient.type ? ingredient.type.toLowerCase().trim() : '';
        const selectedType = type.toLowerCase().trim();
        
        // Normalize types for comparison
        const normalizedIngType = ingredientType.replace(/[\s-]+/g, '');
        const normalizedSelType = selectedType.replace(/[\s-]+/g, '');
        
        return normalizedIngType === normalizedSelType;
    });
    
    // Update ingredients dropdown
    const ingredientsList = document.getElementById('ingredientsList');
    const ingredientsDropdown = document.getElementById('ingredientsDropdown');
    
    // Clear current options
    ingredientsList.innerHTML = '';
    ingredientsDropdown.textContent = 'Select Ingredient';
    
    if (filteredIngredients.length === 0) {
        const noIngredientsElem = document.createElement('a');
        noIngredientsElem.href = '#';
        noIngredientsElem.textContent = 'No ingredients available';
        noIngredientsElem.style.color = '#999';
        noIngredientsElem.onclick = e => e.preventDefault();
        ingredientsList.appendChild(noIngredientsElem);
        return;
    }
    
    // Add filtered ingredients
    filteredIngredients.forEach(ingredient => {
        const option = document.createElement('a');
        option.href = '#';
        option.textContent = ingredient.name;
        option.onclick = (e) => {
            e.preventDefault();
            selectIngredient(ingredient);
            toggleDropdown('ingredientsList');
        };
        ingredientsList.appendChild(option);
    });
}

function selectIngredient(ingredient) {
    console.log('Selecting ingredient:', ingredient); // Debug log

    // Update dropdown text
    const dropdownBtn = document.getElementById('ingredientsDropdown');
    if (dropdownBtn) {
        dropdownBtn.textContent = ingredient.name;
    } else {
        console.error('Ingredients dropdown button not found');
        return;
    }

    // Auto-fill price if available
    const priceInput = document.querySelector('input[placeholder="price per kg"]');
    if (priceInput) {
        if (ingredient.price) {
            priceInput.value = ingredient.price;
        } else {
            priceInput.value = ''; // Clear the price if none available
        }
    }

    // Store selected ingredient data
    window.selectedIngredient = ingredient;

    // Close dropdown
    const ingredientsList = document.getElementById('ingredientsList');
    if (ingredientsList) {
        ingredientsList.style.display = 'none';
    }

    // Show notification of selection
    showNotification(`Selected ingredient: ${ingredient.name}`, 'success');
}

// This will be handled by the backend
function updateCalculations() {
    // Update will come from backend
    // Temporarily set to 0%
    const proteinDisplay = document.querySelector('.data-filled1[placeholder="protein"]');
    const tdnDisplay = document.querySelector('.data-filled1[placeholder="TDN"]');
    
    if (proteinDisplay) proteinDisplay.textContent = '0%';
    if (tdnDisplay) tdnDisplay.textContent = '0%';
}

// Add these functions at the top of the file
function populateIngredientsDropdown(ingredientType) {
    const ingredientsList = document.getElementById('ingredientsList');
    const ingredientsDropdown = document.getElementById('ingredientsDropdown');
    
    // Clear existing options
    ingredientsList.innerHTML = '';
    
    // Get ingredients from localStorage
    const ingredients = JSON.parse(localStorage.getItem('ingredients')) || [];
    
    // Filter ingredients by type
    const filteredIngredients = ingredients.filter(ing => ing.type === ingredientType);
    
    if (filteredIngredients.length === 0) {
        const noIngredientsOption = document.createElement('a');
        noIngredientsOption.href = '#';
        noIngredientsOption.textContent = 'No ingredients found';
        ingredientsList.appendChild(noIngredientsOption);
        return;
    }
    
    // Add filtered ingredients to dropdown
    filteredIngredients.forEach(ing => {
        const option = document.createElement('a');
        option.href = '#';
        option.textContent = ing.name;
        option.onclick = () => {
            ingredientsDropdown.textContent = ing.name;
            // Auto-fill price if available
            const priceInput = document.querySelector('input[placeholder="price per kg"]');
            if (priceInput && ing.price) {
                priceInput.value = ing.price;
            }
            closeAllDropdowns();
        };
        ingredientsList.appendChild(option);
    });
}

// Update the selectIngredientType function
function selectIngredientType(type) {
    console.log('Selecting ingredient type:', type);
    
    // Update the ingredient type button text
    const buttons = document.querySelectorAll('.dropbtn');
    buttons.forEach(btn => {
        if (btn.textContent.trim() === 'Select Ingredient Type') {
            btn.textContent = type;
        }
    });

    // Get ingredients from localStorage
    const allIngredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
    console.log('All ingredients:', allIngredients);

    // Normalize the selected type
    const normalizeType = (t) => {
        // Convert to lowercase and trim
        t = t.toLowerCase().trim();
        // Handle variations of "by product" and "byproduct"
        t = t.replace(/\s*by\s*product\s*$/i, 'byproduct');
        t = t.replace(/\s+/g, ''); // Remove remaining spaces
        return t;
    };

    // Filter ingredients by type
    const filteredIngredients = allIngredients.filter(ingredient => {
        if (!ingredient.type) return false;
        
        const ingType = normalizeType(ingredient.type);
        const selectedType = normalizeType(type);
        
        console.log(`Comparing: "${ingType}" with "${selectedType}"`); // Debug log
        return ingType === selectedType;
    });

    console.log('Filtered ingredients:', filteredIngredients);

    // Update ingredients dropdown
    const ingredientsList = document.getElementById('ingredientsList');
    const ingredientsDropdown = document.getElementById('ingredientsDropdown');

    if (!ingredientsList || !ingredientsDropdown) {
        console.error('Could not find ingredients dropdown elements');
        return;
    }

    // Reset the ingredients selection
    ingredientsDropdown.textContent = 'Select Ingredient';
    ingredientsList.innerHTML = '';

    // Add filtered ingredients to dropdown
    if (filteredIngredients.length === 0) {
        const noIngredients = document.createElement('a');
        noIngredients.href = '#';
        noIngredients.textContent = 'No ingredients found';
        noIngredients.style.color = '#999';
        noIngredients.onclick = e => e.preventDefault();
        ingredientsList.appendChild(noIngredients);
    } else {
        filteredIngredients.forEach(ingredient => {
            const option = document.createElement('a');
            option.href = '#';
            option.textContent = ingredient.name;
            option.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                // Update dropdown text
                ingredientsDropdown.textContent = ingredient.name;
                
                // Auto-fill price if available
                const priceInput = document.querySelector('input[placeholder="price per kg"]');
                if (priceInput && ingredient.price) {
                    priceInput.value = ingredient.price;
                }
                
                // Store selected ingredient data
                window.selectedIngredient = ingredient;
                
                // Close dropdown
                ingredientsList.style.display = 'none';
            };
            ingredientsList.appendChild(option);
        });
    }

    // Close all dropdowns
    document.querySelectorAll('.dropdown-content').forEach(dropdown => {
        dropdown.style.display = 'none';
    });
}

// Helper function to find button by text content
function findDropdownButton(labelText) {
    // Find all frame-7 divs that contain the label text
    const containers = Array.from(document.querySelectorAll('.frame-7'));
    for (const container of containers) {
        const label = container.querySelector('.text-wrapper-6');
        if (label && (
            label.textContent.toLowerCase().includes(labelText.toLowerCase()) ||
            (labelText === 'Animal' && label.textContent.includes("Animal's Type"))
        )) {
            return container.querySelector('.dropbtn');
        }
    }
    return null;
}

// Helper function to collect feed data
function collectFeedData() {
    const feedNameInput = document.querySelector('input[placeholder="feed name"]');
    const growthRateInput = document.querySelector('input[placeholder="Growth rate"]');
    const weightInput = document.querySelector('input[placeholder="Weight"]');
    const proteinElement = document.querySelector('.data-filled1[placeholder="protein"]');
    const tdnElement = document.querySelector('.data-filled1[placeholder="TDN"]');

    const feedData = {
        feedName: feedNameInput ? feedNameInput.value.trim() : '',
        code: 'F' + Date.now().toString().slice(-6),
        animalType: findDropdownButton('Animal')?.textContent.trim() || '',
        growthRate: growthRateInput ? growthRateInput.value.trim() : '',
        weight: weightInput ? weightInput.value.trim() : '',
        gender: findDropdownButton('Gender')?.textContent.trim() || '',
        feedType: findDropdownButton('Feed Type')?.textContent.trim() || '',
        season: findDropdownButton('Season')?.textContent.trim() || '',
        protein: proteinElement ? proteinElement.textContent.trim() : '0%',
        tdn: tdnElement ? tdnElement.textContent.trim() : '0%',
        ingredients: collectIngredientsData(),
        createdAt: new Date().toISOString()
    };

    // Save protein and TDN percentages without % symbol
    feedData.proteinPercentage = feedData.protein.replace('%', '').trim();
    feedData.tdnPercentage = feedData.tdn.replace('%', '').trim();

    return feedData;
}

// Helper function to get form data
function collectFormData() {
    const feedNameInput = document.querySelector('input[placeholder="feed name"]');
    const growthRateInput = document.querySelector('input[placeholder="Growth rate"]');
    const weightInput = document.querySelector('input[placeholder="Weight"]');
    const proteinElement = document.querySelector('.data-filled1[placeholder="protein"]');
    const tdnElement = document.querySelector('.data-filled1[placeholder="TDN"]');

    const formData = {
        feedName: feedNameInput ? feedNameInput.value.trim() : '',
        code: generateFeedCode(),
        animalType: findDropdownButton('Animal')?.textContent.trim() || '',
        growthRate: growthRateInput ? growthRateInput.value.trim() : '',
        weight: weightInput ? weightInput.value.trim() : '',
        gender: findDropdownButton('Gender')?.textContent.trim() || '',
        feedType: findDropdownButton('Feed Type')?.textContent.trim() || '',
        season: findDropdownButton('Season')?.textContent.trim() || '',
        protein: proteinElement ? proteinElement.textContent.trim() : '0%',
        tdn: tdnElement ? tdnElement.textContent.trim() : '0%',
        ingredients: collectIngredientsData(),
        createdAt: new Date().toISOString()
    };

    // Store protein and TDN percentages as numbers for calculations
    formData.proteinPercentage = parseFloat(formData.protein.replace('%', '')) || 0;
    formData.tdnPercentage = parseFloat(formData.tdn.replace('%', '')) || 0;

    return formData;
}

// Helper function to generate unique feed code
function generateFeedCode() {
    return 'F' + Date.now().toString().slice(-6);
}

// Initialize everything when the document is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize variables
    const addIngredientBtn = document.querySelector('.data-filled11 img');
    const ingredientsTable = document.querySelector('table tbody');
    const pricePerKgInput = document.querySelector('input[placeholder="price per kg"]');
    const finishIngredientsBtn = document.querySelector('.btntext1');
    const saveFeedBtn = document.getElementById('updateIngredientsBtn');

    // Add click handler for Finish Choose Ingredients button
    if (finishIngredientsBtn) {
        finishIngredientsBtn.addEventListener('click', function() {
            // Validate ingredients and form data
            if (!validateFormData()) {
                return;
            }

            if (!validateIngredients()) {
                return;
            }

            try {
                const formData = collectFormData();
                // Store the validated feed data
                localStorage.setItem('currentFeed', JSON.stringify(formData));
                showNotification('Feed data prepared successfully! Click Save Feed to continue.', 'success');
            } catch (error) {
                console.error('Error preparing feed:', error);
                showNotification('Error preparing feed data. Please try again.', 'error');
            }
        });
    }

    // Add Save Feed button click handler
    if (saveFeedBtn) {
        saveFeedBtn.addEventListener('click', function() {
            // Check if ingredients were finished first
            const currentFeed = localStorage.getItem('currentFeed');
            if (!currentFeed) {
                showNotification('Please click "Finish Choose Ingredients" first', 'error');
                return;
            }

            try {
                // Load current feed data
                const feedData = JSON.parse(currentFeed);

                // Load existing feeds
                const feeds = JSON.parse(localStorage.getItem('feeds') || '[]');

                // Check for duplicate feed names
                if (feeds.some(feed => feed.feedName === feedData.feedName)) {
                    showNotification('A feed with this name already exists', 'error');
                    return;
                }

                // Add feed to storage
                feeds.push(feedData);
                localStorage.setItem('feeds', JSON.stringify(feeds));

                // Clear current feed
                localStorage.removeItem('currentFeed');

                showNotification('Feed saved successfully!', 'success');
                
                // Redirect to feeds page after short delay
                setTimeout(() => {
                    window.location.href = 'feed.html';
                }, 1500);
            } catch (error) {
                console.error('Error saving feed:', error);
                showNotification('Error saving feed. Please try again.', 'error');
            }
        });
    }

    // Add ingredient button click handler
    addIngredientBtn.addEventListener('click', function() {
        // Get selected ingredient and price
        const ingredientsDropdown = document.getElementById('ingredientsDropdown');
        const priceInput = document.querySelector('input[placeholder="price per kg"]');
        
        // Validate selections
        if (ingredientsDropdown.textContent === 'Select Ingredient') {
            showNotification('Please select an ingredient first', 'error');
            return;
        }
        
        if (!priceInput.value) {
            showNotification('Please enter price per kg', 'error');
            return;
        }

        // Get ingredient data from localStorage
        const allIngredients = loadIngredientsFromStorage();
        const selectedIngredient = allIngredients.find(ing => ing.name === ingredientsDropdown.textContent);

        if (!selectedIngredient) {
            showNotification('Selected ingredient not found', 'error');
            return;
        }        // Create new table row
        const newRow = document.createElement('tr');
        newRow.innerHTML = `
            <td><div class="data-filled">${selectedIngredient.name}</div></td>
            <td><div class="frame-7"><div class="text-wrapper-6">Price per kg</div>
                <div class="data-filled">${priceInput.value}</div></div></td>
            <td><div class="frame-7"><div class="text-wrapper-6">Weight</div>
                <div class="data-filled">--</div></div></td>
            <td><img class="img" src="icons/delingred.png"></td>
        `;

        // Add delete functionality
        const deleteBtn = newRow.querySelector('img[src="icons/delingred.png"]');
        deleteBtn.addEventListener('click', function() {
            newRow.remove();
            updateCalculations();
        });

        // Add the row to the table
        ingredientsTable.appendChild(newRow);

        // Reset selection fields
        ingredientsDropdown.textContent = 'Select Ingredient';
        priceInput.value = '';

        // Show success notification
        showNotification('Ingredient added successfully', 'success');

        // Update calculations
        updateCalculations();
    });

    // Set up dropdown button click handlers
    document.querySelectorAll('.dropbtn').forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const dropdownContent = this.nextElementSibling;
            if (dropdownContent && dropdownContent.classList.contains('dropdown-content')) {
                // Close all other dropdowns first
                document.querySelectorAll('.dropdown-content').forEach(dropdown => {
                    if (dropdown !== dropdownContent) {
                        dropdown.style.display = 'none';
                    }
                });
                // Toggle this dropdown
                dropdownContent.style.display = dropdownContent.style.display === 'block' ? 'none' : 'block';
            }
        });
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.matches('.dropbtn') && !e.target.closest('.dropdown-content')) {
            closeAllDropdowns();
        }
    });

    // Close dropdowns with ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllDropdowns();
        }
    });

    // Add input validation for price
    if (pricePerKgInput) {
        pricePerKgInput.addEventListener('input', function() {
            // Remove non-numeric characters except decimal point
            this.value = this.value.replace(/[^\d.]/g, '');
            
            // Ensure only one decimal point
            const parts = this.value.split('.');
            if (parts.length > 2) {
                this.value = parts[0] + '.' + parts.slice(1).join('');
            }
        });
    }
});