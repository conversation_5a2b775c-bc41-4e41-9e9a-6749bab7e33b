/**
 * Debug script for testing search functionality
 * Run this in the browser console on the animal.html page
 */

console.log('🔍 Starting search functionality debug...');

// Test 1: Check if required elements exist
function testElements() {
    console.log('\n📋 Testing page elements...');
    
    const searchButton = document.querySelector('#searchButton') || document.querySelector('.search');
    console.log('Search button:', searchButton);
    
    const animalTable = document.querySelector('table');
    console.log('Animal table:', animalTable);
    
    const animalTableBody = document.querySelector('tbody');
    console.log('Table body:', animalTableBody);
    
    return {
        searchButton: !!searchButton,
        animalTable: !!animalTable,
        animalTableBody: !!animalTableBody
    };
}

// Test 2: Check if JavaScript objects are loaded
function testJavaScriptObjects() {
    console.log('\n🔧 Testing JavaScript objects...');
    
    console.log('window.animalPageController:', typeof window.animalPageController);
    console.log('window.performAnimalSearch:', typeof window.performAnimalSearch);
    console.log('window.cancelAnimalSearch:', typeof window.cancelAnimalSearch);
    console.log('window.searchAnimals:', typeof window.searchAnimals);
    
    if (window.animalPageController) {
        console.log('searchDropdownManager:', typeof window.animalPageController.searchDropdownManager);
        console.log('popupManager:', typeof window.animalPageController.popupManager);
        console.log('isInitialized:', window.animalPageController.isInitialized);
    }
    
    return {
        animalPageController: !!window.animalPageController,
        performAnimalSearch: typeof window.performAnimalSearch === 'function',
        cancelAnimalSearch: typeof window.cancelAnimalSearch === 'function',
        searchAnimals: typeof window.searchAnimals === 'function'
    };
}

// Test 3: Test search button click
function testSearchButtonClick() {
    console.log('\n🖱️ Testing search button click...');
    
    const searchButton = document.querySelector('#searchButton') || document.querySelector('.search');
    if (!searchButton) {
        console.error('❌ Search button not found');
        return false;
    }
    
    try {
        console.log('Clicking search button...');
        searchButton.click();
        console.log('✅ Search button clicked successfully');
        return true;
    } catch (error) {
        console.error('❌ Error clicking search button:', error);
        return false;
    }
}

// Test 4: Test search functions directly
function testSearchFunctions() {
    console.log('\n🔍 Testing search functions...');
    
    try {
        if (window.performAnimalSearch) {
            console.log('Testing performAnimalSearch...');
            window.performAnimalSearch('code');
            console.log('✅ performAnimalSearch executed');
        }
        
        if (window.cancelAnimalSearch) {
            console.log('Testing cancelAnimalSearch...');
            window.cancelAnimalSearch();
            console.log('✅ cancelAnimalSearch executed');
        }
        
        if (window.searchAnimals) {
            console.log('Testing searchAnimals...');
            const results = window.searchAnimals('test');
            console.log('✅ searchAnimals returned:', results.length, 'results');
        }
        
        return true;
    } catch (error) {
        console.error('❌ Error testing search functions:', error);
        return false;
    }
}

// Test 5: Create test data and test search
function testSearchWithData() {
    console.log('\n📊 Testing search with data...');
    
    try {
        // Create test data
        const testAnimals = [
            {
                code: 'A001',
                type: 'dairy',
                herdNumber: 'H001',
                gender: 'female',
                weight: 450,
                dateOfWeight: '2024-01-15',
                healthcareNotes: 'Healthy'
            },
            {
                code: 'A002',
                type: 'newborn',
                herdNumber: 'H002',
                gender: 'male',
                weight: 35,
                dateOfWeight: '2024-01-20',
                healthcareNotes: 'Good condition'
            },
            {
                code: 'B001',
                type: 'fattening',
                herdNumber: 'H003',
                gender: 'male',
                weight: 300,
                dateOfWeight: '2024-01-18',
                healthcareNotes: 'Gaining weight'
            }
        ];
        
        localStorage.setItem('animals', JSON.stringify(testAnimals));
        console.log('✅ Test data created');
        
        // Refresh table if possible
        if (window.animalPageController && window.animalPageController.refreshAnimalTable) {
            window.animalPageController.refreshAnimalTable();
            console.log('✅ Table refreshed');
        }
        
        // Test search
        if (window.searchAnimals) {
            const searchResults = window.searchAnimals('A001');
            console.log('Search results for "A001":', searchResults);
            
            const typeResults = window.searchAnimals('dairy');
            console.log('Search results for "dairy":', typeResults);
        }
        
        return true;
    } catch (error) {
        console.error('❌ Error testing search with data:', error);
        return false;
    }
}

// Test 6: Test dropdown manager directly
function testDropdownManager() {
    console.log('\n📋 Testing dropdown manager...');
    
    if (!window.animalPageController || !window.animalPageController.searchDropdownManager) {
        console.error('❌ Search dropdown manager not available');
        return false;
    }
    
    try {
        const searchButton = document.querySelector('#searchButton') || document.querySelector('.search');
        if (!searchButton) {
            console.error('❌ Search button not found');
            return false;
        }
        
        console.log('Testing dropdown manager showDropdown...');
        window.animalPageController.searchDropdownManager.showDropdown(searchButton);
        console.log('✅ Dropdown manager executed');
        
        // Wait a bit then hide
        setTimeout(() => {
            console.log('Hiding dropdown...');
            window.animalPageController.searchDropdownManager.hideDropdown();
            console.log('✅ Dropdown hidden');
        }, 2000);
        
        return true;
    } catch (error) {
        console.error('❌ Error testing dropdown manager:', error);
        return false;
    }
}

// Run all tests
function runAllTests() {
    console.log('🚀 Running all search functionality tests...\n');
    
    const results = {
        elements: testElements(),
        javascript: testJavaScriptObjects(),
        buttonClick: testSearchButtonClick(),
        functions: testSearchFunctions(),
        searchWithData: testSearchWithData(),
        dropdownManager: testDropdownManager()
    };
    
    console.log('\n📊 Test Results Summary:');
    console.table(results);
    
    const allPassed = Object.values(results).every(result => 
        typeof result === 'boolean' ? result : Object.values(result).every(v => v)
    );
    
    console.log(allPassed ? '✅ All tests passed!' : '❌ Some tests failed');
    
    return results;
}

// Auto-run tests if this script is loaded
if (typeof window !== 'undefined') {
    // Wait for page to load
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(runAllTests, 1000);
        });
    } else {
        setTimeout(runAllTests, 1000);
    }
}

// Export functions for manual testing
window.debugSearch = {
    testElements,
    testJavaScriptObjects,
    testSearchButtonClick,
    testSearchFunctions,
    testSearchWithData,
    testDropdownManager,
    runAllTests
};

console.log('🔧 Debug functions available at window.debugSearch');
