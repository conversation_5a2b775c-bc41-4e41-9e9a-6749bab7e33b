document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const newPasswordInput = document.querySelector('input[placeholder="Password"]:nth-of-type(1)');
    const confirmPasswordInput = document.querySelector('input[placeholder="Password"]:nth-of-type(2)');
    const resetPasswordBtn = document.querySelector('.component');
    
    // Get email from sessionStorage (set in forgetpassbyemail.js)
    const email = sessionStorage.getItem('resetPasswordEmail');
    
    // If no email in session, redirect to forgetpassbyemail.html
    if (!email) {
        alert('Please enter your email first');
        window.location.href = 'forgetpassbyemail.html';
        return;
    }
    
    // Handle reset password button click
    resetPasswordBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Get form values
        const newPassword = newPasswordInput.value.trim();
        const confirmPassword = confirmPasswordInput.value.trim();
        
        // Validate form
        if (!newPassword) {
            alert('Please enter a new password');
            newPasswordInput.focus();
            return;
        }
        
        if (newPassword.length < 8) {
            alert('Password must be at least 8 characters long');
            newPasswordInput.focus();
            return;
        }
        
        if (!confirmPassword) {
            alert('Please confirm your password');
            confirmPasswordInput.focus();
            return;
        }
        
        if (newPassword !== confirmPassword) {
            alert('Passwords do not match');
            confirmPasswordInput.focus();
            return;
        }
        
        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        
        // Find user with the email
        const userIndex = users.findIndex(user => user.email === email);
        
        if (userIndex === -1) {
            alert('User not found. Please try again.');
            window.location.href = 'forgetpassbyemail.html';
            return;
        }
        
        // Check if new password is different from the old one
        if (users[userIndex].password === newPassword) {
            alert('New password must be different from the previous password');
            newPasswordInput.value = '';
            confirmPasswordInput.value = '';
            newPasswordInput.focus();
            return;
        }
        
        // Update user's password
        users[userIndex].password = newPassword;
        
        // Save updated users to localStorage
        localStorage.setItem('users', JSON.stringify(users));
        
        // Clear session storage
        sessionStorage.removeItem('resetPasswordEmail');
        
        // Show success message
        alert('Password reset successfully!');
        
        // Redirect to login page
        window.location.href = 'login.html';
    });
    
    // Add event listeners for Enter key on input fields
    newPasswordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            confirmPasswordInput.focus();
        }
    });
    
    confirmPasswordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            resetPasswordBtn.click();
        }
    });
    
    // Optional: Add password visibility toggle
    addPasswordToggle();
    
    function addPasswordToggle() {
        // Create eye icons for both password fields
        const passwordFields = document.querySelectorAll('input[type="password"]');
        
        passwordFields.forEach(field => {
            // Create eye icon
            const eyeIcon = document.createElement('span');
            eyeIcon.innerHTML = '👁️';
            eyeIcon.style.position = 'absolute';
            eyeIcon.style.right = '10px';
            eyeIcon.style.top = '50%';
            eyeIcon.style.transform = 'translateY(-50%)';
            eyeIcon.style.cursor = 'pointer';
            eyeIcon.style.zIndex = '10';
            
            // Position the container relatively
            field.parentElement.style.position = 'relative';
            
            // Add eye icon after the input field
            field.parentElement.appendChild(eyeIcon);
            
            // Add click event to toggle password visibility
            eyeIcon.addEventListener('click', function() {
                if (field.type === 'password') {
                    field.type = 'text';
                    eyeIcon.style.opacity = '0.7';
                } else {
                    field.type = 'password';
                    eyeIcon.style.opacity = '1';
                }
            });
        });
    }
    
    // Optional: Add password strength meter
    newPasswordInput.addEventListener('input', function() {
        const password = this.value;
        const strengthIndicator = document.querySelector('.passinstruction');
        
        if (password.length === 0) {
            strengthIndicator.textContent = 'Must be at least 8 characters';
            strengthIndicator.style.color = '#A09F9F';
            return;
        }
        
        // Check password strength
        let strength = 0;
        
        // Length check
        if (password.length >= 8) strength += 1;
        if (password.length >= 12) strength += 1;
        
        // Character type checks
        if (/[A-Z]/.test(password)) strength += 1;
        if (/[a-z]/.test(password)) strength += 1;
        if (/[0-9]/.test(password)) strength += 1;
        if (/[^A-Za-z0-9]/.test(password)) strength += 1;
        
        // Update strength indicator
        if (strength < 3) {
            strengthIndicator.textContent = 'Weak password';
            strengthIndicator.style.color = '#FF6347'; // Tomato red
        } else if (strength < 5) {
            strengthIndicator.textContent = 'Medium strength password';
            strengthIndicator.style.color = '#FFA500'; // Orange
        } else {
            strengthIndicator.textContent = 'Strong password';
            strengthIndicator.style.color = '#32CD32'; // Lime green
        }
    });
});