/**
 * Add New Animal Page - Complete OOP Implementation
 * This file provides comprehensive functionality for adding new animals
 * with proper validation, data management, and user feedback
 */

// ==================== BASE CLASSES ====================

/**
 * Base Data Manager for localStorage operations
 */
class BaseDataManager {
    constructor() {
        this.storageKeys = {
            animals: 'animals',
            dairyAnimals: 'dairyAnimals',
            newbornAnimals: 'newbornAnimals'
        };
    }

    /**
     * Get data from localStorage
     */
    getData(key) {
        try {
            const data = localStorage.getItem(this.storageKeys[key] || key);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`Error getting data for key ${key}:`, error);
            return [];
        }
    }

    /**
     * Save data to localStorage
     */
    saveData(key, data) {
        try {
            localStorage.setItem(this.storageKeys[key] || key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error(`Error saving data for key ${key}:`, error);
            return false;
        }
    }

    /**
     * Generate unique ID
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    /**
     * Check if animal code exists
     */
    codeExists(code) {
        if (!code || typeof code !== 'string') {
            return false;
        }

        const animals = this.getData('animals');
        return animals.some(animal => animal && animal.code && typeof animal.code === 'string' &&
                               animal.code.toLowerCase() === code.toLowerCase());
    }
}

/**
 * Validation Manager for form validation
 */
class ValidationManager {
    constructor() {
        this.rules = {
            required: ['code', 'type', 'gender'],
            conditionalRequired: {
                dairy: ['herdNumber'],
                newborn: ['dateOfBirth']
            }
        };
    }

    /**
     * Validate single field
     */
    validateField(fieldName, value, animalType = null) {
        const errors = [];

        // Required field validation
        if (this.rules.required.includes(fieldName) && !value) {
            errors.push(`${this.getFieldDisplayName(fieldName)} is required`);
        }

        // Conditional required validation
        if (animalType && this.rules.conditionalRequired[animalType]) {
            if (this.rules.conditionalRequired[animalType].includes(fieldName) && !value) {
                errors.push(`${this.getFieldDisplayName(fieldName)} is required for ${animalType} animals`);
            }
        }

        // Specific field validations
        switch (fieldName) {
            case 'code':
                if (value && !/^[A-Za-z0-9]+$/.test(value)) {
                    errors.push('Code can only contain letters and numbers');
                }
                break;
            case 'weight':
                if (value && (isNaN(value) || parseFloat(value) <= 0)) {
                    errors.push('Weight must be a positive number');
                }
                break;
            case 'dateOfBirth':
                if (value && new Date(value) > new Date()) {
                    errors.push('Date of birth cannot be in the future');
                }
                break;
            case 'dateOfWeight':
                if (value && new Date(value) > new Date()) {
                    errors.push('Date of weight cannot be in the future');
                }
                break;
        }

        return errors;
    }

    /**
     * Validate entire form data
     */
    validateFormData(data) {
        const errors = [];

        // Validate all fields
        Object.entries(data).forEach(([field, value]) => {
            const fieldErrors = this.validateField(field, value, data.type);
            errors.push(...fieldErrors);
        });

        // Cross-field validations
        if (data.dateOfBirth && data.dateOfWeight) {
            if (new Date(data.dateOfWeight) < new Date(data.dateOfBirth)) {
                errors.push('Date of weight cannot be before date of birth');
            }
        }

        // Business rule validations
        if (data.type === 'dairy' && data.gender !== 'female') {
            errors.push('Dairy animals must be female');
        }

        return errors;
    }

    /**
     * Get display name for field
     */
    getFieldDisplayName(fieldName) {
        const displayNames = {
            code: 'Code',
            type: 'Type',
            gender: 'Gender',
            herdNumber: 'Herd Number',
            weight: 'Weight',
            dateOfWeight: 'Date of Weight',
            dateOfBirth: 'Date of Birth',
            healthcareNotes: 'Healthcare Notes',
            takenVaccination: 'Vaccination Information'
        };
        return displayNames[fieldName] || fieldName;
    }
}

/**
 * UI Manager for user interface operations
 */
class UIManager {
    constructor() {
        this.notifications = [];
    }

    /**
     * Show field error
     */
    showFieldError(fieldElement, message) {
        this.clearFieldError(fieldElement);

        // Add error styling
        fieldElement.style.borderColor = '#dc3545';
        fieldElement.style.backgroundColor = '#fff5f5';

        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.style.cssText = `
            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
            padding: 2px 0;
        `;
        errorDiv.textContent = message;

        // Insert after field
        fieldElement.parentNode.insertBefore(errorDiv, fieldElement.nextSibling);
    }

    /**
     * Clear field error
     */
    clearFieldError(fieldElement) {
        // Reset styling
        fieldElement.style.borderColor = '';
        fieldElement.style.backgroundColor = '';

        // Remove error message
        const errorDiv = fieldElement.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * Clear all field errors
     */
    clearAllFieldErrors() {
        const errorDivs = document.querySelectorAll('.field-error');
        errorDivs.forEach(div => div.remove());

        const fields = document.querySelectorAll('.data-filled, .textarea');
        fields.forEach(field => {
            field.style.borderColor = '';
            field.style.backgroundColor = '';
        });
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        this.clearNotifications();

        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            animation: slideIn 0.3s ease-out;
        `;

        // Set colors based on type
        const colors = {
            success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
            error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
            warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
            info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
        };

        const color = colors[type] || colors.info;
        notification.style.backgroundColor = color.bg;
        notification.style.border = `1px solid ${color.border}`;
        notification.style.color = color.text;

        notification.textContent = message;

        // Add close button
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            float: right;
            margin-left: 10px;
            cursor: pointer;
            font-size: 18px;
            line-height: 1;
        `;
        closeBtn.onclick = () => notification.remove();
        notification.appendChild(closeBtn);

        document.body.appendChild(notification);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }

        // Add animation styles
        if (!document.querySelector('#notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }

        this.notifications.push(notification);
        return notification;
    }

    /**
     * Clear all notifications
     */
    clearNotifications() {
        this.notifications.forEach(notification => {
            if (notification.parentNode) {
                notification.remove();
            }
        });
        this.notifications = [];
    }

    /**
     * Show loading state on button
     */
    showLoading(button, text = 'Saving...') {
        if (!button) return;

        button.disabled = true;
        const btnText = button.querySelector('.btntext');
        if (btnText) {
            button.dataset.originalText = btnText.textContent;
            btnText.textContent = text;
        }
        button.style.opacity = '0.7';
    }

    /**
     * Hide loading state on button
     */
    hideLoading(button) {
        if (!button) return;

        button.disabled = false;
        const btnText = button.querySelector('.btntext');
        if (btnText && button.dataset.originalText) {
            btnText.textContent = button.dataset.originalText;
            delete button.dataset.originalText;
        }
        button.style.opacity = '1';
    }
}

// ==================== MAIN CLASSES ====================

/**
 * Animal Data Manager - Handles all data operations
 */
class AnimalDataManager extends BaseDataManager {
    constructor() {
        super();
    }

    /**
     * Add new animal to main table and type-specific table
     */
    addAnimal(animalData) {
        // Validate input data
        if (!animalData) {
            throw new Error('Animal data is required');
        }

        if (!animalData.code || typeof animalData.code !== 'string') {
            throw new Error('Animal code is required and must be a string');
        }

        if (!animalData.type || typeof animalData.type !== 'string') {
            throw new Error('Animal type is required and must be a string');
        }

        if (!animalData.gender || typeof animalData.gender !== 'string') {
            throw new Error('Animal gender is required and must be a string');
        }

        const animalId = this.generateId();
        const timestamp = new Date().toISOString();

        // Create base animal object
        const baseAnimal = {
            ...animalData,
            id: animalId,
            createdAt: timestamp,
            updatedAt: timestamp
        };

        try {
            // 1. Save to main animals table (ALWAYS)
            this.saveToMainTable(baseAnimal);

            // 2. Save to type-specific table based on animal type
            switch (animalData.type) {
                case 'dairy':
                    this.saveToDairyTable(baseAnimal);
                    break;
                case 'newborn':
                    this.saveToNewbornTable(baseAnimal);
                    break;
                case 'fattening':
                    // Fattening animals only go to main table
                    break;
                default:
                    console.warn(`Unknown animal type: ${animalData.type}`);
            }

            console.log(`✅ Animal ${animalData.code} saved successfully:`, {
                mainTable: true,
                typeSpecificTable: animalData.type !== 'fattening',
                type: animalData.type
            });

            return baseAnimal;

        } catch (error) {
            console.error('❌ Error saving animal:', error);
            throw error;
        }
    }

    /**
     * Save to main animals table
     */
    saveToMainTable(animal) {
        const animals = this.getData('animals');

        // Check for duplicate code (safe version)
        if (animal && animal.code && typeof animal.code === 'string') {
            const duplicateExists = animals.some(a => a && a.code && typeof a.code === 'string' &&
                                                 a.code.toLowerCase() === animal.code.toLowerCase());
            if (duplicateExists) {
                throw new Error(`Animal with code "${animal.code}" already exists`);
            }
        }

        animals.push(animal);

        if (!this.saveData('animals', animals)) {
            throw new Error('Failed to save to main animals table');
        }

        console.log(`📋 Saved to main animals table: ${animal.code}`);
    }

    /**
     * Save to dairy animals table
     */
    saveToDairyTable(animal) {
        const dairyAnimals = this.getData('dairyAnimals');

        // Add dairy-specific fields
        const dairyAnimal = {
            ...animal,
            animalId: animal.id, // Reference to main table
            milkProduction: 0,
            averageDailyMilk: 0,
            lastMilkingDate: null,
            totalMilkProduced: 0,
            lactationPeriod: null,
            breedType: null,
            calvingDate: null,
            dryPeriodStart: null,
            dryPeriodEnd: null
        };

        dairyAnimals.push(dairyAnimal);

        if (!this.saveData('dairyAnimals', dairyAnimals)) {
            throw new Error('Failed to save to dairy animals table');
        }

        console.log(`🥛 Saved to dairy animals table: ${animal.code}`);
    }

    /**
     * Save to newborn animals table
     */
    saveToNewbornTable(animal) {
        const newbornAnimals = this.getData('newbornAnimals');

        // Add newborn-specific fields
        const newbornAnimal = {
            ...animal,
            animalId: animal.id, // Reference to main table
            motherCode: null,
            fatherCode: null,
            birthWeight: animal.weight || null,
            weaningDate: null,
            weaningWeight: null,
            tagNumber: null,
            registrationNumber: null,
            pedigreeInfo: null
        };

        newbornAnimals.push(newbornAnimal);

        if (!this.saveData('newbornAnimals', newbornAnimals)) {
            throw new Error('Failed to save to newborn animals table');
        }

        console.log(`👶 Saved to newborn animals table: ${animal.code}`);
    }

    /**
     * Get statistics
     */
    getStatistics() {
        const animals = this.getData('animals');
        const dairyAnimals = this.getData('dairyAnimals');
        const newbornAnimals = this.getData('newbornAnimals');

        return {
            total: animals.length,
            dairy: animals.filter(a => a.type === 'dairy').length,
            newborn: animals.filter(a => a.type === 'newborn').length,
            fattening: animals.filter(a => a.type === 'fattening').length,
            female: animals.filter(a => a.gender === 'female').length,
            male: animals.filter(a => a.gender === 'male').length,
            dairyTableCount: dairyAnimals.length,
            newbornTableCount: newbornAnimals.length,
            dataIntegrity: {
                dairyConsistent: animals.filter(a => a.type === 'dairy').length === dairyAnimals.length,
                newbornConsistent: animals.filter(a => a.type === 'newborn').length === newbornAnimals.length
            }
        };
    }

    /**
     * Validate data integrity
     */
    validateDataIntegrity() {
        const animals = this.getData('animals');
        const dairyAnimals = this.getData('dairyAnimals');
        const newbornAnimals = this.getData('newbornAnimals');
        const issues = [];

        // Check dairy animals consistency
        const dairyInMain = animals.filter(a => a.type === 'dairy');
        if (dairyInMain.length !== dairyAnimals.length) {
            issues.push(`Dairy animals mismatch: ${dairyInMain.length} in main table, ${dairyAnimals.length} in dairy table`);
        }

        // Check newborn animals consistency
        const newbornInMain = animals.filter(a => a.type === 'newborn');
        if (newbornInMain.length !== newbornAnimals.length) {
            issues.push(`Newborn animals mismatch: ${newbornInMain.length} in main table, ${newbornAnimals.length} in newborn table`);
        }

        // Check for orphaned records
        dairyAnimals.forEach(dairy => {
            if (!animals.find(a => a.id === dairy.id)) {
                issues.push(`Orphaned dairy record: ${dairy.code} (ID: ${dairy.id})`);
            }
        });

        newbornAnimals.forEach(newborn => {
            if (!animals.find(a => a.id === newborn.id)) {
                issues.push(`Orphaned newborn record: ${newborn.code} (ID: ${newborn.id})`);
            }
        });

        return {
            isValid: issues.length === 0,
            issues,
            summary: {
                totalAnimals: animals.length,
                dairyAnimals: dairyAnimals.length,
                newbornAnimals: newbornAnimals.length,
                totalIssues: issues.length
            }
        };
    }

    /**
     * Clear all data
     */
    clearAllData() {
        Object.values(this.storageKeys).forEach(key => {
            localStorage.removeItem(key);
        });
        console.log('🗑️ All animal data cleared');
    }

    /**
     * Export all data
     */
    exportData() {
        return {
            animals: this.getData('animals'),
            dairyAnimals: this.getData('dairyAnimals'),
            newbornAnimals: this.getData('newbornAnimals'),
            exportedAt: new Date().toISOString()
        };
    }
}

/**
 * Main AddNewAnimal Controller Class
 */
class AddNewAnimal {
    constructor() {
        this.dataManager = new AnimalDataManager();
        this.validationManager = new ValidationManager();
        this.uiManager = new UIManager();
        this.formData = {};
        this.isInitialized = false;
    }

    /**
     * Initialize the controller
     */
    init() {
        try {
            console.log('🚀 Initializing Add New Animal system...');

            this.setupFormElements();
            this.setupEventListeners();
            this.setupValidation();
            this.markRequiredFields();

            this.isInitialized = true;
            console.log('✅ Add New Animal system initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize Add New Animal system:', error);
            this.uiManager.showNotification('Failed to initialize the form. Please refresh the page.', 'error');
        }
    }

    /**
     * Setup form elements
     */
    setupFormElements() {
        this.elements = {
            // Type radio buttons
            typeRadios: document.querySelectorAll('input[name="access"]'),

            // Gender radio buttons
            genderRadios: document.querySelectorAll('input[name="gender"]'),

            // Input fields
            codeInput: document.querySelector('.data-filled'),
            herdNumberInput: document.querySelectorAll('.data-filled')[1],
            weightInput: document.querySelectorAll('.data-filled')[2],
            dateOfWeightInput: document.querySelectorAll('.data-filled')[3],
            dateOfBirthInput: document.querySelectorAll('.data-filled')[4],

            // Textarea fields
            healthcareNotesInput: document.querySelectorAll('.textarea')[0],
            vaccinationInput: document.querySelectorAll('.textarea')[1],

            // Save button
            saveButton: document.querySelector('.frame-9')
        };

        // Validate that all elements exist
        const missingElements = [];
        Object.entries(this.elements).forEach(([key, element]) => {
            if (!element || (Array.isArray(element) && element.length === 0)) {
                missingElements.push(key);
            }
        });

        if (missingElements.length > 0) {
            throw new Error(`Missing form elements: ${missingElements.join(', ')}`);
        }

        console.log('📋 Form elements found and mapped successfully');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Type selection
        this.elements.typeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.handleTypeChange(e.target.id);
                }
            });
        });

        // Gender selection
        this.elements.genderRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.handleGenderChange(e.target.id);
                }
            });
        });

        // Input field changes
        const allInputs = [
            this.elements.codeInput,
            this.elements.herdNumberInput,
            this.elements.weightInput,
            this.elements.dateOfWeightInput,
            this.elements.dateOfBirthInput,
            this.elements.healthcareNotesInput,
            this.elements.vaccinationInput
        ];

        allInputs.forEach((input, index) => {
            if (input) {
                // Real-time validation on input
                input.addEventListener('input', (e) => {
                    this.handleInputChange(e, this.getFieldName(index));
                });

                // Validation on blur
                input.addEventListener('blur', (e) => {
                    this.validateSingleField(e.target, this.getFieldName(index));
                });
            }
        });

        // Save button
        this.elements.saveButton.addEventListener('click', (e) => {
            e.preventDefault();
            this.handleSave();
        });

        console.log('🔗 Event listeners setup complete');
    }

    /**
     * Get field name by index
     */
    getFieldName(index) {
        const fieldNames = [
            'code',
            'herdNumber',
            'weight',
            'dateOfWeight',
            'dateOfBirth',
            'healthcareNotes',
            'takenVaccination'
        ];
        return fieldNames[index] || 'unknown';
    }

    /**
     * Handle animal type change
     */
    handleTypeChange(type) {
        this.formData.type = type;
        console.log(`📝 Animal type selected: ${type}`);

        // Clear previous validation errors
        this.uiManager.clearAllFieldErrors();

        // Update required fields based on type
        this.updateRequiredFields(type);

        // Auto-select female for dairy animals
        if (type === 'dairy') {
            const femaleRadio = document.getElementById('female');
            if (femaleRadio && !femaleRadio.checked) {
                femaleRadio.checked = true;
                this.handleGenderChange('female');
                this.uiManager.showNotification('Dairy animals must be female. Gender automatically set to female.', 'info', 3000);
            }
        }
    }

    /**
     * Handle gender change
     */
    handleGenderChange(gender) {
        this.formData.gender = gender;
        console.log(`👤 Gender selected: ${gender}`);

        // Validate dairy animal gender
        if (this.formData.type === 'dairy' && gender !== 'female') {
            this.uiManager.showNotification('Dairy animals must be female', 'warning');

            // Auto-correct to female
            setTimeout(() => {
                const femaleRadio = document.getElementById('female');
                if (femaleRadio) {
                    femaleRadio.checked = true;
                    this.formData.gender = 'female';
                }
            }, 1000);
        }
    }

    /**
     * Handle input field changes
     */
    handleInputChange(event, fieldName) {
        const value = event.target.value.trim();
        this.formData[fieldName] = value;

        // Clear field error when user starts typing
        this.uiManager.clearFieldError(event.target);

        // Real-time validation for specific fields
        if (fieldName === 'code' && value) {
            this.validateCodeUniqueness(event.target, value);
        }
    }

    /**
     * Validate code uniqueness
     */
    validateCodeUniqueness(fieldElement, code) {
        if (this.dataManager.codeExists(code)) {
            this.uiManager.showFieldError(fieldElement, `Animal with code "${code}" already exists`);
            return false;
        }
        return true;
    }

    /**
     * Validate single field
     */
    validateSingleField(fieldElement, fieldName) {
        const value = fieldElement.value.trim();
        const errors = this.validationManager.validateField(fieldName, value, this.formData.type);

        if (errors.length > 0) {
            this.uiManager.showFieldError(fieldElement, errors[0]);
            return false;
        } else {
            this.uiManager.clearFieldError(fieldElement);
            return true;
        }
    }

    /**
     * Update required fields based on animal type
     */
    updateRequiredFields(type) {
        // Clear all required indicators first
        document.querySelectorAll('.required-indicator').forEach(el => el.remove());

        // Add required indicators
        const requiredFields = ['code', 'type', 'gender'];

        if (type === 'dairy') {
            requiredFields.push('herdNumber');
        } else if (type === 'newborn') {
            requiredFields.push('dateOfBirth');
        }

        this.markFieldsAsRequired(requiredFields);
    }

    /**
     * Mark fields as required
     */
    markFieldsAsRequired(fieldNames) {
        const fieldMap = {
            code: this.elements.codeInput,
            herdNumber: this.elements.herdNumberInput,
            dateOfBirth: this.elements.dateOfBirthInput
        };

        fieldNames.forEach(fieldName => {
            const field = fieldMap[fieldName];
            if (field) {
                const label = field.closest('.frame-7')?.querySelector('.text-wrapper-6');
                if (label && !label.textContent.includes('*')) {
                    const indicator = document.createElement('span');
                    indicator.className = 'required-indicator';
                    indicator.textContent = ' *';
                    indicator.style.color = '#dc3545';
                    label.appendChild(indicator);
                }
            }
        });
    }

    /**
     * Mark required fields on initialization
     */
    markRequiredFields() {
        this.markFieldsAsRequired(['code']);
    }

    /**
     * Setup validation
     */
    setupValidation() {
        // Add form validation styles
        const style = document.createElement('style');
        style.textContent = `
            .field-error {
                animation: shake 0.3s ease-in-out;
            }
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Collect all form data
     */
    collectFormData() {
        const data = { ...this.formData };

        // Collect input field values safely
        if (this.elements.codeInput && this.elements.codeInput.value !== undefined) {
            data.code = this.elements.codeInput.value.trim();
        }
        if (this.elements.herdNumberInput && this.elements.herdNumberInput.value !== undefined) {
            data.herdNumber = this.elements.herdNumberInput.value.trim();
        }
        if (this.elements.weightInput && this.elements.weightInput.value !== undefined) {
            data.weight = this.elements.weightInput.value.trim();
        }
        if (this.elements.dateOfWeightInput && this.elements.dateOfWeightInput.value !== undefined) {
            data.dateOfWeight = this.elements.dateOfWeightInput.value;
        }
        if (this.elements.dateOfBirthInput && this.elements.dateOfBirthInput.value !== undefined) {
            data.dateOfBirth = this.elements.dateOfBirthInput.value;
        }
        if (this.elements.healthcareNotesInput && this.elements.healthcareNotesInput.value !== undefined) {
            data.healthcareNotes = this.elements.healthcareNotesInput.value.trim();
        }
        if (this.elements.vaccinationInput && this.elements.vaccinationInput.value !== undefined) {
            data.takenVaccination = this.elements.vaccinationInput.value.trim();
        }

        // Ensure required fields have default values if missing
        if (!data.code) data.code = '';
        if (!data.type) data.type = '';
        if (!data.gender) data.gender = '';

        return data;
    }

    /**
     * Validate entire form
     */
    validateForm() {
        const data = this.collectFormData();
        const errors = this.validationManager.validateFormData(data);

        // Clear all previous errors
        this.uiManager.clearAllFieldErrors();

        if (errors.length > 0) {
            // Show first error as notification
            this.uiManager.showNotification(errors[0], 'error');

            // Show field-specific errors
            this.showFieldErrors(errors);

            return false;
        }

        return true;
    }

    /**
     * Show field-specific errors
     */
    showFieldErrors(errors) {
        const fieldMap = {
            code: this.elements.codeInput,
            herdNumber: this.elements.herdNumberInput,
            weight: this.elements.weightInput,
            dateOfWeight: this.elements.dateOfWeightInput,
            dateOfBirth: this.elements.dateOfBirthInput,
            healthcareNotes: this.elements.healthcareNotesInput,
            takenVaccination: this.elements.vaccinationInput
        };

        // Map errors to fields
        errors.forEach(error => {
            Object.entries(fieldMap).forEach(([fieldName, element]) => {
                if (error.toLowerCase().includes(fieldName.toLowerCase()) ||
                    error.toLowerCase().includes(this.validationManager.getFieldDisplayName(fieldName).toLowerCase())) {
                    if (element) {
                        this.uiManager.showFieldError(element, error);
                    }
                }
            });
        });
    }

    /**
     * Handle save button click
     */
    async handleSave() {
        console.log('💾 Save button clicked');

        if (!this.validateForm()) {
            console.log('❌ Form validation failed');
            return;
        }

        const formData = this.collectFormData();
        console.log('📝 Form data collected:', formData);

        // Show loading state
        this.uiManager.showLoading(this.elements.saveButton);

        try {
            // Save the animal
            const savedAnimal = this.dataManager.addAnimal(formData);

            console.log('✅ Animal saved successfully:', savedAnimal);

            // Show success message
            this.uiManager.showNotification(
                `Animal "${savedAnimal.code}" saved successfully! Saved to ${this.getTableInfo(savedAnimal.type)}.`,
                'success',
                4000
            );

            // Reset form after delay
            setTimeout(() => {
                this.resetForm();
            }, 2000);

            // Trigger storage event for other pages to update
            window.dispatchEvent(new StorageEvent('storage', {
                key: 'animals',
                newValue: JSON.stringify(this.dataManager.getData('animals'))
            }));

        } catch (error) {
            console.error('❌ Error saving animal:', error);
            this.uiManager.showNotification(
                `Failed to save animal: ${error.message}`,
                'error'
            );
        } finally {
            // Hide loading state
            this.uiManager.hideLoading(this.elements.saveButton);
        }
    }

    /**
     * Get table information for display
     */
    getTableInfo(type) {
        switch (type) {
            case 'dairy':
                return 'Animals Table + Dairy Table';
            case 'newborn':
                return 'Animals Table + Newborn Table';
            case 'fattening':
                return 'Animals Table';
            default:
                return 'Animals Table';
        }
    }

    /**
     * Reset form to initial state
     */
    resetForm() {
        // Clear form data
        this.formData = {};

        // Clear all input fields
        Object.values(this.elements).forEach(element => {
            if (element && element.value !== undefined) {
                element.value = '';
            } else if (element && element.length) {
                // Handle NodeList
                element.forEach(el => {
                    if (el.value !== undefined) el.value = '';
                    if (el.checked !== undefined) el.checked = false;
                });
            }
        });

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        // Reset required field indicators
        document.querySelectorAll('.required-indicator').forEach(el => el.remove());
        this.markRequiredFields();

        console.log('🔄 Form reset to initial state');
    }

    /**
     * Get current statistics
     */
    getStatistics() {
        return this.dataManager.getStatistics();
    }

    /**
     * Export data
     */
    exportData() {
        return this.dataManager.exportData();
    }

    /**
     * Clear all data
     */
    clearAllData() {
        const confirmed = confirm('Are you sure you want to clear all animal data? This action cannot be undone.');
        if (confirmed) {
            this.dataManager.clearAllData();
            this.uiManager.showNotification('All animal data cleared', 'info');
        }
    }

    /**
     * Validate data integrity
     */
    validateDataIntegrity() {
        return this.dataManager.validateDataIntegrity();
    }
}

// ==================== INITIALIZATION ====================

/**
 * Initialize the Add New Animal system when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing Add New Animal system...');

    try {
        // Create and initialize the controller
        const addNewAnimal = new AddNewAnimal();
        addNewAnimal.init();

        // Make it globally accessible for debugging and external access
        window.addNewAnimal = addNewAnimal;

        console.log('🎉 Add New Animal system ready!');
        console.log('Available global methods:');
        console.log('- window.addNewAnimal.getStatistics()');
        console.log('- window.addNewAnimal.exportData()');
        console.log('- window.addNewAnimal.validateDataIntegrity()');
        console.log('- window.addNewAnimal.clearAllData()');

    } catch (error) {
        console.error('💥 Failed to initialize Add New Animal system:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        errorDiv.textContent = 'Failed to initialize the form. Please refresh the page.';
        document.body.appendChild(errorDiv);
    }
});

// ==================== UTILITY FUNCTIONS ====================

/**
 * Add sample animal for testing
 */
window.addSampleAnimal = function() {
    if (!window.addNewAnimal) {
        console.error('Add New Animal system not initialized');
        return;
    }

    const sampleData = {
        code: 'SAMPLE' + Date.now(),
        type: 'dairy',
        gender: 'female',
        herdNumber: 'H999',
        weight: '450',
        dateOfWeight: new Date().toISOString().split('T')[0],
        dateOfBirth: '2022-01-15',
        healthcareNotes: 'Sample animal for testing',
        takenVaccination: 'Annual vaccination completed'
    };

    try {
        const result = window.addNewAnimal.dataManager.addAnimal(sampleData);
        console.log('✅ Sample animal added:', result);
        window.addNewAnimal.uiManager.showNotification('Sample animal added successfully!', 'success');
        return result;
    } catch (error) {
        console.error('❌ Error adding sample animal:', error);
        window.addNewAnimal.uiManager.showNotification('Error adding sample animal: ' + error.message, 'error');
        return null;
    }
};

/**
 * Debug function to check system status
 */
window.debugAddNewAnimal = function() {
    console.log('=== Add New Animal Debug Info ===');

    if (!window.addNewAnimal) {
        console.error('❌ Add New Animal system not initialized');
        return;
    }

    console.log('✅ System initialized');
    console.log('📊 Statistics:', window.addNewAnimal.getStatistics());
    console.log('🔍 Data integrity:', window.addNewAnimal.validateDataIntegrity());
    console.log('💾 Current form data:', window.addNewAnimal.formData);
    console.log('=== End Debug Info ===');
};

// Close button functionality removed - handled by external close button in animal.js

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        AddNewAnimal,
        AnimalDataManager,
        ValidationManager,
        UIManager,
        BaseDataManager
    };
}