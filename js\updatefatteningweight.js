/**
 * Update Fattening Weight Page - Complete OOP Implementation
 * This file provides comprehensive functionality for updating fattening animal weight data
 * with real-time search, growth tracking, feed efficiency calculations, and comprehensive validation
 *
 * @author: Animal Management System
 * @version: 1.0.0
 * @description: Object-oriented system for updating fattening animal weight data with growth monitoring
 */

// ==================== BASE CLASSES ====================

/**
 * Base Fattening Data Manager for localStorage operations
 * Provides common functionality for fattening animal data management
 */
class BaseFatteningDataManager {
    constructor() {
        this.storageKeys = {
            animals: 'animals',
            fatteningWeights: 'fatteningWeights',
            fatteningRecords: 'fatteningRecords'
        };
    }

    /**
     * Get data from localStorage with error handling
     * @param {string} key - Storage key
     * @returns {Array} - Array of data or empty array
     */
    getData(key) {
        try {
            const data = localStorage.getItem(this.storageKeys[key] || key);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`Error getting data for key ${key}:`, error);
            return [];
        }
    }

    /**
     * Save data to localStorage with error handling
     * @param {string} key - Storage key
     * @param {Array} data - Data to save
     * @returns {boolean} - Success status
     */
    saveData(key, data) {
        try {
            localStorage.setItem(this.storageKeys[key] || key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error(`Error saving data for key ${key}:`, error);
            return false;
        }
    }

    /**
     * Generate unique ID
     * @returns {string} - Unique identifier
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    /**
     * Find fattening animal by code (case-insensitive)
     * @param {string} code - Animal code to search for
     * @returns {Object|null} - Found fattening animal or null
     */
    findFatteningAnimalByCode(code) {
        if (!code || typeof code !== 'string') {
            return null;
        }

        const animals = this.getData('animals');
        return animals.find(animal =>
            animal && animal.code && typeof animal.code === 'string' &&
            animal.type === 'fattening' &&
            animal.code.toLowerCase() === code.toLowerCase()
        ) || null;
    }

    /**
     * Check if animal is fattening type
     * @param {string} code - Animal code to check
     * @returns {boolean} - True if animal is fattening type
     */
    isFatteningAnimal(code) {
        const animal = this.findFatteningAnimalByCode(code);
        return animal !== null;
    }

    /**
     * Get weight history for fattening animal
     * @param {string} animalCode - Animal code
     * @returns {Array} - Array of weight records sorted by date
     */
    getWeightHistory(animalCode) {
        const weightRecords = this.getData('fatteningWeights');
        const animalRecords = weightRecords.filter(record =>
            record.animalCode && record.animalCode.toLowerCase() === animalCode.toLowerCase()
        );

        // Sort by date (newest first)
        return animalRecords.sort((a, b) => new Date(b.date) - new Date(a.date));
    }

    /**
     * Calculate weight gain statistics
     * @param {Array} weightHistory - Array of weight records
     * @returns {Object} - Weight gain statistics
     */
    calculateWeightGainStats(weightHistory) {
        if (!weightHistory || weightHistory.length < 2) {
            return {
                totalGain: 0,
                averageDailyGain: 0,
                daysTracked: 0,
                initialWeight: weightHistory.length > 0 ? weightHistory[weightHistory.length - 1].weight : 0,
                currentWeight: weightHistory.length > 0 ? weightHistory[0].weight : 0,
                gainPercentage: 0
            };
        }

        const sortedHistory = [...weightHistory].sort((a, b) => new Date(a.date) - new Date(b.date));
        const initialRecord = sortedHistory[0];
        const latestRecord = sortedHistory[sortedHistory.length - 1];

        const initialWeight = parseFloat(initialRecord.weight) || 0;
        const currentWeight = parseFloat(latestRecord.weight) || 0;
        const totalGain = currentWeight - initialWeight;

        const startDate = new Date(initialRecord.date);
        const endDate = new Date(latestRecord.date);
        const daysTracked = Math.max(1, Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)));

        const averageDailyGain = totalGain / daysTracked;
        const gainPercentage = initialWeight > 0 ? (totalGain / initialWeight) * 100 : 0;

        return {
            totalGain: Math.round(totalGain * 100) / 100,
            averageDailyGain: Math.round(averageDailyGain * 1000) / 1000,
            daysTracked,
            initialWeight,
            currentWeight,
            gainPercentage: Math.round(gainPercentage * 100) / 100
        };
    }

    /**
     * Calculate feed efficiency (if feed data available)
     * @param {string} animalCode - Animal code
     * @param {number} feedConsumed - Feed consumed in kg
     * @param {number} weightGain - Weight gain in kg
     * @returns {number} - Feed conversion ratio (FCR)
     */
    calculateFeedEfficiency(animalCode, feedConsumed, weightGain) {
        if (!feedConsumed || !weightGain || weightGain <= 0) {
            return 0;
        }

        // Feed Conversion Ratio = Feed Consumed / Weight Gain
        // Lower FCR is better (less feed needed per kg of weight gain)
        return Math.round((feedConsumed / weightGain) * 100) / 100;
    }

    /**
     * Estimate target weight based on age and breed
     * @param {Object} animal - Animal data
     * @returns {Object} - Target weight information
     */
    estimateTargetWeight(animal) {
        if (!animal || !animal.dateOfBirth) {
            return { targetWeight: 0, daysToTarget: 0, recommendedDailyGain: 0 };
        }

        const birthDate = new Date(animal.dateOfBirth);
        const currentDate = new Date();
        const ageInDays = Math.ceil((currentDate - birthDate) / (1000 * 60 * 60 * 24));
        const ageInMonths = ageInDays / 30.44; // Average days per month

        // Typical fattening targets (can be customized based on breed)
        let targetWeight = 0;
        let targetAgeMonths = 18; // Default target age for slaughter

        // Estimate target weight based on age and typical growth patterns
        if (ageInMonths < 6) {
            targetWeight = 200; // Young calf target
            targetAgeMonths = 6;
        } else if (ageInMonths < 12) {
            targetWeight = 400; // Growing steer target
            targetAgeMonths = 12;
        } else {
            targetWeight = 600; // Mature fattening target
            targetAgeMonths = 18;
        }

        const targetAgeInDays = targetAgeMonths * 30.44;
        const daysToTarget = Math.max(0, targetAgeInDays - ageInDays);

        const currentWeight = parseFloat(animal.weight) || 0;
        const weightToGain = Math.max(0, targetWeight - currentWeight);
        const recommendedDailyGain = daysToTarget > 0 ? weightToGain / daysToTarget : 0;

        return {
            targetWeight,
            daysToTarget: Math.ceil(daysToTarget),
            recommendedDailyGain: Math.round(recommendedDailyGain * 1000) / 1000,
            currentAge: Math.ceil(ageInDays),
            targetAge: Math.ceil(targetAgeInDays)
        };
    }
}

/**
 * Validation Manager for fattening animal validation
 * Handles all validation logic with comprehensive rules for fattening operations
 */
class FatteningValidationManager {
    constructor() {
        this.rules = {
            required: ['code'],
            fatteningRequired: ['weight', 'dateOfWeight'],
            weightLimits: {
                minimum: 50,  // Minimum weight for fattening animal (kg)
                maximum: 1500, // Maximum realistic weight (kg)
                dailyGainMax: 3.0, // Maximum realistic daily gain (kg)
                dailyLossMax: 2.0  // Maximum acceptable daily loss (kg)
            }
        };
    }

    /**
     * Validate single field with comprehensive rules
     * @param {string} fieldName - Name of the field
     * @param {*} value - Value to validate
     * @param {Object} formData - Complete form data for context
     * @param {Object} previousWeight - Previous weight record for comparison
     * @returns {Array} - Array of error messages
     */
    validateField(fieldName, value, formData = {}, previousWeight = null) {
        const errors = [];

        // Required field validation
        if (this.rules.required.includes(fieldName) && !value) {
            errors.push(`${this.getFieldDisplayName(fieldName)} is required`);
        }

        // Fattening required fields
        if (this.rules.fatteningRequired.includes(fieldName) && !value) {
            errors.push(`${this.getFieldDisplayName(fieldName)} is required for fattening animals`);
        }

        // Specific field validations
        switch (fieldName) {
            case 'code':
                if (value && !/^[A-Za-z0-9]+$/.test(value)) {
                    errors.push('Code can only contain letters and numbers');
                }
                if (value && value.length > 20) {
                    errors.push('Code cannot be longer than 20 characters');
                }
                break;
            case 'weight':
                if (value) {
                    const weight = parseFloat(value);
                    if (isNaN(weight) || weight <= 0) {
                        errors.push('Weight must be a positive number');
                    } else if (weight < this.rules.weightLimits.minimum) {
                        errors.push(`Weight seems too low for fattening animal (min ${this.rules.weightLimits.minimum}kg)`);
                    } else if (weight > this.rules.weightLimits.maximum) {
                        errors.push(`Weight seems unrealistic (max ${this.rules.weightLimits.maximum}kg)`);
                    }

                    // Validate weight change if previous weight available
                    if (previousWeight && formData.dateOfWeight) {
                        const prevWeight = parseFloat(previousWeight.weight);
                        const prevDate = new Date(previousWeight.date);
                        const currentDate = new Date(formData.dateOfWeight);
                        const daysDiff = Math.max(1, (currentDate - prevDate) / (1000 * 60 * 60 * 24));

                        if (daysDiff > 0) {
                            const dailyChange = (weight - prevWeight) / daysDiff;

                            if (dailyChange > this.rules.weightLimits.dailyGainMax) {
                                errors.push(`Daily weight gain seems too high (${dailyChange.toFixed(2)}kg/day, max ${this.rules.weightLimits.dailyGainMax}kg/day)`);
                            } else if (dailyChange < -this.rules.weightLimits.dailyLossMax) {
                                errors.push(`Daily weight loss seems concerning (${Math.abs(dailyChange).toFixed(2)}kg/day loss)`);
                            }
                        }
                    }
                }
                break;
            case 'dateOfWeight':
                if (value) {
                    const weightDate = new Date(value);
                    const today = new Date();
                    today.setHours(23, 59, 59, 999); // End of today

                    if (weightDate > today) {
                        errors.push('Date of weight cannot be in the future');
                    }

                    // Check if date is too far in the past
                    const oneYearAgo = new Date();
                    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
                    if (weightDate < oneYearAgo) {
                        errors.push('Date of weight cannot be more than 1 year ago');
                    }

                    // Validate against previous weight date
                    if (previousWeight) {
                        const prevDate = new Date(previousWeight.date);
                        if (weightDate <= prevDate) {
                            errors.push('Date of weight must be after the previous weight record');
                        }
                    }
                }
                break;
            case 'healthcareNotes':
                if (value && value.length > 500) {
                    errors.push('Healthcare notes cannot be longer than 500 characters');
                }
                break;
        }

        return errors;
    }

    /**
     * Validate entire form data with cross-field validation
     * @param {Object} data - Form data to validate
     * @param {Object} previousWeight - Previous weight record
     * @returns {Array} - Array of error messages
     */
    validateFormData(data, previousWeight = null) {
        const errors = [];

        // Validate all fields
        Object.entries(data).forEach(([field, value]) => {
            const fieldErrors = this.validateField(field, value, data, previousWeight);
            errors.push(...fieldErrors);
        });

        // Cross-field validations
        if (data.weight && data.dateOfWeight) {
            // Additional business rule validations can be added here
        }

        return errors;
    }

    /**
     * Get display name for field
     * @param {string} fieldName - Field name
     * @returns {string} - Human-readable field name
     */
    getFieldDisplayName(fieldName) {
        const displayNames = {
            code: 'Code',
            weight: 'Weight',
            dateOfWeight: 'Date of Weight',
            healthcareNotes: 'Healthcare Notes'
        };
        return displayNames[fieldName] || fieldName;
    }
}

/**
 * UI Manager for user interface operations
 * Handles all UI interactions, notifications, and visual feedback for fattening operations
 */
class FatteningUIManager {
    constructor() {
        this.notifications = [];
    }

    /**
     * Show field error with visual styling
     * @param {HTMLElement} fieldElement - Form field element
     * @param {string} message - Error message to display
     */
    showFieldError(fieldElement, message) {
        this.clearFieldError(fieldElement);

        // Add error styling
        fieldElement.style.borderColor = '#dc3545';
        fieldElement.style.backgroundColor = '#fff5f5';

        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.style.cssText = `
            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
            padding: 2px 0;
            animation: shake 0.3s ease-in-out;
        `;
        errorDiv.textContent = message;

        // Insert after field
        fieldElement.parentNode.insertBefore(errorDiv, fieldElement.nextSibling);
    }

    /**
     * Clear field error styling and message
     * @param {HTMLElement} fieldElement - Form field element
     */
    clearFieldError(fieldElement) {
        // Reset styling
        fieldElement.style.borderColor = '';
        fieldElement.style.backgroundColor = '';

        // Remove error message
        const errorDiv = fieldElement.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * Clear all field errors
     */
    clearAllFieldErrors() {
        const errorDivs = document.querySelectorAll('.field-error');
        errorDivs.forEach(div => div.remove());

        const fields = document.querySelectorAll('.data-filled, .textarea');
        fields.forEach(field => {
            field.style.borderColor = '';
            field.style.backgroundColor = '';
        });
    }

    /**
     * Show notification with different types and auto-dismiss
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, warning, info)
     * @param {number} duration - Auto-dismiss duration in milliseconds
     * @returns {HTMLElement} - Notification element
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        this.clearNotifications();

        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            animation: slideIn 0.3s ease-out;
        `;

        // Set colors based on type
        const colors = {
            success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
            error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
            warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
            info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
        };

        const color = colors[type] || colors.info;
        notification.style.backgroundColor = color.bg;
        notification.style.border = `1px solid ${color.border}`;
        notification.style.color = color.text;

        notification.textContent = message;

        // Add close button
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            float: right;
            margin-left: 10px;
            cursor: pointer;
            font-size: 18px;
            line-height: 1;
        `;
        closeBtn.onclick = () => notification.remove();
        notification.appendChild(closeBtn);

        document.body.appendChild(notification);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }

        // Add animation styles if not already present
        if (!document.querySelector('#fattening-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'fattening-notification-styles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-5px); }
                    75% { transform: translateX(5px); }
                }
            `;
            document.head.appendChild(style);
        }

        this.notifications.push(notification);
        return notification;
    }

    /**
     * Clear all notifications
     */
    clearNotifications() {
        this.notifications.forEach(notification => {
            if (notification.parentNode) {
                notification.remove();
            }
        });
        this.notifications = [];
    }

    /**
     * Show loading state on button
     * @param {HTMLElement} button - Button element
     * @param {string} text - Loading text
     */
    showLoading(button, text = 'Saving...') {
        if (!button) return;

        button.disabled = true;
        const btnText = button.querySelector('.btntext');
        if (btnText) {
            button.dataset.originalText = btnText.textContent;
            btnText.textContent = text;
        }
        button.style.opacity = '0.7';
    }

    /**
     * Hide loading state on button
     * @param {HTMLElement} button - Button element
     */
    hideLoading(button) {
        if (!button) return;

        button.disabled = false;
        const btnText = button.querySelector('.btntext');
        if (btnText && button.dataset.originalText) {
            btnText.textContent = button.dataset.originalText;
            delete button.dataset.originalText;
        }
        button.style.opacity = '1';
    }

    /**
     * Highlight field with success styling
     * @param {HTMLElement} fieldElement - Form field element
     */
    highlightFieldSuccess(fieldElement) {
        fieldElement.style.borderColor = '#28a745';
        fieldElement.style.backgroundColor = '#f8fff9';

        setTimeout(() => {
            fieldElement.style.borderColor = '';
            fieldElement.style.backgroundColor = '';
        }, 2000);
    }

    /**
     * Show fattening-specific status indicator
     * @param {Object} stats - Weight gain statistics
     * @param {HTMLElement} container - Container element
     */
    showFatteningStatus(stats, container) {
        if (!container || !stats) return;

        const statusIndicator = document.createElement('div');
        statusIndicator.className = 'fattening-status-indicator';
        statusIndicator.style.cssText = `
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 8px;
        `;

        let statusConfig;
        if (stats.averageDailyGain > 1.0) {
            statusConfig = { bg: '#d4edda', text: '#155724', label: '📈 Excellent Growth' };
        } else if (stats.averageDailyGain > 0.5) {
            statusConfig = { bg: '#d1ecf1', text: '#0c5460', label: '📊 Good Growth' };
        } else if (stats.averageDailyGain > 0) {
            statusConfig = { bg: '#fff3cd', text: '#856404', label: '⚠️ Slow Growth' };
        } else {
            statusConfig = { bg: '#f8d7da', text: '#721c24', label: '❌ Weight Loss' };
        }

        statusIndicator.style.backgroundColor = statusConfig.bg;
        statusIndicator.style.color = statusConfig.text;
        statusIndicator.textContent = statusConfig.label;

        container.appendChild(statusIndicator);
    }

    /**
     * Show weight gain statistics in a visual format
     * @param {Object} stats - Weight gain statistics
     * @param {HTMLElement} container - Container to show stats
     */
    showWeightGainStats(stats, container) {
        if (!container || !stats) return;

        const statsDiv = document.createElement('div');
        statsDiv.className = 'weight-gain-stats';
        statsDiv.style.cssText = `
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 12px;
            margin: 10px 0;
            font-size: 12px;
            line-height: 1.4;
        `;

        statsDiv.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 8px; color: #495057;">📊 Weight Gain Statistics</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px;">
                <div><strong>Total Gain:</strong> ${stats.totalGain}kg</div>
                <div><strong>Daily Avg:</strong> ${stats.averageDailyGain}kg/day</div>
                <div><strong>Days Tracked:</strong> ${stats.daysTracked}</div>
                <div><strong>Gain %:</strong> ${stats.gainPercentage}%</div>
            </div>
        `;

        container.appendChild(statsDiv);
    }
}

// ==================== MAIN CLASSES ====================

/**
 * Fattening Data Manager - Handles all data operations for updating fattening animals
 * Extends BaseFatteningDataManager with update-specific functionality
 */
class FatteningDataManager extends BaseFatteningDataManager {
    constructor() {
        super();
    }

    /**
     * Update fattening animal weight in main table and add weight record
     * @param {string} animalCode - Code of animal to update
     * @param {Object} weightData - New weight data for the fattening animal
     * @returns {Object} - Updated animal object and weight record
     */
    updateFatteningWeight(animalCode, weightData) {
        if (!animalCode) {
            throw new Error('Animal code is required for update');
        }

        if (!weightData) {
            throw new Error('Weight data is required');
        }

        // Verify this is a fattening animal
        if (!this.isFatteningAnimal(animalCode)) {
            throw new Error(`Animal "${animalCode}" is not a fattening animal or does not exist`);
        }

        const timestamp = new Date().toISOString();

        try {
            // 1. Update main animals table
            const updatedAnimal = this.updateMainAnimalTable(animalCode, weightData, timestamp);

            // 2. Add weight record to tracking history
            const weightRecord = this.addWeightRecord(animalCode, weightData, timestamp);

            console.log(`✅ Fattening animal ${animalCode} weight updated successfully:`, {
                mainTable: true,
                weightRecord: true,
                newWeight: weightData.weight
            });

            return {
                animal: updatedAnimal,
                weightRecord: weightRecord
            };

        } catch (error) {
            console.error('❌ Error updating fattening animal weight:', error);
            throw error;
        }
    }

    /**
     * Update animal in main animals table
     * @param {string} animalCode - Animal code
     * @param {Object} weightData - Weight data
     * @param {string} timestamp - Update timestamp
     * @returns {Object} - Updated animal
     */
    updateMainAnimalTable(animalCode, weightData, timestamp) {
        const animals = this.getData('animals');
        const animalIndex = animals.findIndex(a =>
            a.code && a.code.toLowerCase() === animalCode.toLowerCase()
        );

        if (animalIndex === -1) {
            throw new Error(`Animal with code "${animalCode}" not found in main table`);
        }

        const currentAnimal = animals[animalIndex];

        // Create updated animal object (update weight and related fields)
        const updatedAnimal = {
            ...currentAnimal,
            weight: weightData.weight,
            dateOfWeight: weightData.dateOfWeight,
            healthcareNotes: weightData.healthcareNotes || currentAnimal.healthcareNotes,
            updatedAt: timestamp
        };

        // Update in array
        animals[animalIndex] = updatedAnimal;

        if (!this.saveData('animals', animals)) {
            throw new Error('Failed to update main animals table');
        }

        console.log(`📋 Updated in main animals table: ${animalCode}`);
        return updatedAnimal;
    }

    /**
     * Add weight record to fattening weights tracking
     * @param {string} animalCode - Animal code
     * @param {Object} weightData - Weight data
     * @param {string} timestamp - Record timestamp
     * @returns {Object} - Created weight record
     */
    addWeightRecord(animalCode, weightData, timestamp) {
        const weightRecords = this.getData('fatteningWeights');

        const weightRecord = {
            id: this.generateId(),
            animalCode: animalCode,
            weight: parseFloat(weightData.weight),
            date: weightData.dateOfWeight,
            healthcareNotes: weightData.healthcareNotes || '',
            createdAt: timestamp
        };

        weightRecords.push(weightRecord);

        if (!this.saveData('fatteningWeights', weightRecords)) {
            throw new Error('Failed to save weight record');
        }

        console.log(`📊 Added weight record for ${animalCode}: ${weightRecord.weight}kg on ${weightRecord.date}`);
        return weightRecord;
    }

    /**
     * Get complete fattening animal data with weight history and statistics
     * @param {string} animalCode - Animal code
     * @returns {Object} - Complete fattening animal data
     */
    getCompleteFatteningAnimalData(animalCode) {
        const animal = this.findFatteningAnimalByCode(animalCode);

        if (!animal) {
            return null;
        }

        const weightHistory = this.getWeightHistory(animalCode);
        const weightStats = this.calculateWeightGainStats(weightHistory);
        const targetInfo = this.estimateTargetWeight(animal);

        return {
            ...animal,
            weightHistory: weightHistory,
            weightStats: weightStats,
            targetInfo: targetInfo,
            lastWeightRecord: weightHistory.length > 0 ? weightHistory[0] : null,
            totalWeightRecords: weightHistory.length
        };
    }

    /**
     * Get previous weight record for validation
     * @param {string} animalCode - Animal code
     * @returns {Object|null} - Previous weight record or null
     */
    getPreviousWeightRecord(animalCode) {
        const weightHistory = this.getWeightHistory(animalCode);
        return weightHistory.length > 0 ? weightHistory[0] : null;
    }

    /**
     * Search fattening animals by various criteria
     * @param {string} searchTerm - Search term
     * @param {string} searchField - Field to search in (optional)
     * @returns {Array} - Matching fattening animals
     */
    searchFatteningAnimals(searchTerm, searchField = null) {
        if (!searchTerm) {
            const animals = this.getData('animals');
            return animals.filter(animal => animal.type === 'fattening');
        }

        const animals = this.getData('animals');
        const fatteningAnimals = animals.filter(animal => animal.type === 'fattening');
        const term = searchTerm.toLowerCase();

        return fatteningAnimals.filter(animal => {
            if (searchField) {
                const fieldValue = animal[searchField];
                return fieldValue && fieldValue.toString().toLowerCase().includes(term);
            } else {
                // Search across multiple fields
                const searchableFields = ['code', 'healthcareNotes'];
                return searchableFields.some(field => {
                    const fieldValue = animal[field];
                    return fieldValue && fieldValue.toString().toLowerCase().includes(term);
                });
            }
        });
    }

    /**
     * Get fattening statistics
     * @returns {Object} - Statistics object
     */
    getFatteningStatistics() {
        const animals = this.getData('animals');
        const fatteningAnimals = animals.filter(animal => animal.type === 'fattening');
        const weightRecords = this.getData('fatteningWeights');

        const totalFatteningAnimals = fatteningAnimals.length;
        const totalWeightRecords = weightRecords.length;

        // Calculate overall statistics
        let totalWeightGain = 0;
        let animalsWithGain = 0;
        let averageWeight = 0;

        fatteningAnimals.forEach(animal => {
            const weightHistory = this.getWeightHistory(animal.code);
            const stats = this.calculateWeightGainStats(weightHistory);

            if (stats.totalGain > 0) {
                totalWeightGain += stats.totalGain;
                animalsWithGain++;
            }

            if (animal.weight) {
                averageWeight += parseFloat(animal.weight);
            }
        });

        const averageWeightGain = animalsWithGain > 0 ? totalWeightGain / animalsWithGain : 0;
        averageWeight = totalFatteningAnimals > 0 ? averageWeight / totalFatteningAnimals : 0;

        // Performance categories
        const excellentPerformers = fatteningAnimals.filter(animal => {
            const weightHistory = this.getWeightHistory(animal.code);
            const stats = this.calculateWeightGainStats(weightHistory);
            return stats.averageDailyGain > 1.0;
        }).length;

        const goodPerformers = fatteningAnimals.filter(animal => {
            const weightHistory = this.getWeightHistory(animal.code);
            const stats = this.calculateWeightGainStats(weightHistory);
            return stats.averageDailyGain > 0.5 && stats.averageDailyGain <= 1.0;
        }).length;

        const poorPerformers = fatteningAnimals.filter(animal => {
            const weightHistory = this.getWeightHistory(animal.code);
            const stats = this.calculateWeightGainStats(weightHistory);
            return stats.averageDailyGain <= 0.5;
        }).length;

        return {
            totalFatteningAnimals,
            totalWeightRecords,
            averageWeight: Math.round(averageWeight * 100) / 100,
            averageWeightGain: Math.round(averageWeightGain * 100) / 100,
            excellentPerformers,
            goodPerformers,
            poorPerformers,
            performanceDistribution: {
                excellent: Math.round((excellentPerformers / totalFatteningAnimals) * 100) || 0,
                good: Math.round((goodPerformers / totalFatteningAnimals) * 100) || 0,
                poor: Math.round((poorPerformers / totalFatteningAnimals) * 100) || 0
            }
        };
    }

    /**
     * Get weight tracking recommendations for an animal
     * @param {string} animalCode - Animal code
     * @returns {Object} - Recommendations object
     */
    getWeightTrackingRecommendations(animalCode) {
        const animal = this.findFatteningAnimalByCode(animalCode);
        if (!animal) {
            return null;
        }

        const weightHistory = this.getWeightHistory(animalCode);
        const stats = this.calculateWeightGainStats(weightHistory);
        const targetInfo = this.estimateTargetWeight(animal);

        const recommendations = [];

        // Weight gain recommendations
        if (stats.averageDailyGain < 0.5) {
            recommendations.push({
                type: 'warning',
                message: 'Low weight gain detected. Consider reviewing feed quality and quantity.',
                action: 'Increase feed or consult veterinarian'
            });
        } else if (stats.averageDailyGain > 2.0) {
            recommendations.push({
                type: 'info',
                message: 'Excellent weight gain! Monitor for optimal feed efficiency.',
                action: 'Continue current feeding program'
            });
        }

        // Target weight recommendations
        if (targetInfo.daysToTarget > 0 && targetInfo.recommendedDailyGain > stats.averageDailyGain) {
            const gainIncrease = targetInfo.recommendedDailyGain - stats.averageDailyGain;
            recommendations.push({
                type: 'info',
                message: `To reach target weight, increase daily gain by ${gainIncrease.toFixed(3)}kg/day`,
                action: 'Adjust feeding program'
            });
        }

        // Tracking frequency recommendations
        const daysSinceLastRecord = weightHistory.length > 0 ?
            Math.ceil((new Date() - new Date(weightHistory[0].date)) / (1000 * 60 * 60 * 24)) : 0;

        if (daysSinceLastRecord > 14) {
            recommendations.push({
                type: 'warning',
                message: 'Weight not recorded recently. Regular monitoring recommended.',
                action: 'Record weight at least every 2 weeks'
            });
        }

        return {
            recommendations,
            currentPerformance: stats.averageDailyGain > 1.0 ? 'excellent' :
                               stats.averageDailyGain > 0.5 ? 'good' : 'poor',
            targetStatus: targetInfo.daysToTarget > 0 ? 'on-track' : 'target-reached'
        };
    }
}

/**
 * Main Update Fattening Weight Controller Class
 * Orchestrates all components and manages the update fattening weight page
 */
class UpdateFatteningWeight {
    constructor() {
        this.dataManager = new FatteningDataManager();
        this.validationManager = new FatteningValidationManager();
        this.uiManager = new FatteningUIManager();
        this.formData = {};
        this.currentFatteningAnimal = null;
        this.isInitialized = false;
        this.searchTimeout = null;
        this.notFoundTimeout = null;
    }

    /**
     * Initialize the controller
     */
    init() {
        try {
            console.log('🚀 Initializing Update Fattening Weight system...');

            this.setupFormElements();
            this.setupEventListeners();
            this.setupValidation();
            this.setupReadyMode();

            this.isInitialized = true;
            console.log('✅ Update Fattening Weight system initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize Update Fattening Weight system:', error);
            this.uiManager.showNotification('Failed to initialize the form. Please refresh the page.', 'error');
        }
    }

    /**
     * Setup form elements
     */
    setupFormElements() {
        this.elements = {
            // Code input serves as both search and editable field
            codeInput: document.querySelector('.data-filled'),

            // Input fields
            weightInput: document.querySelectorAll('.data-filled')[1],
            dateOfWeightInput: document.querySelectorAll('.data-filled')[2],

            // Textarea field
            healthcareNotesInput: document.querySelector('.textarea'),

            // Save button
            saveButton: document.querySelector('.frame-9')
        };

        // Validate that all elements exist
        const missingElements = [];
        Object.entries(this.elements).forEach(([key, element]) => {
            if (!element) {
                missingElements.push(key);
            }
        });

        if (missingElements.length > 0) {
            console.warn(`Some form elements not found: ${missingElements.join(', ')}`);
        }

        console.log('📋 Form elements found and mapped');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Code field handles both search and editing
        if (this.elements.codeInput) {
            // Real-time search as user types
            this.elements.codeInput.addEventListener('input', (e) => {
                const value = e.target.value.trim();

                // Update form data
                this.formData.code = value;

                // Clear field error when user starts typing
                this.uiManager.clearFieldError(e.target);

                // Perform real-time search
                this.handleRealTimeSearch(value);
            });

            // Handle Enter key for immediate search
            this.elements.codeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleSearch();
                }
            });

            // Handle focus to show instructions
            this.elements.codeInput.addEventListener('focus', () => {
                if (!this.currentFatteningAnimal) {
                    this.uiManager.showNotification('Start typing a fattening animal code to load data...', 'info', 2000);
                }
            });

            // Handle blur for validation
            this.elements.codeInput.addEventListener('blur', (e) => {
                this.validateSingleField(e.target, 'code');
            });
        }

        // Other input field changes
        const otherInputs = [
            { element: this.elements.weightInput, field: 'weight' },
            { element: this.elements.dateOfWeightInput, field: 'dateOfWeight' },
            { element: this.elements.healthcareNotesInput, field: 'healthcareNotes' }
        ];

        otherInputs.forEach(({ element, field }) => {
            if (element) {
                // Real-time validation on input
                element.addEventListener('input', (e) => {
                    this.handleInputChange(e, field);
                });

                // Validation on blur
                element.addEventListener('blur', (e) => {
                    this.validateSingleField(e.target, field);
                });
            }
        });

        // Save button
        if (this.elements.saveButton) {
            this.elements.saveButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleSave();
            });
        }

        console.log('🔗 Event listeners setup complete');
    }

    /**
     * Setup ready mode - enable fields for immediate editing
     */
    setupReadyMode() {
        this.setFormMode('ready');
        this.uiManager.showNotification('Start typing a fattening animal code to load and update weight data', 'info', 3000);
    }

    /**
     * Set form mode (ready or edit)
     * @param {string} mode - 'ready' or 'edit'
     */
    setFormMode(mode) {
        const formFields = [
            this.elements.weightInput,
            this.elements.dateOfWeightInput,
            this.elements.healthcareNotesInput
        ];

        if (mode === 'ready') {
            // Ready mode: All fields enabled but empty, waiting for fattening animal to be loaded

            // Code field is always enabled for search/edit
            if (this.elements.codeInput) {
                this.elements.codeInput.disabled = false;
                this.elements.codeInput.style.opacity = '1';
                this.elements.codeInput.placeholder = 'Type fattening animal code to load and update weight...';
            }

            // Other fields enabled but save button disabled
            formFields.forEach(field => {
                if (field) {
                    field.disabled = false;
                    field.style.opacity = '1';
                }
            });

            if (this.elements.saveButton) {
                this.elements.saveButton.disabled = true;
                this.elements.saveButton.style.opacity = '0.5';
                this.elements.saveButton.title = 'Load a fattening animal first';
            }

        } else if (mode === 'edit') {
            // All fields enabled including code field
            if (this.elements.codeInput) {
                this.elements.codeInput.disabled = false;
                this.elements.codeInput.style.opacity = '1';
                this.elements.codeInput.placeholder = 'Fattening animal code (editable)';
            }

            formFields.forEach(field => {
                if (field) {
                    field.disabled = false;
                    field.style.opacity = '1';
                }
            });

            if (this.elements.saveButton) {
                this.elements.saveButton.disabled = false;
                this.elements.saveButton.style.opacity = '1';
                this.elements.saveButton.title = 'Save weight data';
            }
        }
    }

    /**
     * Handle real-time search as user types
     * @param {string} searchTerm - The search term
     */
    handleRealTimeSearch(searchTerm) {
        // Clear any existing timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // If search term is empty, clear form and set to ready mode
        if (!searchTerm) {
            this.clearFormData();
            this.setFormMode('ready');
            return;
        }

        // Debounce the search to avoid too many lookups
        this.searchTimeout = setTimeout(() => {
            this.performRealTimeSearch(searchTerm);
        }, 300); // 300ms delay
    }

    /**
     * Perform the actual real-time search
     * @param {string} searchTerm - The search term
     */
    performRealTimeSearch(searchTerm) {
        console.log(`🔍 Real-time searching for fattening animal: ${searchTerm}`);

        // Check if this is a fattening animal
        if (!this.dataManager.isFatteningAnimal(searchTerm)) {
            this.clearFormData();
            this.setFormMode('ready');
            this.showNotFoundFeedback(searchTerm, 'not_fattening');
            console.log(`❌ Animal "${searchTerm}" is not a fattening animal or does not exist`);
            return;
        }

        // Get complete fattening animal data
        const fatteningAnimal = this.dataManager.getCompleteFatteningAnimalData(searchTerm);

        if (fatteningAnimal) {
            // Fattening animal found, load it into form immediately
            this.loadFatteningAnimalIntoForm(fatteningAnimal);
            this.setFormMode('edit');
            console.log(`✅ Fattening animal "${fatteningAnimal.code}" loaded automatically`);
        } else {
            // Fattening animal not found, clear form but keep fields enabled
            this.clearFormData();
            this.setFormMode('ready');
            this.showNotFoundFeedback(searchTerm, 'not_found');
            console.log(`❌ Fattening animal "${searchTerm}" not found`);
        }
    }

    /**
     * Handle manual search (when Enter is pressed)
     */
    handleSearch() {
        const searchTerm = this.elements.codeInput.value.trim();

        if (!searchTerm) {
            this.uiManager.showNotification('Please enter a fattening animal code to search', 'warning');
            return;
        }

        console.log(`🔍 Manual search for fattening animal: ${searchTerm}`);

        // Check if this is a fattening animal
        if (!this.dataManager.isFatteningAnimal(searchTerm)) {
            this.uiManager.showNotification(`Animal "${searchTerm}" is not a fattening animal or does not exist`, 'error');
            this.clearFormData();
            this.setFormMode('ready');
            return;
        }

        // Get complete fattening animal data
        const fatteningAnimal = this.dataManager.getCompleteFatteningAnimalData(searchTerm);

        if (!fatteningAnimal) {
            this.uiManager.showNotification(`Fattening animal with code "${searchTerm}" not found`, 'error');
            this.clearFormData();
            this.setFormMode('ready');
            return;
        }

        // Fattening animal found, load it into form
        this.loadFatteningAnimalIntoForm(fatteningAnimal);
        this.setFormMode('edit');
        this.uiManager.showNotification(`Fattening animal "${fatteningAnimal.code}" loaded successfully!`, 'success', 2000);
    }

    /**
     * Show visual feedback when animal is not found
     * @param {string} searchTerm - The search term that wasn't found
     * @param {string} reason - Reason for not found ('not_found' or 'not_fattening')
     */
    showNotFoundFeedback(searchTerm, reason = 'not_found') {
        if (this.elements.codeInput) {
            // Add red border to indicate not found
            this.elements.codeInput.style.borderColor = '#dc3545';
            this.elements.codeInput.style.backgroundColor = '#fff5f5';

            // Reset after 2 seconds
            setTimeout(() => {
                this.elements.codeInput.style.borderColor = '';
                this.elements.codeInput.style.backgroundColor = '';
            }, 2000);
        }

        // Only show notification for longer search terms to avoid spam
        if (searchTerm.length >= 3) {
            // Clear any existing notification timeout
            if (this.notFoundTimeout) {
                clearTimeout(this.notFoundTimeout);
            }

            // Show notification after a delay to avoid too many notifications
            this.notFoundTimeout = setTimeout(() => {
                const message = reason === 'not_fattening'
                    ? `"${searchTerm}" is not a fattening animal`
                    : `Fattening animal "${searchTerm}" not found`;
                this.uiManager.showNotification(message, 'warning', 2000);
            }, 1000);
        }
    }

    /**
     * Load fattening animal data into form
     * @param {Object} fatteningAnimal - Fattening animal data to load
     */
    loadFatteningAnimalIntoForm(fatteningAnimal) {
        this.currentFatteningAnimal = fatteningAnimal;
        this.formData = { ...fatteningAnimal };

        console.log('📝 Loading fattening animal into form:', fatteningAnimal);

        // Add visual feedback to code input
        if (this.elements.codeInput) {
            this.elements.codeInput.style.borderColor = '#28a745';
            this.elements.codeInput.style.backgroundColor = '#f8fff9';
            setTimeout(() => {
                this.elements.codeInput.style.borderColor = '';
                this.elements.codeInput.style.backgroundColor = '';
            }, 2000);
        }

        // Fill input fields with smooth animation
        this.fillFieldWithAnimation(this.elements.weightInput, fatteningAnimal.weight || '');
        this.fillFieldWithAnimation(this.elements.dateOfWeightInput, fatteningAnimal.dateOfWeight || new Date().toISOString().split('T')[0]);
        this.fillFieldWithAnimation(this.elements.healthcareNotesInput, fatteningAnimal.healthcareNotes || '');

        // Show fattening status indicator
        const titleElement = document.querySelector('.text-wrapper-5');
        if (titleElement) {
            // Remove existing status indicators
            const existingIndicators = titleElement.parentNode.querySelectorAll('.fattening-status-indicator');
            existingIndicators.forEach(indicator => indicator.remove());

            // Add new status indicator based on weight gain stats
            if (fatteningAnimal.weightStats) {
                this.uiManager.showFatteningStatus(fatteningAnimal.weightStats, titleElement.parentNode);
            }
        }

        // Show weight gain statistics if available
        if (fatteningAnimal.weightStats && fatteningAnimal.weightHistory.length > 1) {
            this.showWeightGainInfo(fatteningAnimal.weightStats, fatteningAnimal.targetInfo);
        }

        // Clear any previous errors
        this.uiManager.clearAllFieldErrors();
    }

    /**
     * Fill field with smooth animation
     * @param {HTMLElement} field - Field element
     * @param {string} value - Value to fill
     */
    fillFieldWithAnimation(field, value) {
        if (!field) return;

        // Add loading animation
        field.style.transition = 'all 0.3s ease';
        field.style.backgroundColor = '#e3f2fd';

        setTimeout(() => {
            field.value = value;
            field.style.backgroundColor = '#f8fff9';

            setTimeout(() => {
                field.style.backgroundColor = '';
                field.style.transition = '';
            }, 500);
        }, 100);
    }

    /**
     * Show weight gain information
     * @param {Object} weightStats - Weight gain statistics
     * @param {Object} targetInfo - Target weight information
     */
    showWeightGainInfo(weightStats, targetInfo) {
        // Create or update weight info display
        let weightInfoDiv = document.querySelector('.weight-gain-info');
        if (!weightInfoDiv) {
            weightInfoDiv = document.createElement('div');
            weightInfoDiv.className = 'weight-gain-info';
            weightInfoDiv.style.cssText = `
                background: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 12px;
                margin: 10px 0;
                font-size: 12px;
                line-height: 1.4;
            `;

            // Insert after the form
            const form = document.querySelector('.frame-6');
            if (form) {
                form.appendChild(weightInfoDiv);
            }
        }

        weightInfoDiv.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 8px; color: #495057;">📊 Weight Tracking Summary</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; margin-bottom: 8px;">
                <div><strong>Current Weight:</strong> ${weightStats.currentWeight}kg</div>
                <div><strong>Total Gain:</strong> ${weightStats.totalGain}kg</div>
                <div><strong>Daily Average:</strong> ${weightStats.averageDailyGain}kg/day</div>
                <div><strong>Days Tracked:</strong> ${weightStats.daysTracked}</div>
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 8px; padding-top: 8px; border-top: 1px solid #dee2e6;">
                <div><strong>Target Weight:</strong> ${targetInfo.targetWeight}kg</div>
                <div><strong>Days to Target:</strong> ${targetInfo.daysToTarget}</div>
            </div>
        `;
    }

    /**
     * Handle input field changes
     */
    handleInputChange(event, fieldName) {
        const value = event.target.value.trim();
        this.formData[fieldName] = value;

        // Clear field error when user starts typing
        this.uiManager.clearFieldError(event.target);

        // Auto-set date of weight to today if not set
        if (fieldName === 'weight' && value && !this.elements.dateOfWeightInput.value) {
            this.elements.dateOfWeightInput.value = new Date().toISOString().split('T')[0];
            this.formData.dateOfWeight = this.elements.dateOfWeightInput.value;
        }
    }

    /**
     * Validate single field
     */
    validateSingleField(fieldElement, fieldName) {
        const value = fieldElement.value.trim();
        const previousWeight = this.currentFatteningAnimal ?
            this.dataManager.getPreviousWeightRecord(this.currentFatteningAnimal.code) : null;

        const errors = this.validationManager.validateField(fieldName, value, this.formData, previousWeight);

        if (errors.length > 0) {
            this.uiManager.showFieldError(fieldElement, errors[0]);
            return false;
        } else {
            this.uiManager.clearFieldError(fieldElement);
            return true;
        }
    }

    /**
     * Collect all form data
     */
    collectFormData() {
        const data = { ...this.formData };

        // Collect input field values safely
        if (this.elements.codeInput && this.elements.codeInput.value !== undefined) {
            data.code = this.elements.codeInput.value.trim();
        }
        if (this.elements.weightInput && this.elements.weightInput.value !== undefined) {
            data.weight = this.elements.weightInput.value.trim();
        }
        if (this.elements.dateOfWeightInput && this.elements.dateOfWeightInput.value !== undefined) {
            data.dateOfWeight = this.elements.dateOfWeightInput.value;
        }
        if (this.elements.healthcareNotesInput && this.elements.healthcareNotesInput.value !== undefined) {
            data.healthcareNotes = this.elements.healthcareNotesInput.value.trim();
        }

        // Ensure required fields have default values if missing
        if (!data.code) data.code = '';
        if (!data.dateOfWeight) data.dateOfWeight = new Date().toISOString().split('T')[0];

        return data;
    }

    /**
     * Validate entire form
     */
    validateForm() {
        if (!this.currentFatteningAnimal) {
            this.uiManager.showNotification('No fattening animal loaded for weight update', 'error');
            return false;
        }

        const data = this.collectFormData();
        const previousWeight = this.dataManager.getPreviousWeightRecord(this.currentFatteningAnimal.code);
        const errors = this.validationManager.validateFormData(data, previousWeight);

        // Clear all previous errors
        this.uiManager.clearAllFieldErrors();

        if (errors.length > 0) {
            // Show first error as notification
            this.uiManager.showNotification(errors[0], 'error');

            // Show field-specific errors
            this.showFieldErrors(errors);

            return false;
        }

        return true;
    }

    /**
     * Show field-specific errors
     */
    showFieldErrors(errors) {
        const fieldMap = {
            code: this.elements.codeInput,
            weight: this.elements.weightInput,
            dateOfWeight: this.elements.dateOfWeightInput,
            healthcareNotes: this.elements.healthcareNotesInput
        };

        // Map errors to fields
        errors.forEach(error => {
            Object.entries(fieldMap).forEach(([fieldName, element]) => {
                if (error.toLowerCase().includes(fieldName.toLowerCase()) ||
                    error.toLowerCase().includes(this.validationManager.getFieldDisplayName(fieldName).toLowerCase())) {
                    if (element) {
                        this.uiManager.showFieldError(element, error);
                    }
                }
            });
        });
    }

    /**
     * Handle save button click
     */
    async handleSave() {
        console.log('💾 Save button clicked');

        if (!this.validateForm()) {
            console.log('❌ Form validation failed');
            return;
        }

        const formData = this.collectFormData();
        console.log('📝 Form data collected:', formData);

        // Show loading state
        this.uiManager.showLoading(this.elements.saveButton);

        try {
            // Update the fattening animal weight
            const updatedData = this.dataManager.updateFatteningWeight(this.currentFatteningAnimal.code, formData);

            console.log('✅ Fattening animal weight updated successfully:', updatedData);

            // Show success message
            this.uiManager.showNotification(
                `Weight updated for "${updatedData.animal.code}": ${formData.weight}kg`,
                'success',
                4000
            );

            // Update current animal reference
            this.currentFatteningAnimal = { ...this.currentFatteningAnimal, ...updatedData.animal };

            // Highlight updated fields
            this.highlightUpdatedFields();

            // Refresh weight statistics display
            const updatedAnimalData = this.dataManager.getCompleteFatteningAnimalData(this.currentFatteningAnimal.code);
            if (updatedAnimalData && updatedAnimalData.weightStats) {
                this.showWeightGainInfo(updatedAnimalData.weightStats, updatedAnimalData.targetInfo);

                // Update status indicator
                const titleElement = document.querySelector('.text-wrapper-5');
                if (titleElement) {
                    const existingIndicators = titleElement.parentNode.querySelectorAll('.fattening-status-indicator');
                    existingIndicators.forEach(indicator => indicator.remove());
                    this.uiManager.showFatteningStatus(updatedAnimalData.weightStats, titleElement.parentNode);
                }
            }

            // Trigger storage event for other pages to update
            window.dispatchEvent(new StorageEvent('storage', {
                key: 'animals',
                newValue: JSON.stringify(this.dataManager.getData('animals'))
            }));

        } catch (error) {
            console.error('❌ Error updating fattening animal weight:', error);
            this.uiManager.showNotification(
                `Failed to update weight: ${error.message}`,
                'error'
            );
        } finally {
            // Hide loading state
            this.uiManager.hideLoading(this.elements.saveButton);
        }
    }

    /**
     * Highlight updated fields with success styling
     */
    highlightUpdatedFields() {
        const fields = [
            this.elements.codeInput,
            this.elements.weightInput,
            this.elements.dateOfWeightInput,
            this.elements.healthcareNotesInput
        ];

        fields.forEach(field => {
            if (field) {
                this.uiManager.highlightFieldSuccess(field);
            }
        });
    }

    /**
     * Clear only form data (keep current animal reference and code field)
     */
    clearFormData() {
        // Clear form fields but keep code field and current animal reference for comparison
        const fieldsToKeep = ['codeInput', 'saveButton'];

        Object.entries(this.elements).forEach(([key, element]) => {
            if (fieldsToKeep.includes(key)) {
                return; // Skip these elements
            }

            if (element && element.value !== undefined) {
                element.value = '';
            }
        });

        // Clear form data but keep code for search
        const currentCode = this.formData.code;
        this.formData = { code: currentCode };

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        // Clear weight info display
        const weightInfoDiv = document.querySelector('.weight-gain-info');
        if (weightInfoDiv) {
            weightInfoDiv.remove();
        }

        console.log('🔄 Form data cleared (keeping code field)');
    }

    /**
     * Clear form to initial state
     */
    clearForm() {
        // Clear form data
        this.formData = {};
        this.currentFatteningAnimal = null;

        // Clear all input fields
        Object.values(this.elements).forEach(element => {
            if (element && element.value !== undefined) {
                element.value = '';
            }
        });

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        // Clear weight info display
        const weightInfoDiv = document.querySelector('.weight-gain-info');
        if (weightInfoDiv) {
            weightInfoDiv.remove();
        }

        console.log('🔄 Form cleared completely');
    }

    /**
     * Setup validation styles
     */
    setupValidation() {
        // Add form validation styles
        const style = document.createElement('style');
        style.textContent = `
            .field-error {
                animation: shake 0.3s ease-in-out;
            }
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Get current fattening statistics
     */
    getFatteningStatistics() {
        return this.dataManager.getFatteningStatistics();
    }

    /**
     * Search fattening animals
     */
    searchFatteningAnimals(searchTerm, searchField = null) {
        return this.dataManager.searchFatteningAnimals(searchTerm, searchField);
    }

    /**
     * Get all fattening animals
     */
    getAllFatteningAnimals() {
        return this.dataManager.getData('animals').filter(animal => animal.type === 'fattening');
    }

    /**
     * Get weight tracking recommendations
     */
    getWeightTrackingRecommendations(animalCode) {
        return this.dataManager.getWeightTrackingRecommendations(animalCode);
    }
}

// ==================== INITIALIZATION ====================

/**
 * Initialize the Update Fattening Weight system when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing Update Fattening Weight system...');

    try {
        // Create and initialize the controller
        const updateFatteningWeight = new UpdateFatteningWeight();
        updateFatteningWeight.init();

        // Make it globally accessible for debugging and external access
        window.updateFatteningWeight = updateFatteningWeight;

        console.log('🎉 Update Fattening Weight system ready!');
        console.log('Available global methods:');
        console.log('- window.updateFatteningWeight.searchFatteningAnimals(term)');
        console.log('- window.updateFatteningWeight.getFatteningStatistics()');
        console.log('- window.updateFatteningWeight.getAllFatteningAnimals()');

    } catch (error) {
        console.error('💥 Failed to initialize Update Fattening Weight system:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        errorDiv.textContent = 'Failed to initialize the fattening weight form. Please refresh the page.';
        document.body.appendChild(errorDiv);
    }
});

// ==================== UTILITY FUNCTIONS ====================

/**
 * Search for fattening animal by code (utility function)
 * @param {string} code - Animal code to search for
 * @returns {Object|null} - Found fattening animal or null
 */
window.searchFatteningAnimalByCode = function(code) {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return null;
    }

    return window.updateFatteningWeight.dataManager.getCompleteFatteningAnimalData(code);
};

/**
 * Load fattening animal into form by code (utility function)
 * @param {string} code - Animal code to load
 * @returns {boolean} - Success status
 */
window.loadFatteningAnimalByCode = function(code) {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return false;
    }

    if (!window.updateFatteningWeight.dataManager.isFatteningAnimal(code)) {
        console.error(`Animal "${code}" is not a fattening animal`);
        return false;
    }

    const fatteningAnimal = window.updateFatteningWeight.dataManager.getCompleteFatteningAnimalData(code);
    if (fatteningAnimal) {
        window.updateFatteningWeight.loadFatteningAnimalIntoForm(fatteningAnimal);
        window.updateFatteningWeight.setFormMode('edit');
        return true;
    }
    return false;
};

/**
 * Get all fattening animals (utility function)
 * @returns {Array} - Array of all fattening animals
 */
window.getAllFatteningAnimals = function() {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return [];
    }

    return window.updateFatteningWeight.dataManager.getData('animals').filter(animal => animal.type === 'fattening');
};

/**
 * Update fattening animal weight programmatically (utility function)
 * @param {string} animalCode - Animal code
 * @param {Object} weightData - Weight data to update
 * @returns {Object|null} - Updated animal or null
 */
window.updateFatteningWeightByCode = function(animalCode, weightData) {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return null;
    }

    try {
        return window.updateFatteningWeight.dataManager.updateFatteningWeight(animalCode, weightData);
    } catch (error) {
        console.error('Error updating fattening animal weight:', error);
        return null;
    }
};

/**
 * Add weight record for fattening animal (utility function)
 * @param {string} animalCode - Animal code
 * @param {Object} weightData - Weight data
 * @returns {Object|null} - Created weight record or null
 */
window.addFatteningWeightRecord = function(animalCode, weightData) {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return null;
    }

    try {
        return window.updateFatteningWeight.dataManager.addWeightRecord(animalCode, weightData, new Date().toISOString());
    } catch (error) {
        console.error('Error adding weight record:', error);
        return null;
    }
};

/**
 * Get weight history for fattening animal (utility function)
 * @param {string} animalCode - Animal code
 * @returns {Array} - Weight history array
 */
window.getFatteningWeightHistory = function(animalCode) {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return [];
    }

    return window.updateFatteningWeight.dataManager.getWeightHistory(animalCode);
};

/**
 * Get weight gain statistics for fattening animal (utility function)
 * @param {string} animalCode - Animal code
 * @returns {Object|null} - Weight gain statistics or null
 */
window.getFatteningWeightStats = function(animalCode) {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return null;
    }

    const weightHistory = window.updateFatteningWeight.dataManager.getWeightHistory(animalCode);
    return window.updateFatteningWeight.dataManager.calculateWeightGainStats(weightHistory);
};

/**
 * Debug function to check system status
 */
window.debugUpdateFatteningWeight = function() {
    console.log('=== Update Fattening Weight Debug Info ===');

    if (!window.updateFatteningWeight) {
        console.error('❌ Update Fattening Weight system not initialized');
        return;
    }

    console.log('✅ System initialized');
    console.log('📊 Fattening Statistics:', window.updateFatteningWeight.getFatteningStatistics());
    console.log('🔍 Current fattening animal:', window.updateFatteningWeight.currentFatteningAnimal);
    console.log('📝 Form data:', window.updateFatteningWeight.formData);

    // Check form elements
    const elements = window.updateFatteningWeight.elements;
    console.log('📋 Form elements:');
    Object.entries(elements).forEach(([key, element]) => {
        if (element) {
            console.log(`  ✅ ${key}: Found`);
        } else {
            console.log(`  ❌ ${key}: Missing`);
        }
    });

    console.log('=== End Debug Info ===');
};

/**
 * Quick test function to load a test fattening animal
 */
window.testLoadFatteningAnimal = function() {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return;
    }

    // Get first fattening animal from storage
    const fatteningAnimals = window.updateFatteningWeight.dataManager.getData('animals').filter(animal => animal.type === 'fattening');
    if (fatteningAnimals.length > 0) {
        const firstFatteningAnimal = fatteningAnimals[0];
        console.log('Loading test fattening animal:', firstFatteningAnimal.code);

        // Set code input and trigger search
        if (window.updateFatteningWeight.elements.codeInput) {
            window.updateFatteningWeight.elements.codeInput.value = firstFatteningAnimal.code;
            window.updateFatteningWeight.handleSearch();
        }

        return firstFatteningAnimal;
    } else {
        console.log('No fattening animals found in storage');
        return null;
    }
};

/**
 * Create test fattening animal for testing update functionality
 */
window.createTestFatteningAnimal = function() {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return;
    }

    const testAnimal = {
        code: 'FATTENING_TEST_' + Date.now(),
        type: 'fattening',
        gender: 'male',
        weight: '250',
        dateOfWeight: new Date().toISOString().split('T')[0],
        dateOfBirth: '2023-06-15',
        healthcareNotes: 'Test fattening animal for weight tracking',
    };

    try {
        // Use the add new animal system if available, otherwise add directly
        if (window.addNewAnimal && window.addNewAnimal.dataManager) {
            const result = window.addNewAnimal.dataManager.addAnimal(testAnimal);
            console.log('✅ Test fattening animal created:', result.code);
            return result;
        } else {
            // Fallback: add directly to storage
            const animals = JSON.parse(localStorage.getItem('animals') || '[]');

            testAnimal.id = Date.now().toString(36) + Math.random().toString(36).substring(2);
            testAnimal.createdAt = new Date().toISOString();
            testAnimal.updatedAt = new Date().toISOString();

            animals.push(testAnimal);
            localStorage.setItem('animals', JSON.stringify(animals));

            console.log('✅ Test fattening animal created (fallback):', testAnimal.code);
            return testAnimal;
        }
    } catch (error) {
        console.error('❌ Error creating test fattening animal:', error);
        return null;
    }
};

/**
 * Create multiple test fattening animals with weight history
 */
window.createTestFatteningAnimalsWithHistory = function() {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return;
    }

    const testAnimals = [
        {
            code: 'FATTENING001',
            type: 'fattening',
            gender: 'male',
            weight: '320',
            dateOfWeight: '2024-01-20',
            dateOfBirth: '2023-03-15',
            healthcareNotes: 'Excellent growth rate, good feed conversion'
        },
        {
            code: 'FATTENING002',
            type: 'fattening',
            gender: 'male',
            weight: '280',
            dateOfWeight: '2024-01-18',
            dateOfBirth: '2023-05-10',
            healthcareNotes: 'Steady growth, monitor feed intake'
        },
        {
            code: 'FATTENING003',
            type: 'fattening',
            gender: 'female',
            weight: '260',
            dateOfWeight: '2024-01-15',
            dateOfBirth: '2023-07-20',
            healthcareNotes: 'Young heifer, good potential'
        }
    ];

    const createdAnimals = [];

    testAnimals.forEach(animal => {
        try {
            if (window.addNewAnimal && window.addNewAnimal.dataManager) {
                const result = window.addNewAnimal.dataManager.addAnimal(animal);
                createdAnimals.push(result);

                // Add some weight history
                const weightHistory = [
                    { weight: parseFloat(animal.weight) - 50, date: '2023-12-01' },
                    { weight: parseFloat(animal.weight) - 30, date: '2023-12-15' },
                    { weight: parseFloat(animal.weight) - 15, date: '2024-01-01' },
                    { weight: parseFloat(animal.weight), date: animal.dateOfWeight }
                ];

                weightHistory.forEach(record => {
                    window.addFatteningWeightRecord(animal.code, {
                        weight: record.weight.toString(),
                        dateOfWeight: record.date,
                        healthcareNotes: `Weight record: ${record.weight}kg`
                    });
                });
            }
        } catch (error) {
            console.error('Error creating test fattening animal:', error);
        }
    });

    console.log(`✅ Created ${createdAnimals.length} test fattening animals with weight history`);
    return createdAnimals;
};

/**
 * Get performance summary for all fattening animals
 */
window.getFatteningPerformanceSummary = function() {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return null;
    }

    const stats = window.updateFatteningWeight.getFatteningStatistics();
    const animals = window.getAllFatteningAnimals();

    const performanceData = animals.map(animal => {
        const weightHistory = window.getFatteningWeightHistory(animal.code);
        const weightStats = window.getFatteningWeightStats(animal.code);
        const recommendations = window.updateFatteningWeight.getWeightTrackingRecommendations(animal.code);

        return {
            code: animal.code,
            currentWeight: animal.weight,
            weightStats,
            recommendations,
            performance: recommendations ? recommendations.currentPerformance : 'unknown'
        };
    });

    return {
        overallStats: stats,
        animalPerformance: performanceData,
        summary: {
            totalAnimals: animals.length,
            excellentPerformers: performanceData.filter(a => a.performance === 'excellent').length,
            goodPerformers: performanceData.filter(a => a.performance === 'good').length,
            poorPerformers: performanceData.filter(a => a.performance === 'poor').length
        }
    };
};

/**
 * Export weight data for fattening animals (utility function)
 */
window.exportFatteningWeightData = function() {
    if (!window.updateFatteningWeight) {
        console.error('Update Fattening Weight system not initialized');
        return null;
    }

    const animals = window.getAllFatteningAnimals();
    const exportData = animals.map(animal => {
        const weightHistory = window.getFatteningWeightHistory(animal.code);
        const weightStats = window.getFatteningWeightStats(animal.code);

        return {
            animalCode: animal.code,
            currentWeight: animal.weight,
            dateOfBirth: animal.dateOfBirth,
            totalWeightGain: weightStats.totalGain,
            averageDailyGain: weightStats.averageDailyGain,
            daysTracked: weightStats.daysTracked,
            weightHistory: weightHistory
        };
    });

    // Create downloadable CSV
    const csvContent = [
        ['Animal Code', 'Current Weight', 'Date of Birth', 'Total Gain', 'Daily Avg Gain', 'Days Tracked'],
        ...exportData.map(animal => [
            animal.animalCode,
            animal.currentWeight,
            animal.dateOfBirth,
            animal.totalWeightGain,
            animal.averageDailyGain,
            animal.daysTracked
        ])
    ].map(row => row.join(',')).join('\n');

    console.log('📊 Fattening weight data exported');
    console.log(csvContent);

    return {
        data: exportData,
        csv: csvContent
    };
};

// Close button functionality removed - handled by external close button in animal.js

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        UpdateFatteningWeight,
        FatteningDataManager,
        FatteningValidationManager,
        FatteningUIManager,
        BaseFatteningDataManager
    };
}