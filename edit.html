<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1, width=device-width" />

  <link rel="stylesheet" href="./css/general.css" />
  <link rel="stylesheet" href="./css/edit.css" />
  <link rel="stylesheet" href="./css/navigation-components.css" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@200;400;500&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=ABeeZee:wght@400&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Source Code Pro:wght@400&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=REM:wght@400&display=swap" />
</head>

<body>
  <div class="edit1">

    <!-- Include the sidebar and navbar components -->
    <div class="page-container">
      <!-- Sidebar Component -->
      <div class="side-bar">
        <div class="s-r-a-parent-wrapper">
          <div class="sra-parent">
            <h1 class="sra">SRA</h1>
            <div class="smart-raising-animal-wrapper">
              <div class="smart-raising-animal">Smart Raising Animal</div>
            </div>
          </div>
        </div>
        <div class="side-bar-bottom">
          <div class="side-bar-bottom-inner">
            <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
          </div>
          <div class="side-bar-elements">
            <div class="side-bar-options">
              <div class="side-bar-option-parent">
                <div class="vuesaxlinearcategory-2-parent">
                  <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt=""
                    src="./public/vuesaxlinearcategory21.svg" />
                  <a class="animals" href="index.html">Dashboard</a>
                </div>
              </div>
              <div class="side-bar-option-parent-inner">
                <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
              </div>
              <div class="side-bar-option-parent1">
                <div class="side-bar-element">
                  <div class="side-animal">
                    <img class="bull-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <div class="animals-wrapper">
                      <a class="animals" href="animal.html">Animals</a>
                    </div>
                  </div>
                  <div class="side-animal">
                    <img class="bull-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="dairy.html">Milch</a>
                  </div>
                  <div class="side-animal">
                    <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="newborn.html">New Born</a>
                  </div>
                  <div class="side-animal">
                    <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="feed.html">Feed</a>
                  </div>
                  <div class="side-animal">
                    <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="ingrediants.html">Ingredients</a>
                  </div>
                  <div class="side-animal">
                    <div class="side-animal">
                      <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                      <a class="animals" href="vaccination.html">Vaccination</a>
                    </div>
                  </div>
                  <div class="side-animal">
                    <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="reports.html">Reports</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navbar Component -->
      <header class="navbar">
        <img class="notification-bell-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
        <div class="nav-profile" id="navProfileContainer">
          <img class="male-avatar-portrait-of-a-youn-icon" loading="lazy" alt=""
            src="./public/<EMAIL>" />
          <div class="side-pregnant">
            <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt="" src="./public/vuesaxlineararrowdown.svg" />
          </div>
        </div>
      </header>


      <!-- Your page content goes here -->
      <main class="content-container">
        <!-- Page specific content -->



        <div class="settings21">

          <div class="services-parent">
            <img class="services-icon1" loading="lazy" alt="" src="./public/<EMAIL>" />

            <div class="edit-profile">Settings</div>
          </div>
          <div class="profile-separator"></div>
          <div class="profile-separator-parent">

            <div class="frame-group">
              <div class="frame-container">
                <div class="frame-div">
                  <div class="edit-profile-wrapper">
                    <div class="edit-profile">Edit Profile</div>
                  </div>
                  <div class="photo2">
                    <div class="avatar-background"></div>
                    <img class="photo-item" alt="" src="./public/<EMAIL>" />

                    <div class="camera-wrapper">
                      <img class="camera-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    </div>
                  </div>
                </div>
                <div class="user-data-fields-wrapper">
                  <div class="data-filled-group">
                    <div class="data-filled">
                      <div class="data-field-labels">
                        <div class="email">Name</div>
                      </div>
                      <input class="layla-hassan" placeholder="Layla Hassan" type="text" />
                    </div>

                    <div class="data-filled">
                      <div class="data-field-labels">
                        <div class="email">Email</div>
                      </div>
                      <input class="azzamaaexampleocm" placeholder="<EMAIL>" type="email" />
                    </div>
                    <!-- <div class="data-filled-parent"> -->
                    <div class="data-filled">
                      <div class="data-field-labels">
                        <div class="email">Password</div>
                      </div>
                      <input class="password-input" placeholder="**********" type="password" />
                    </div>
                    <div class="frame-wrapper1">
                      <div class="change-password-wrapper" id="frameContainer">
                        <div class="change-password">Change Password</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="navigation-buttons">
              <div class="back-left" id="backLeftContainer">
                <img class="double-left-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
              </div>
              <button class="save" id="saveContainer">
                <img class="save-icon" alt="" src="./public/<EMAIL>" />

                <div class="save1">Save</div>
              </button>
            </div>
          </div>
        </div>
    </div>
    </main>
  </div>
  </div>
  <div id="navProfilePopup" class="popup-overlay" style="display: none">
    <div class="nav-profile1">
      <div class="component-13">
        <a class="log-out" href="logout.html">Log Out</a>
      </div>
      <div class="component-131">
        <a class="log-out1" href="setting.html">Settings</a>
      </div>
      <div class="component-14">
        <a class="log-out2" href="about-us.html">About</a>
      </div>
    </div>
  </div>

  <!-- تضمين ملف JavaScript الموحد -->
  <!-- <script src="./js/main1.js"></script> -->
  <script src="js/navigation-components.js"></script>
  <script src="js/edit.js"></script>
</body>

</html>
