/* --------------------------------------------------------------------------------------- */
.services-icon {
  height: 45px;
  width: 40px;
  position: relative;
  object-fit: contain;
}

.settings {
  position: relative;
  font-weight: 500;
}

.user-settings-title-container {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  /* padding: var(--padding-3xs); */
  gap: 10px;
}

.user-settings-top {
  width: 13%;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-mid);
  box-sizing: border-box;
}

.user-settings-content-child {
  align-self: stretch;
  height: 1px;
  position: relative;
  border-top: 1px solid var(--color-gray-600);
  box-sizing: border-box;
}

.edit {
  position: relative;
  /* font-size: var(--highlights-size); */
  font-size: 20px;
  font-weight: 200;
  font-family: var(--highlights);
  color: #000;
  text-align: left;
  text-decoration: none;
}

.basic-settings,
.edite-buttom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.edite-buttom {
  cursor: pointer;
  border: 1px solid var(--color-gray-200);
  /* padding: var(--padding-5xs) 0; */
  padding: 3px;
  background-color: transparent;
  width: 105px;
  border-radius: var(--br-xl);
  flex-direction: column;
}

.edite-buttom:hover,
.time-zone-value-container:hover {
  background-color: var(--color-darkslategray-400);
  border: 1px solid var(--color-darkslategray-300);
  box-sizing: border-box;
}

.basic-settings {
  width: 100%;
  flex-direction: row;
  padding: var(--padding-3xs) var(--padding-9xs);
  /* gap: 730px; */
}

.photo {
  position: relative;
}

.user-photo-title-container {
  height: 37px;
  width: 105px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: var(--padding-3xs);
  box-sizing: border-box;
}

.photo-child {
  height: 80px;
  width: 80px;
  position: relative;
  object-fit: cover;
  border-radius: 50%;
}

.photo1,
.user-photo-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.photo1 {
  width: 116px;
  padding: var(--padding-3xs);
  box-sizing: border-box;
}

.user-photo-container {
  width: 77%;
  gap: var(--gap-0);
  text-align: center;
}

.basic-settings-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  /* gap: var(--gap-base); */
  text-align: left;
  height: 40%;
}

.profile-settings-fields {
  align-self: stretch;
  height: 1px;
  position: relative;
  border-top: 1px solid var(--color-gray-300);
  box-sizing: border-box;
}

.profile-settings-values {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.email-address {
  position: relative;
  flex-shrink: 0;
}

.email-address-wrapper,
.profile-settings-fields3 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.email-address-wrapper {
  height: 37px;
  width: 216px;
  padding: var(--padding-3xs);
  box-sizing: border-box;
}

.profile-settings-fields3 {
  width: 84%;

}

.password {
  width: 200px;
  display: inline-block;
  flex-shrink: 0;
}

.div5,
.password {
  position: relative;
}

.profile-settings-fields5,
.wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.profile-settings-fields5 {
  width: 80%;
  justify-content: space-between;

  text-align: left;
}

.owner-container,
.owner-title-container {
  height: 40px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
}

.owner-container {
  width: 10%;
  background-color: #fff;
  text-align: left;
}

.accessibility-container,
.profile-settings-layout {
  display: flex;
  justify-content: space-between;
  gap: var(--gap-0);
}

.accessibility-container {
  width: 80%;
  flex-direction: row;
  align-items: center;
}

.profile-settings-layout {
  align-self: stretch;
  height: 65%;
  flex-direction: column;
  align-items: flex-start;
}

.input-data {
  width: 150px;
  height: 37px;
  border: 1px solid rgba(0, 0, 0, .2);
  background-color: transparent;
  border-radius: 16px;
  padding: 0px 20px;
  font-size: 20px;
  font-weight: 200;
}

.date-format-field,
.date-format-settings {
  display: flex;
  flex-direction: row;
  max-width: 100%;
}

.date-format-field {
  width: 909px;
  align-items: center;
  justify-content: space-between;
}

.date-format-settings {
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
}

.time-zone-container-child {
  width: 929px;
  height: 1px;
  position: relative;
  border-top: 1px solid rgba(0, 0, 0, .15);
  box-sizing: border-box;
}

.time-zone-value-container {
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, .2);
  padding: 8px 10px;
  background-color: transparent;
  width: 150px;
  border-radius: 20px;
}

.time-zone-field,
.time-zone-settings {
  display: flex;
  flex-direction: row;
  max-width: 100%;
}

.time-zone-field {
  width: 911px;
  align-items: center;
  justify-content: space-between;
}

.time-zone-settings {
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
}

.time-zone-container {
  gap: 10px;
}

.date-format-container,
.performance-container,
.time-zone-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  max-width: 100%;
}

.date-format-container {
  gap: 10px;
}

.performance-container {
  align-self: stretch;
  padding: 1%;
  gap: 10px;
}

.profile-settings,
.profile-settings-container {
  align-self: stretch;
  flex: 1;
  display: flex;
  align-items: flex-start;
  max-width: 100%;
}

.profile-settings {
  flex-direction: column;
  height: 95%;
  justify-content: space-around;
}

.profile-settings-container {
  flex-direction: row;
  justify-content: flex-end;
  padding: 0 var(--padding-5xs) 0 0;
  box-sizing: border-box;
}

.user-settings-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  /* gap: var(--gap-smi); */
  max-width: 100%;
  text-align: center;
  color: #000;
  height: 100%;
}

.settings-container {
  align-self: stretch;
  flex: 1;
  display: flex;
  align-items: flex-start;
  box-sizing: border-box;
  max-width: 100%;
}

.settings2 {
  position: absolute;
  width: 75%;
  height: 87%;
  top: 12%;
  left: 22%;
  background-color: #fff;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 2%;
  justify-content: flex-start;
  align-items: flex-start;
  box-sizing: border-box;
  max-width: 100%;
  align-self: stretch;
  padding: 2%;

}

.settings-container {
  flex-direction: row;
  justify-content: flex-end;
  padding: 4px 30px;
}

.setting {

  background-color: #e3e4e4;
  display: flex;
  flex-direction: row;
  justify-content: center;
  /* padding: 2%; */
  width: 100%;
  height: 100vh;
}

/* إضافة media queries للتوافق مع مختلف أحجام الشاشات */
@media screen and (max-width: 1200px) {
  .settings2 {
    padding: var(--padding-xl) 20px 75px;
    box-sizing: border-box;
  }

  .profile-settings-container {
    padding: 0;
  }
}

@media screen and (max-width: 1050px) {
  .settings {
    font-size: var(--font-size-7xl);
  }

  .edit {
    font-size: var(--font-size-7xl);
  }

  .email-address,
  .password,
  .photo {
    font-size: var(--font-size-7xl);
  }

  .basic-settings {
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 15px;
  }

  .profile-settings-fields3,
  .profile-settings-fields5,
  .accessibility-container {
    width: 100%;
    flex-wrap: wrap;
    gap: 15px;
  }
}

@media screen and (max-width: 800px) {
  .side-bar {
    width: 250px;
  }

  .navbar {
    width: 100%;
  }

  .settings2 {
    padding: var(--padding-xl) 15px 50px;
  }

  .user-photo-container,
  .profile-settings-fields3,
  .profile-settings-fields5 {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .input-data {
    width: 100%;
    max-width: 250px;
  }

  .time-zone-value-container {
    width: 100%;
    max-width: 250px;
  }

  .performance-container {
    gap: 20px;
  }
}

@media screen and (max-width: 600px) {
  .setting {
    flex-direction: column;
  }

  .side-bar {
    width: 100%;
    border-radius: 0;
    margin-bottom: 10px;
  }

  .nav-bar-container {
    max-width: 100%;
  }

  .settings2 {
    border-radius: var(--br-base);
    padding: var(--padding-base) 10px 30px;
  }

  .user-settings-content {
    padding: 0 10px;
  }

  .basic-settings {
    padding: var(--padding-3xs) 0;
  }

  .photo1 {
    width: 100%;
    justify-content: center;
  }
}

@media screen and (max-width: 450px) {

  .settings,
  .edit {
    font-size: var(--font-size-lgi);
  }

  .email-address,
  .password,
  .photo {
    font-size: var(--font-size-lgi);
  }

  .user-settings-title-container {
    flex-wrap: wrap;
    justify-content: center;
  }

  .input-data {
    font-size: 16px;
    height: 30px;
    padding: 0 10px;
  }

  .time-zone-value-container {
    padding: 5px;
  }

  .profile-settings-layout {
    height: auto;
  }
}

/* إعادة تصميم قائمة النافبار المنسدلة */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  z-index: 100;
}

.nav-profile1 {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 140px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 101;
}

.component-13,
.component-131,
.component-14 {
  align-self: stretch;
  height: 44px;
  border-radius: var(--br-7xs);
  background-color: var(--color-floralwhite);
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: var(--padding-2xs) var(--padding-3xs);
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.3s;
}

.component-13:hover,
.component-131:hover,
.component-14:hover {
  background-color: #f0f0f0;
}

.log-out,
.log-out1,
.log-out2 {
  width: auto;
  position: relative;
  font-weight: 500;
  display: inline-block;
}

.popup-overlay a {
  text-decoration: none;
  color: black;
}

/* إضافة مؤشر للعنصر القابل للنقر */
#navProfileContainer {
  cursor: pointer;
}