@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0%;
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}

.feed {

    background-color: #e3e4e4;
    display: flex;
    flex-direction: row;
    justify-content: center;
    /* padding: 2%; */
    width: 100%;
    height: 100%;
}


/* 
 .navbar {
    position: absolute;
    width:75%;
    height: 54px;
    top: 3%;
    left: 22%;
    background-color: #ffffff;
    border-radius: 20px;
    overflow: hidden;
}

.notification-bell {
    position: absolute;
    width: 55px;
    height: 55px;
    top: 6px;
    left: 19px;
    background-image: url(./img/notification.png);
    background-size: 100% 100%;
}

 .frame {
    position: absolute;
    width: 143px;
    height: 64px;
    top: 1px;
    left: 915px;
}

 .side-bar {
    position: absolute;
    width: 18%;
    height: 94%;
    left: 2%;
  margin-bottom: 2%;
    background-color: #0b291a;
    border-radius: 20px;
    overflow: hidden;
} */

.parent{
  display:flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 85%;


}

.child{
    height: 10%;

    color: #ffffff;
}





 .frame-3 {
    display: flex;
    flex-direction: column;

    position: relative;
    text-align: center;
justify-content: center;
align-items: center;
    width: 100%;
  height: 15%;


font-size: 40px;
color: #fbfaf0;
font-family: ABeeZee;
}

 .text-wrapper-3 {

    font-size: 4vw; /* Large font size for acronym */
    font-weight: 400;
    color: #f0f0f0; /* Light color for text */

}


 .text-wrapper-4 {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    font-size: .9vw; /* Smaller font size for full text */
    color: #f0f0f0; /* Light color for text */
    z-index: 3;
    background-color: #0b291a;

 }






 .frame-4 {
    position: absolute;
    width: 75%;
    height:6%;
    top:11%;
    left: 22%;
    background-color: transparent;
    border-radius: 16px;
    display: flex;
    direction: row;
    gap: 2%;
    justify-content: flex-end;

}

 .frame-5 {
    display: flex;
    width: 10%;
    height: 10%;
    justify-content: flex-end;
  margin-left: 1%;
}



 .frame-9 {
    display: flex;
    position: relative;
    width: 24%;
    height: 45px;
    align-items: center;
    justify-content: center;
   border: hidden;
    position: relative;
    background-color: #aedf32;
    border-radius: 14px;

}



.btntext{
    font-size: 20px;
    font-weight: 40px;
    color: #ffffff;
}


.parent1{
    position: absolute;
    width: 75%;
    height: 75%;
    top:18%;
    left: 22%;

    border-radius: 16px;
    display: flex;
    direction: row;
   justify-content: flex-start;



    gap: 1%;
flex-wrap: wrap;

}

.category {
    position: relative;
    width: 49%;
    height: 33%;
    background-color: var(--color-card-bg);
    border-radius: 12px;
    overflow-y: auto; /* Enable vertical scrolling */
    scrollbar-width: thin;
    scroll-behavior: smooth;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--color-border);
    margin-bottom: 10px;
    padding-top: 2%;
    background-color: #ffffff;
  }

  /* تنسيق شريط التمرير */
  .category::-webkit-scrollbar {
    width: 8px;
  }

  .category::-webkit-scrollbar-track {
    background: var(--color-light-bg);
    border-radius: 4px;
  }

  .category::-webkit-scrollbar-thumb {
    background: var(--color-primary);
    border-radius: 4px;
    border: hidden;
  }

  .category::-webkit-scrollbar-thumb:hover {
    background: var(--color-primary-light);
  }

.frame-5 {
  display: flex;
  width: 10%;
  height: 10%;
  justify-content: flex-start;
margin-left: 1%;
}

.update {
  position: relative;
  width: 25px;
  height: 25px;

}


.text-wrapper-5 {
  position: relative;
  width: fit-content;
  
  font-weight:bold;
  color: #0b291a;
  font-size: 18px;
  letter-spacing: var(--highlights-letter-spacing);
  line-height: var(--highlights-line-height);
  white-space: nowrap;
  font-style: var(--highlights-font-style);
}

.child1{
  width: 95%;
  height: 18%;
  display: flex;
  align-items: flex-start;
  left: 10%;
  background-color: #f8f8f8;
  margin: 2.5%;
}


.statement{
  font-size: 14px;
  
}

@media screen and (max-width: 1200px) {
  .category {
    width: 100%;
    height: 250px;
  }
}

@media screen and (max-width: 768px) {
  .side-bar {
    width: 25%;
  }

  .navbar, .parent1 {
    width: 70%;
    left: 28%;
  }
}

@media screen and (max-width: 480px) {
  .side-bar {
    width: 0;
    display: none;
  }

  .navbar, .parent1 {
    width: 95%;
    left: 2.5%;
  }

  .category {
    height: 200px;
  }
}