document.addEventListener('DOMContentLoaded', function() {
  // Initialize dashboard components
  initializeCharts();
  setupDashboardAnimations();
  setupDashboardInteractions();
  loadDashboardData();
  setupResponsiveLayout();
});

/**
 * Initialize and animate chart elements
 */
function initializeCharts() {
  // Animate pie chart
  animatePieChart();
  
  // Animate bar charts
  animateBarCharts();
  
  // Setup chart hover effects
  setupChartHoverEffects();
}

/**
 * Animate the pie chart with a reveal effect
 */
function animatePieChart() {
  const pieLayer = document.querySelector('.pielayer-icon');
  const pieValue = document.querySelector('.pie-layer-parent .div');
  
  if (pieLayer && pieValue) {
    // Initial state
    pieLayer.style.opacity = '0';
    pieLayer.style.transform = 'scale(0.8) rotate(-90deg)';
    pieLayer.style.transition = 'opacity 1s ease, transform 1s ease';
    
    pieValue.style.opacity = '0';
    pieValue.style.transform = 'scale(0.8)';
    pieValue.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
    pieValue.style.transitionDelay = '0.6s';
    
    // Animate in
    setTimeout(() => {
      pieLayer.style.opacity = '1';
      pieLayer.style.transform = 'scale(1) rotate(0)';
      
      setTimeout(() => {
        pieValue.style.opacity = '1';
        pieValue.style.transform = 'scale(1)';
      }, 600);
    }, 300);
  }
  
  // Animate legend items
  const legends = document.querySelectorAll('.legend');
  legends.forEach((legend, index) => {
    legend.style.opacity = '0';
    legend.style.transform = 'translateX(-20px)';
    legend.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
    legend.style.transitionDelay = `${0.8 + (index * 0.15)}s`;
    
    setTimeout(() => {
      legend.style.opacity = '1';
      legend.style.transform = 'translateX(0)';
    }, 300);
  });
}

/**
 * Animate the bar charts with a growing effect
 */
function animateBarCharts() {
  // Animate weight chart bars
  const barStrips = document.querySelectorAll('.barstrip, .barstrip1, .barstrip2, .barstrip3');
  
  barStrips.forEach((bar, index) => {
    // Set initial state
    bar.style.transformOrigin = 'bottom';
    bar.style.transform = 'scaleY(0)';
    bar.style.transition = 'transform 0.8s ease';
    bar.style.transitionDelay = `${0.3 + (index * 0.2)}s`;
    
    // Animate in
    setTimeout(() => {
      bar.style.transform = 'scaleY(1)';
    }, 500);
  });
  
  // Animate quarters chart
  animateQuartersChart();
}

/**
 * Animate the quarters milk production chart
 */
function animateQuartersChart() {
  const quarterLabels = document.querySelectorAll('.summer');
  const milkValues = document.querySelectorAll('.sun');
  
  // Animate quarter labels
  quarterLabels.forEach((label, index) => {
    label.style.opacity = '0';
    label.style.transform = 'translateX(-20px)';
    label.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
    label.style.transitionDelay = `${0.3 + (index * 0.15)}s`;
    
    setTimeout(() => {
      label.style.opacity = '1';
      label.style.transform = 'translateX(0)';
    }, 300);
  });
  
  // Animate milk production values
  milkValues.forEach((value, index) => {
    value.style.opacity = '0';
    value.style.transform = 'translateX(20px)';
    value.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
    value.style.transitionDelay = `${0.5 + (index * 0.15)}s`;
    
    setTimeout(() => {
      value.style.opacity = '1';
      value.style.transform = 'translateX(0)';
    }, 500);
  });
}

/**
 * Setup hover effects for chart elements
 */
function setupChartHoverEffects() {
  // Pie chart hover effects
  const pieChart = document.querySelector('.pielayer-icon');
  const pieValue = document.querySelector('.pie-layer-parent .div');
  
  if (pieChart) {
    pieChart.style.transition = 'transform 0.3s ease';
    
    pieChart.addEventListener('mouseover', function() {
      this.style.transform = 'scale(1.05)';
      if (pieValue) pieValue.style.fontWeight = 'bold';
    });
    
    pieChart.addEventListener('mouseout', function() {
      this.style.transform = 'scale(1)';
      if (pieValue) pieValue.style.fontWeight = 'normal';
    });
  }
  
  // Bar chart hover effects
  const barStrips = document.querySelectorAll('.barstrip, .barstrip1, .barstrip2, .barstrip3');
  
  barStrips.forEach(bar => {
    bar.style.transition = 'transform 0.3s ease, filter 0.3s ease';
    
    bar.addEventListener('mouseover', function() {
      this.style.transform = 'scaleY(1.1)';
      this.style.filter = 'brightness(1.2)';
      
      // Show tooltip with value
      showChartTooltip(this, 'Value: ' + Math.floor(Math.random() * 20) + ' kg');
    });
    
    bar.addEventListener('mouseout', function() {
      this.style.transform = 'scaleY(1)';
      this.style.filter = 'brightness(1)';
      
      // Hide tooltip
      const tooltip = document.querySelector('.chart-tooltip');
      if (tooltip) tooltip.remove();
    });
  });
  
  // Legend hover effects
  const legends = document.querySelectorAll('.legend');
  
  legends.forEach(legend => {
    legend.style.cursor = 'pointer';
    legend.style.transition = 'transform 0.3s ease';
    
    legend.addEventListener('mouseover', function() {
      this.style.transform = 'translateX(5px)';
    });
    
    legend.addEventListener('mouseout', function() {
      this.style.transform = 'translateX(0)';
    });
    
    // Add click handler to show detailed information
    legend.addEventListener('click', function() {
      const category = this.querySelector('.pregnant').textContent;
      showCategoryDetails(category);
    });
  });
}

/**
 * Show a tooltip for chart elements
 * @param {HTMLElement} element - Element to show tooltip for
 * @param {string} text - Text to display in tooltip
 */
function showChartTooltip(element, text) {
  // Remove any existing tooltips
  const existingTooltip = document.querySelector('.chart-tooltip');
  if (existingTooltip) existingTooltip.remove();
  
  // Create tooltip
  const tooltip = document.createElement('div');
  tooltip.className = 'chart-tooltip';
  tooltip.textContent = text;
  tooltip.style.position = 'absolute';
  tooltip.style.backgroundColor = 'rgba(45, 67, 86, 0.9)';
  tooltip.style.color = 'white';
  tooltip.style.padding = '5px 10px';
  tooltip.style.borderRadius = '4px';
  tooltip.style.fontSize = '12px';
  tooltip.style.zIndex = '100';
  tooltip.style.pointerEvents = 'none';
  
  // Add to document
  document.body.appendChild(tooltip);
  
  // Position tooltip above element
  const rect = element.getBoundingClientRect();
  tooltip.style.top = `${rect.top + window.scrollY - tooltip.offsetHeight - 5}px`;
  tooltip.style.left = `${rect.left + window.scrollX + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
}

/**
 * Show detailed information for a category
 * @param {string} category - Category name
 */
function showCategoryDetails(category) {
  // Create modal for category details
  const modal = document.createElement('div');
  modal.className = 'category-modal';
  modal.style.position = 'fixed';
  modal.style.top = '0';
  modal.style.left = '0';
  modal.style.width = '100%';
  modal.style.height = '100%';
  modal.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
  modal.style.display = 'flex';
  modal.style.justifyContent = 'center';
  modal.style.alignItems = 'center';
  modal.style.zIndex = '1000';
  
  // Create modal content
  const content = document.createElement('div');
  content.style.backgroundColor = 'white';
  content.style.padding = '20px';
  content.style.borderRadius = '8px';
  content.style.maxWidth = '500px';
  content.style.width = '80%';
  content.style.maxHeight = '80%';
  content.style.overflow = 'auto';
  
  // Add header
  const header = document.createElement('h2');
  header.textContent = category + ' Animals';
  header.style.color = '#2D4356';
  header.style.marginTop = '0';
  
  // Add close button
  const closeButton = document.createElement('button');
  closeButton.textContent = '×';
  closeButton.style.position = 'absolute';
  closeButton.style.top = '10px';
  closeButton.style.right = '15px';
  closeButton.style.backgroundColor = 'transparent';
  closeButton.style.border = 'none';
  closeButton.style.fontSize = '24px';
  closeButton.style.cursor = 'pointer';
  closeButton.style.color = '#2D4356';
  
  // Add sample data
  const data = document.createElement('div');
  data.innerHTML = `
    <p><strong>Total Count:</strong> ${Math.floor(Math.random() * 100) + 50}</p>
    <p><strong>Average Age:</strong> ${Math.floor(Math.random() * 5) + 2} years</p>
    <p><strong>Average Weight:</strong> ${Math.floor(Math.random() * 300) + 200} kg</p>
    <p><strong>Health Status:</strong> Good</p>
    <p><strong>Last Updated:</strong> ${new Date().toLocaleDateString()}</p>
    <button class="view-all-button">View All ${category} Animals</button>
  `;
  
  // Style the view all button
  setTimeout(() => {
    const viewAllButton = document.querySelector('.view-all-button');
    if (viewAllButton) {
      viewAllButton.style.backgroundColor = '#2D4356';
      viewAllButton.style.color = 'white';
      viewAllButton.style.border = 'none';
      viewAllButton.style.padding = '8px 16px';
      viewAllButton.style.borderRadius = '4px';
      viewAllButton.style.cursor = 'pointer';
      viewAllButton.style.marginTop = '10px';
      
      viewAllButton.addEventListener('click', function() {
        // Navigate to appropriate page based on category
        switch(category.toLowerCase()) {
          case 'pregnant':
            window.location.href = 'animal.html?filter=pregnant';
            break;
          case 'new born':
            window.location.href = 'newborn.html';
            break;
          case 'fattening':
            window.location.href = 'animal.html?filter=fattening';
            break;
          case 'milch':
            window.location.href = 'dairy.html';
            break;
          default:
            window.location.href = 'animal.html';
        }
      });
    }
  }, 100);
  
  // Assemble modal
  content.appendChild(header);
  content.appendChild(data);
  modal.appendChild(content);
  modal.appendChild(closeButton);
  
  // Add to document
  document.body.appendChild(modal);
  
  // Add event listeners
  closeButton.addEventListener('click', function() {
    document.body.removeChild(modal);
  });
  
  modal.addEventListener('click', function(e) {
    if (e.target === modal) {
      document.body.removeChild(modal);
    }
  });
}

/**
 * Setup dashboard interactions
 */
function setupDashboardInteractions() {
  // Make section headers interactive
  const sectionHeaders = document.querySelectorAll('.quartiers');
  
  sectionHeaders.forEach(header => {
    header.style.cursor = 'pointer';
    header.style.transition = 'color 0.3s ease';
    
    header.addEventListener('mouseover', function() {
      this.style.color = '#5CB338';
    });
    
    header.addEventListener('mouseout', function() {
      this.style.color = '';
    });
  });
  
  // Add refresh button to each section
  const sections = document.querySelectorAll('.num-of-animal, .quarters, .weight1');
  
  sections.forEach(section => {
    const refreshButton = document.createElement('button');
    refreshButton.innerHTML = '↻';
    refreshButton.style.position = 'absolute';
    refreshButton.style.top = '10px';
    refreshButton.style.right = '10px';
    refreshButton.style.backgroundColor = 'transparent';
    refreshButton.style.border = 'none';
    refreshButton.style.fontSize = '18px';
    refreshButton.style.cursor = 'pointer';
    refreshButton.style.color = '#2D4356';
    refreshButton.title = 'Refresh data';
    
    // Make sure section has position relative for absolute positioning
    section.style.position = 'relative';
    
    section.appendChild(refreshButton);
    
    // Add refresh functionality
    refreshButton.addEventListener('click', function() {
      // Animate refresh button
      this.style.transition = 'transform 0.5s ease';
      this.style.transform = 'rotate(360deg)';
      
      // Reset animation after completion
      setTimeout(() => {
        this.style.transition = 'none';
        this.style.transform = 'rotate(0)';
      }, 500);
      
      // Refresh section data
      refreshSectionData(section);
    });
  });
}

/**
 * Refresh data for a specific dashboard section
 * @param {HTMLElement} section - Section to refresh
 */
function refreshSectionData(section) {
  // Determine which section to refresh
  if (section.classList.contains('num-of-animal')) {
    // Refresh animal numbers
    const animalCount = document.querySelector('.pie-layer-parent .div');
    if (animalCount) {
      animalCount.textContent = Math.floor(Math.random() * 100) + 400;
      
      // Animate the count change
      animalCount.style.transform = 'scale(1.2)';
      animalCount.style.color = '#5CB338';
      
      setTimeout(() => {
        animalCount.style.transform = 'scale(1)';
        animalCount.style.color = '';
      }, 500);
    }
    
    // Re-animate pie chart
    animatePieChart();
  } 
  else if (section.classList.contains('quarters')) {
    // Refresh milk production data
    const milkValues = document.querySelectorAll('.sun');
    
    milkValues.forEach(value => {
      const newValue = Math.floor(Math.random() * 100) + 100;
      value.textContent = newValue + ' Ton';
      
      // Animate the value change
      value.style.transform = 'scale(1.2)';
      value.style.color = '#5CB338';
      
      setTimeout(() => {
        value.style.transform = 'scale(1)';
        value.style.color = '';
      }, 500);
    });
    
    // Re-animate quarters chart
    animateQuartersChart();
  }
  else if (section.classList.contains('weight1')) {
    // Refresh weight chart
    animateBarCharts();
  }
  
  // Show refresh message
  const message = document.createElement('div');
  message.textContent = 'Data refreshed';
  message.style.position = 'absolute';
  message.style.top = '40px';
  message.style.right = '10px';
  message.style.backgroundColor = 'rgba(92, 179, 56, 0.9)';
  message.style.color = 'white';
  message.style.padding = '5px 10px';
  message.style.borderRadius = '4px';
  message.style.fontSize = '12px';
  message.style.opacity = '0';
  message.style.transition = 'opacity 0.3s ease';
  
  section.appendChild(message);
  
  // Show and hide message
  setTimeout(() => {
    message.style.opacity = '1';
    
    setTimeout(() => {
      message.style.opacity = '0';
      
      setTimeout(() => {
        section.removeChild(message);
      }, 300);
    }, 2000);
  }, 100);
}

/**
 * Load dashboard data from localStorage or API
 */
function loadDashboardData() {
  // In a real application, this would fetch data from an API
  // For now, we'll use localStorage or generate sample data
  
  // Get animal counts
  const animals = JSON.parse(localStorage.getItem('animals') || '[]');
  const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
  const newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
  
  // Calculate total animal count
  const totalAnimals = animals.length + dairyAnimals.length + newbornAnimals.length;
  
  // Update animal count if we have data
  if (totalAnimals > 0) {
    const animalCount = document.querySelector('.pie-layer-parent .div');
    if (animalCount) {
      animalCount.textContent = totalAnimals;
    }
  }
  
  // Update last refresh time
  updateLastRefreshTime();
}

/**
 * Update the last refresh time indicator
 */
function updateLastRefreshTime() {
  // Create or update last refresh indicator
  let lastRefresh = document.querySelector('.last-refresh-time');
  
  if (!lastRefresh) {
    lastRefresh = document.createElement('div');
    lastRefresh.className = 'last-refresh-time';
    lastRefresh.style.position = 'absolute';
    lastRefresh.style.bottom = '10px';
    lastRefresh.style.right = '10px';
    lastRefresh.style.fontSize = '12px';
    lastRefresh.style.color = '#666';
    
    const content = document.querySelector('.content');
    if (content) {
      content.style.position = 'relative';
      content.appendChild(lastRefresh);
    }
  }
  
  // Update the time
  const now = new Date();
  lastRefresh.textContent = 'Last updated: ' + now.toLocaleTimeString();
}

/**
 * Setup responsive layout adjustments
 */
function setupResponsiveLayout() {
  // Function to adjust layout based on screen size
  function adjustLayout() {
    const width = window.innerWidth;
    
    if (width < 768) {
      // Mobile layout adjustments
      const contentMetrics = document.querySelector('.content-metrics');
      if (contentMetrics) {
        contentMetrics.style.flexDirection = 'column';
      }
      
      const sections = document.querySelectorAll('.num-of-animal, .quarters, .weight1');
      sections.forEach(section => {
        section.style