document.addEventListener('DOMContentLoaded', function() {
  // Initialize animations and interactions
  initializeAnimations();
  setupSocialMediaInteractions();
  setupContactInteractions();
  setupResponsiveElements();
});

/**
 * Initialize animations for the about-us page elements
 */
function initializeAnimations() {
  // Elements to animate
  const aboutUsTitle = document.querySelector('.about-us1');
  const whoWeAre = document.querySelector('.who-we-are');
  const descriptions = document.querySelectorAll('.we-will-help-container, .we-are-a-container');
  const socialSection = document.querySelector('.e-socoal-parent');
  const connectSection = document.querySelector('.connect-us');
  const heroImage = document.querySelector('.unsplash7cyehjgcosi-icon');
  
  // Animate hero image on load
  if (heroImage) {
    heroImage.style.opacity = '0';
    heroImage.style.transform = 'scale(1.05)';
    heroImage.style.transition = 'opacity 1s ease, transform 1.5s ease';
    
    setTimeout(() => {
      heroImage.style.opacity = '1';
      heroImage.style.transform = 'scale(1)';
    }, 300);
  }
  
  // Set initial styles for elements
  if (aboutUsTitle) {
    aboutUsTitle.style.opacity = '0';
    aboutUsTitle.style.transform = 'translateY(30px)';
    aboutUsTitle.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
  }
  
  if (whoWeAre) {
    whoWeAre.style.opacity = '0';
    whoWeAre.style.transform = 'translateY(30px)';
    whoWeAre.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
  }
  
  descriptions.forEach((desc, index) => {
    desc.style.opacity = '0';
    desc.style.transform = 'translateY(30px)';
    desc.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
    desc.style.transitionDelay = `${0.2 + (index * 0.2)}s`;
  });
  
  if (socialSection) {
    socialSection.style.opacity = '0';
    socialSection.style.transform = 'translateY(30px)';
    socialSection.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
  }
  
  if (connectSection) {
    connectSection.style.opacity = '0';
    connectSection.style.transform = 'translateY(30px)';
    connectSection.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
  }
  
  // Function to check if element is in viewport
  function isInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
      rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
      rect.bottom >= 0 &&
      rect.left >= 0 &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  }
  
  // Function to check elements and animate them if in viewport
  function checkElements() {
    if (aboutUsTitle && isInViewport(aboutUsTitle)) {
      aboutUsTitle.style.opacity = '1';
      aboutUsTitle.style.transform = 'translateY(0)';
    }
    
    if (whoWeAre && isInViewport(whoWeAre)) {
      whoWeAre.style.opacity = '1';
      whoWeAre.style.transform = 'translateY(0)';
    }
    
    descriptions.forEach(desc => {
      if (isInViewport(desc)) {
        desc.style.opacity = '1';
        desc.style.transform = 'translateY(0)';
      }
    });
    
    if (socialSection && isInViewport(socialSection)) {
      socialSection.style.opacity = '1';
      socialSection.style.transform = 'translateY(0)';
    }
    
    if (connectSection && isInViewport(connectSection)) {
      connectSection.style.opacity = '1';
      connectSection.style.transform = 'translateY(0)';
    }
  }
  
  // Check elements on load and scroll
  setTimeout(checkElements, 100); // Initial check after a small delay
  window.addEventListener('scroll', checkElements);
}

/**
 * Setup social media interactions
 */
function setupSocialMediaInteractions() {
  const socialIcons = document.querySelectorAll('.facebook-parent img');
  
  socialIcons.forEach(icon => {
    // Add hover effects
    icon.style.transition = 'transform 0.3s ease, filter 0.3s ease';
    
    icon.addEventListener('mouseover', function() {
      this.style.transform = 'scale(1.2) rotate(5deg)';
      this.style.filter = 'brightness(1.2)';
    });
    
    icon.addEventListener('mouseout', function() {
      this.style.transform = 'scale(1) rotate(0)';
      this.style.filter = 'brightness(1)';
    });
    
    // Add click handler (for demonstration - would link to social media in real app)
    icon.addEventListener('click', function() {
      const socialPlatform = this.src.includes('facebook') ? 'Facebook' :
                            this.src.includes('instagram') ? 'Instagram' :
                            this.src.includes('linkedin') ? 'LinkedIn' :
                            this.src.includes('x') ? 'Twitter' : 'Social Media';
      
      // Show a message when icon is clicked
      showTooltip(this, `Visit our ${socialPlatform} page`);
    });
  });
}

/**
 * Setup contact information interactions
 */
function setupContactInteractions() {
  // Phone number click to copy
  const phoneElement = document.querySelector('.phone .email-info:last-child');
  if (phoneElement) {
    phoneElement.style.cursor = 'pointer';
    phoneElement.title = 'Click to copy phone number';
    
    phoneElement.addEventListener('click', function() {
      const phoneNumber = this.textContent.trim();
      copyToClipboard(phoneNumber);
      showTooltip(this, 'Phone number copied!');
    });
  }
  
  // Email click to copy
  const emailElement = document.querySelector('.email1 .email-info:last-child');
  if (emailElement) {
    emailElement.style.cursor = 'pointer';
    emailElement.title = 'Click to copy email address';
    
    emailElement.addEventListener('click', function() {
      const email = this.textContent.trim();
      copyToClipboard(email);
      showTooltip(this, 'Email copied!');
    });
  }
}

/**
 * Setup responsive elements behavior
 */
function setupResponsiveElements() {
  // Adjust footer position based on content height
  function adjustFooterPosition() {
    const contentContainer = document.querySelector('.frame-wrapper4');
    const footer = document.querySelector('.footer');
    
    if (contentContainer && footer) {
      const contentHeight = contentContainer.offsetHeight;
      const windowHeight = window.innerHeight;
      const contentBottom = contentContainer.getBoundingClientRect().bottom;
      
      // If content is shorter than window, position footer at bottom of window
      if (contentBottom < windowHeight - 100) {
        footer.style.position = 'relative';
        footer.style.marginTop = '50px';
      } else {
        footer.style.position = 'relative';
        footer.style.marginTop = '20px';
      }
    }
  }
  
  // Call on load and resize
  adjustFooterPosition();
  window.addEventListener('resize', adjustFooterPosition);
  
  // Add smooth scrolling for better mobile experience
  document.documentElement.style.scrollBehavior = 'smooth';
}

/**
 * Helper function to copy text to clipboard
 * @param {string} text - Text to copy
 */
function copyToClipboard(text) {
  const textarea = document.createElement('textarea');
  textarea.value = text;
  textarea.style.position = 'fixed';
  textarea.style.opacity = '0';
  document.body.appendChild(textarea);
  textarea.select();
  document.execCommand('copy');
  document.body.removeChild(textarea);
}

/**
 * Show a tooltip near an element
 * @param {HTMLElement} element - Element to show tooltip near
 * @param {string} message - Message to display
 */
function showTooltip(element, message) {
  // Create tooltip
  const tooltip = document.createElement('div');
  tooltip.textContent = message;
  tooltip.style.position = 'absolute';
  tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
  tooltip.style.color = 'white';
  tooltip.style.padding = '5px 10px';
  tooltip.style.borderRadius = '4px';
  tooltip.style.fontSize = '14px';
  tooltip.style.zIndex = '1000';
  tooltip.style.pointerEvents = 'none';
  tooltip.style.transition = 'opacity 0.3s ease';
  tooltip.style.opacity = '0';
  
  // Add to document
  document.body.appendChild(tooltip);
  
  // Position tooltip
  const rect = element.getBoundingClientRect();
  tooltip.style.top = `${rect.bottom + window.scrollY + 5}px`;
  tooltip.style.left = `${rect.left + window.scrollX + (rect.width / 2) - (tooltip.offsetWidth / 2)}px`;
  
  // Show tooltip
  setTimeout(() => {
    tooltip.style.opacity = '1';
  }, 10);
  
  // Hide and remove tooltip after delay
  setTimeout(() => {
    tooltip.style.opacity = '0';
    setTimeout(() => {
      document.body.removeChild(tooltip);
    }, 300);
  }, 2000);
}

/**
 * Add parallax effect to the hero image
 */
function setupParallaxEffect() {
  const heroImage = document.querySelector('.unsplash7cyehjgcosi-icon');
  
  if (heroImage) {
    window.addEventListener('scroll', function() {
      const scrollPosition = window.scrollY;
      const parallaxSpeed = 0.4;
      
      // Apply parallax effect
      heroImage.style.transform = `translateY(${scrollPosition * parallaxSpeed}px)`;
    });
  }
}

// Call parallax setup
setupParallaxEffect();