/**
 * Add New Vaccination System
 * Comprehensive OOP-based system for adding new vaccinations
 * Integrates with existing vaccination management system
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */

// ==================== DATA MANAGER ====================

/**
 * Handles all data operations for vaccination management
 */
class AddVaccinationDataManager {
    constructor() {
        this.vaccinations = [];
        this.loadData();
    }

    /**
     * Load vaccinations data from localStorage
     */
    loadData() {
        try {
            this.vaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');
            console.log(`📊 Loaded ${this.vaccinations.length} vaccinations`);
        } catch (error) {
            console.error('❌ Error loading vaccinations:', error);
            this.vaccinations = [];
        }
    }

    /**
     * Save vaccinations data to localStorage
     */
    saveData() {
        try {
            localStorage.setItem('vaccinations', JSON.stringify(this.vaccinations));
            console.log('✅ Vaccinations data saved successfully');
            return true;
        } catch (error) {
            console.error('❌ Error saving vaccinations:', error);
            return false;
        }
    }

    /**
     * Check if vaccination name already exists
     * @param {string} name - Vaccination name
     * @returns {boolean} - True if exists
     */
    vaccinationExists(name) {
        if (!name || !name.trim()) {
            return false;
        }

        const normalizedName = name.trim().toLowerCase();
        return this.vaccinations.some(vaccination =>
            vaccination.name && vaccination.name.toLowerCase() === normalizedName
        );
    }

    /**
     * Add new vaccination
     * @param {Object} vaccinationData - Vaccination data
     * @returns {Object} - Added vaccination
     */
    addVaccination(vaccinationData) {
        const timestamp = new Date().toISOString();

        // Create comprehensive vaccination object
        const newVaccination = {
            id: Date.now().toString(),
            name: vaccinationData.name.trim(),
            type: vaccinationData.type.trim(),
            dose: vaccinationData.dose.trim(),
            timeToTake: vaccinationData.timeToTake,
            dateAdded: timestamp,
            createdAt: timestamp,
            updatedAt: timestamp,
            source: 'addnewvaccination_form'
        };

        // Add to array
        this.vaccinations.push(newVaccination);

        // Save data
        if (!this.saveData()) {
            throw new Error('Failed to save vaccination data');
        }

        console.log(`✅ Vaccination "${newVaccination.name}" added successfully`);
        return newVaccination;
    }

    /**
     * Get vaccination statistics
     * @returns {Object} - Statistics object
     */
    getStatistics() {
        const stats = {
            totalVaccinations: this.vaccinations.length,
            typeDistribution: {},
            recentlyAdded: 0
        };

        // Calculate type distribution
        this.vaccinations.forEach(vaccination => {
            const type = vaccination.type || 'Unknown';
            stats.typeDistribution[type] = (stats.typeDistribution[type] || 0) + 1;
        });

        // Calculate recently added (last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        stats.recentlyAdded = this.vaccinations.filter(vaccination =>
            new Date(vaccination.dateAdded) >= sevenDaysAgo
        ).length;

        return stats;
    }

    /**
     * Trigger storage events for other pages to update
     */
    triggerStorageEvents() {
        window.dispatchEvent(new StorageEvent('storage', {
            key: 'vaccinations',
            newValue: JSON.stringify(this.vaccinations)
        }));
        console.log('📡 Storage events triggered for vaccinations update');
    }
}

// ==================== VALIDATION MANAGER ====================

/**
 * Handles form validation for vaccination data
 */
class AddVaccinationValidationManager {
    /**
     * Validate vaccination name
     * @param {string} name - Vaccination name
     * @returns {Array} - Array of error messages
     */
    validateName(name) {
        const errors = [];

        if (!name || !name.trim()) {
            errors.push('Vaccination name is required');
        } else if (name.trim().length < 2) {
            errors.push('Vaccination name must be at least 2 characters');
        } else if (name.trim().length > 100) {
            errors.push('Vaccination name must be less than 100 characters');
        }

        return errors;
    }

    /**
     * Validate vaccination type
     * @param {string} type - Vaccination type
     * @returns {Array} - Array of error messages
     */
    validateType(type) {
        const errors = [];

        if (!type || !type.trim()) {
            errors.push('Vaccination type is required');
        } else if (type.trim().length < 2) {
            errors.push('Vaccination type must be at least 2 characters');
        }

        return errors;
    }

    /**
     * Validate dose
     * @param {string} dose - Dose amount
     * @returns {Array} - Array of error messages
     */
    validateDose(dose) {
        const errors = [];

        if (!dose || !dose.trim()) {
            errors.push('Dose is required');
        } else if (dose.trim().length < 1) {
            errors.push('Dose must be specified');
        }

        return errors;
    }

    /**
     * Validate time to take
     * @param {string} timeToTake - Time to take
     * @returns {Array} - Array of error messages
     */
    validateTimeToTake(timeToTake) {
        const errors = [];

        if (!timeToTake || !timeToTake.trim()) {
            errors.push('Time to take is required');
        } else {
            // Validate time format (HH:MM)
            const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
            if (!timeRegex.test(timeToTake)) {
                errors.push('Please enter a valid time (HH:MM format)');
            }
        }

        return errors;
    }

    /**
     * Validate all form data
     * @param {Object} formData - Form data object
     * @returns {Array} - Array of all error messages
     */
    validateFormData(formData) {
        const allErrors = [];

        allErrors.push(...this.validateName(formData.name));
        allErrors.push(...this.validateType(formData.type));
        allErrors.push(...this.validateDose(formData.dose));
        allErrors.push(...this.validateTimeToTake(formData.timeToTake));

        return allErrors;
    }
}

// ==================== UI MANAGER ====================

/**
 * Handles UI interactions and notifications
 */
class AddVaccinationUIManager {
    constructor() {
        this.notificationTimeout = null;
    }

    /**
     * Show notification message
     * @param {string} message - Message to display
     * @param {string} type - Type of notification (success, error, warning, info)
     * @param {number} duration - Duration in milliseconds
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Clear existing notification
        this.clearNotification();

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 25px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 400px;
            text-align: center;
            animation: slideDown 0.3s ease-out;
        `;

        // Set colors based on type
        const colors = {
            success: { bg: '#d4edda', color: '#155724', border: '#c3e6cb' },
            error: { bg: '#f8d7da', color: '#721c24', border: '#f5c6cb' },
            warning: { bg: '#fff3cd', color: '#856404', border: '#ffeaa7' },
            info: { bg: '#d1ecf1', color: '#0c5460', border: '#bee5eb' }
        };

        const colorScheme = colors[type] || colors.info;
        notification.style.backgroundColor = colorScheme.bg;
        notification.style.color = colorScheme.color;
        notification.style.border = `1px solid ${colorScheme.border}`;

        notification.textContent = message;

        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                to { transform: translateX(-50%) translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(notification);

        // Auto-remove notification
        this.notificationTimeout = setTimeout(() => {
            this.clearNotification();
        }, duration);

        console.log(`📢 Notification (${type}): ${message}`);
    }

    /**
     * Clear current notification
     */
    clearNotification() {
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        if (this.notificationTimeout) {
            clearTimeout(this.notificationTimeout);
            this.notificationTimeout = null;
        }
    }

    /**
     * Show field error
     * @param {HTMLElement} fieldElement - Field element
     * @param {string} errorMessage - Error message
     */
    showFieldError(fieldElement, errorMessage) {
        this.clearFieldError(fieldElement);

        fieldElement.style.borderColor = '#dc3545';
        fieldElement.style.backgroundColor = '#fff5f5';

        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error-message';
        errorElement.style.cssText = `
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            font-family: Arial, sans-serif;
        `;
        errorElement.textContent = errorMessage;

        fieldElement.parentNode.appendChild(errorElement);
    }

    /**
     * Clear field error
     * @param {HTMLElement} fieldElement - Field element
     */
    clearFieldError(fieldElement) {
        fieldElement.style.borderColor = '';
        fieldElement.style.backgroundColor = '';

        const errorElement = fieldElement.parentNode.querySelector('.field-error-message');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * Clear all field errors
     */
    clearAllFieldErrors() {
        const errorElements = document.querySelectorAll('.field-error-message');
        errorElements.forEach(element => element.remove());

        const inputElements = document.querySelectorAll('input');
        inputElements.forEach(input => {
            input.style.borderColor = '';
            input.style.backgroundColor = '';
        });
    }

    /**
     * Highlight field success
     * @param {HTMLElement} fieldElement - Field element
     */
    highlightFieldSuccess(fieldElement) {
        fieldElement.style.borderColor = '#28a745';
        fieldElement.style.backgroundColor = '#f8fff9';

        setTimeout(() => {
            fieldElement.style.borderColor = '';
            fieldElement.style.backgroundColor = '';
        }, 2000);
    }

    /**
     * Show loading state on button (preserves original HTML structure)
     * @param {HTMLElement} buttonElement - Button element
     */
    showLoading(buttonElement) {
        if (buttonElement) {
            buttonElement.disabled = true;
            buttonElement.style.opacity = '0.7';

            // Find the .btntext element inside the button
            const btnTextElement = buttonElement.querySelector('.btntext');
            if (btnTextElement) {
                const originalText = btnTextElement.textContent;
                btnTextElement.textContent = 'Saving...';
                btnTextElement.dataset.originalText = originalText;
            }
        }
    }

    /**
     * Hide loading state on button (restores original HTML structure)
     * @param {HTMLElement} buttonElement - Button element
     */
    hideLoading(buttonElement) {
        if (buttonElement) {
            buttonElement.disabled = false;
            buttonElement.style.opacity = '';

            // Find the .btntext element inside the button
            const btnTextElement = buttonElement.querySelector('.btntext');
            if (btnTextElement) {
                const originalText = btnTextElement.dataset.originalText;
                if (originalText) {
                    btnTextElement.textContent = originalText;
                    delete btnTextElement.dataset.originalText;
                }
            }
        }
    }
}

// ==================== MAIN CONTROLLER ====================

/**
 * Main controller for Add New Vaccination system
 */
class AddVaccinationController {
    constructor() {
        this.dataManager = new AddVaccinationDataManager();
        this.validationManager = new AddVaccinationValidationManager();
        this.uiManager = new AddVaccinationUIManager();

        this.elements = {};
        this.formData = {};

        console.log('🎯 AddVaccinationController initialized');
    }

    /**
     * Initialize the system
     */
    init() {
        try {
            this.initializeElements();
            this.setupEventListeners();
            this.setupRealTimeValidation();

            console.log('✅ Add Vaccination system initialized successfully');
            this.uiManager.showNotification('Add Vaccination system ready!', 'info', 3000);

        } catch (error) {
            console.error('❌ Failed to initialize Add Vaccination system:', error);
            this.uiManager.showNotification('Failed to initialize system. Please refresh the page.', 'error');
        }
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        // Get form inputs based on HTML structure
        const inputs = document.querySelectorAll('.data-filled');

        if (inputs.length >= 4) {
            this.elements.nameInput = inputs[0];
            this.elements.typeInput = inputs[1];
            this.elements.doseInput = inputs[2];
            this.elements.timeToTakeInput = inputs[3];
        } else {
            throw new Error('Required form inputs not found');
        }

        // Get save button
        this.elements.saveButton = document.querySelector('.frame-9');
        if (!this.elements.saveButton) {
            throw new Error('Save button not found');
        }

        console.log('📋 DOM elements initialized:', Object.keys(this.elements));
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Save button click
        this.elements.saveButton.addEventListener('click', () => {
            this.handleSave();
        });

        // Enter key on inputs
        Object.values(this.elements).forEach(element => {
            if (element && element.tagName === 'INPUT') {
                element.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleSave();
                    }
                });
            }
        });

        console.log('🔗 Event listeners setup complete');
    }

    /**
     * Setup real-time validation
     */
    setupRealTimeValidation() {
        // Name input - real-time validation and duplicate check
        if (this.elements.nameInput) {
            this.elements.nameInput.addEventListener('input', (e) => {
                this.handleNameInput(e);
            });

            this.elements.nameInput.addEventListener('blur', (e) => {
                this.validateVaccinationName(e.target.value);
            });
        }

        // Type input - real-time validation
        if (this.elements.typeInput) {
            this.elements.typeInput.addEventListener('input', (e) => {
                this.handleTypeInput(e);
            });
        }

        // Dose input - real-time validation
        if (this.elements.doseInput) {
            this.elements.doseInput.addEventListener('input', (e) => {
                this.handleDoseInput(e);
            });
        }

        // Time input - real-time validation
        if (this.elements.timeToTakeInput) {
            this.elements.timeToTakeInput.addEventListener('input', (e) => {
                this.handleTimeInput(e);
            });
        }

        console.log('✅ Real-time validation setup complete');
    }

    /**
     * Handle vaccination name input
     * @param {Event} event - Input event
     */
    handleNameInput(event) {
        const name = event.target.value.trim();
        this.formData.name = name;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Real-time validation (debounced)
        clearTimeout(this.nameInputTimeout);
        this.nameInputTimeout = setTimeout(() => {
            if (name) {
                this.validateVaccinationName(name);
            }
        }, 500);
    }

    /**
     * Validate vaccination name and show feedback
     * @param {string} name - Vaccination name
     */
    validateVaccinationName(name) {
        if (!name || !name.trim()) {
            return;
        }

        // Check for duplicates
        const exists = this.dataManager.vaccinationExists(name);

        if (exists) {
            this.uiManager.showFieldError(this.elements.nameInput, 'Vaccination with this name already exists');
            this.uiManager.showNotification('Vaccination name already exists. Please choose a different name.', 'warning', 3000);
        } else {
            // Validate name format
            const errors = this.validationManager.validateName(name);
            if (errors.length > 0) {
                this.uiManager.showFieldError(this.elements.nameInput, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(this.elements.nameInput);
            }
        }
    }

    /**
     * Handle type input
     * @param {Event} event - Input event
     */
    handleTypeInput(event) {
        const value = event.target.value.trim();
        this.formData.type = value;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Real-time validation
        if (value) {
            const errors = this.validationManager.validateType(value);
            if (errors.length > 0) {
                this.uiManager.showFieldError(event.target, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(event.target);
            }
        }
    }

    /**
     * Handle dose input
     * @param {Event} event - Input event
     */
    handleDoseInput(event) {
        const value = event.target.value.trim();
        this.formData.dose = value;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Real-time validation
        if (value) {
            const errors = this.validationManager.validateDose(value);
            if (errors.length > 0) {
                this.uiManager.showFieldError(event.target, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(event.target);
            }
        }
    }

    /**
     * Handle time input
     * @param {Event} event - Input event
     */
    handleTimeInput(event) {
        const value = event.target.value.trim();
        this.formData.timeToTake = value;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Real-time validation
        if (value) {
            const errors = this.validationManager.validateTimeToTake(value);
            if (errors.length > 0) {
                this.uiManager.showFieldError(event.target, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(event.target);
            }
        }
    }

    /**
     * Collect form data
     * @returns {Object} - Form data object
     */
    collectFormData() {
        return {
            name: this.elements.nameInput.value.trim(),
            type: this.elements.typeInput.value.trim(),
            dose: this.elements.doseInput.value.trim(),
            timeToTake: this.elements.timeToTakeInput.value.trim()
        };
    }

    /**
     * Validate entire form
     * @returns {boolean} - Validation result
     */
    validateForm() {
        const formData = this.collectFormData();
        const errors = this.validationManager.validateFormData(formData);

        // Clear all previous errors
        this.uiManager.clearAllFieldErrors();

        if (errors.length > 0) {
            // Show first error as notification
            this.uiManager.showNotification(errors[0], 'error');

            // Show field-specific errors
            this.showFieldErrors(errors);
            return false;
        }

        // Additional check: verify vaccination name doesn't exist
        const exists = this.dataManager.vaccinationExists(formData.name);
        if (exists) {
            this.uiManager.showFieldError(this.elements.nameInput, 'Vaccination with this name already exists');
            this.uiManager.showNotification('Please enter a unique vaccination name', 'error');
            return false;
        }

        return true;
    }

    /**
     * Show field-specific errors
     * @param {Array} errors - Array of error messages
     */
    showFieldErrors(errors) {
        errors.forEach(error => {
            if (error.toLowerCase().includes('name')) {
                this.uiManager.showFieldError(this.elements.nameInput, error);
            } else if (error.toLowerCase().includes('type')) {
                this.uiManager.showFieldError(this.elements.typeInput, error);
            } else if (error.toLowerCase().includes('dose')) {
                this.uiManager.showFieldError(this.elements.doseInput, error);
            } else if (error.toLowerCase().includes('time')) {
                this.uiManager.showFieldError(this.elements.timeToTakeInput, error);
            }
        });
    }

    /**
     * Handle save button click
     */
    async handleSave() {
        console.log('💾 Save button clicked');

        if (!this.validateForm()) {
            console.log('❌ Form validation failed');
            return;
        }

        const formData = this.collectFormData();
        console.log('📝 Form data collected:', formData);

        // Show loading state
        this.uiManager.showLoading(this.elements.saveButton);

        try {
            // Add vaccination
            const savedVaccination = this.dataManager.addVaccination(formData);

            console.log('✅ Vaccination saved successfully:', savedVaccination);

            // Show success message with details
            const stats = this.dataManager.getStatistics();
            this.uiManager.showNotification(
                `Vaccination "${savedVaccination.name}" saved successfully! Total vaccinations: ${stats.totalVaccinations}`,
                'success',
                5000
            );

            // Highlight updated fields
            this.highlightUpdatedFields();

            // Trigger storage events for other pages to update
            this.dataManager.triggerStorageEvents();

            // Clear form after delay
            setTimeout(() => {
                this.clearForm();
            }, 2000);

        } catch (error) {
            console.error('❌ Error saving vaccination:', error);
            this.uiManager.showNotification(
                `Failed to save vaccination: ${error.message}`,
                'error'
            );
        } finally {
            // Hide loading state
            this.uiManager.hideLoading(this.elements.saveButton);
        }
         // Navigate back to vaccination page
      
        window.parent.location.reload();
        
    }

    /**
     * Highlight updated fields with success styling
     */
    highlightUpdatedFields() {
        const fields = [
            this.elements.nameInput,
            this.elements.typeInput,
            this.elements.doseInput,
            this.elements.timeToTakeInput
        ];

        fields.forEach(field => {
            if (field) {
                this.uiManager.highlightFieldSuccess(field);
            }
        });
    }

    /**
     * Clear form to initial state
     */
    clearForm() {
        // Clear form data
        this.formData = {};

        // Clear all input fields
        Object.values(this.elements).forEach(element => {
            if (element && element.tagName === 'INPUT') {
                element.value = '';
            }
        });

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        console.log('🔄 Form cleared');
    }

    /**
     * Get vaccination statistics for display
     * @returns {Object} - Statistics object
     */
    getStatistics() {
        return this.dataManager.getStatistics();
    }
}

// ==================== INITIALIZATION ====================

/**
 * Initialize the Add Vaccination system when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing Add Vaccination system...');

    try {
        // Create and initialize the controller
        const addVaccinationController = new AddVaccinationController();
        addVaccinationController.init();

        // Make it globally accessible for debugging and external access
        window.addVaccinationController = addVaccinationController;

        console.log('🎉 Add Vaccination system ready!');
        console.log('Available global methods:');
        console.log('- window.addVaccinationController.getStatistics()');
        console.log('- window.addVaccinationController.clearForm()');

    } catch (error) {
        console.error('💥 Failed to initialize Add Vaccination system:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        errorDiv.textContent = 'Failed to initialize the vaccination form. Please refresh the page.';
        document.body.appendChild(errorDiv);
    }
});

// ==================== UTILITY FUNCTIONS ====================

/**
 * Add new vaccination (utility function)
 * @param {string} name - Vaccination name
 * @param {string} type - Vaccination type
 * @param {string} dose - Dose amount
 * @param {string} timeToTake - Time to take
 * @returns {Object|null} - Added vaccination or null
 */
window.addNewVaccination = function(name, type, dose, timeToTake) {
    if (!window.addVaccinationController) {
        console.error('Add Vaccination system not initialized');
        return null;
    }

    try {
        return window.addVaccinationController.dataManager.addVaccination({
            name,
            type,
            dose,
            timeToTake
        });
    } catch (error) {
        console.error('Error adding vaccination:', error);
        return null;
    }
};

/**
 * Check if vaccination exists (utility function)
 * @param {string} name - Vaccination name
 * @returns {boolean} - True if exists
 */
window.vaccinationExists = function(name) {
    if (!window.addVaccinationController) {
        console.error('Add Vaccination system not initialized');
        return false;
    }

    return window.addVaccinationController.dataManager.vaccinationExists(name);
};

/**
 * Get vaccination statistics (utility function)
 * @returns {Object|null} - Statistics or null
 */
window.getVaccinationStatistics = function() {
    if (!window.addVaccinationController) {
        console.error('Add Vaccination system not initialized');
        return null;
    }

    return window.addVaccinationController.dataManager.getStatistics();
};

// ==================== DEBUG FUNCTIONS ====================

/**
 * Debug function to check system status
 */
window.debugAddVaccination = function() {
    console.log('=== Add Vaccination Debug Info ===');

    if (!window.addVaccinationController) {
        console.error('❌ Add Vaccination system not initialized');
        return;
    }

    console.log('✅ System initialized');
    console.log('📊 Vaccinations loaded:', window.addVaccinationController.dataManager.vaccinations.length);
    console.log('📝 Form data:', window.addVaccinationController.formData);
    console.log('🔍 Elements:', Object.keys(window.addVaccinationController.elements));

    // Check form elements
    const elements = window.addVaccinationController.elements;
    console.log('Form elements status:');
    Object.entries(elements).forEach(([key, element]) => {
        console.log(`  ${key}: ${element ? '✅ Found' : '❌ Missing'}`);
    });

    // Show statistics
    const stats = window.addVaccinationController.dataManager.getStatistics();
    console.log('📊 Statistics:', stats);
};

/**
 * Test vaccination creation
 * @param {string} name - Vaccination name to test
 * @param {string} type - Vaccination type to test
 * @param {string} dose - Dose to test
 * @param {string} time - Time to test
 */
window.testVaccinationCreation = function(name = 'Test Vaccine', type = 'Dairy', dose = '5ml', time = '08:00') {
    console.log('🧪 Testing vaccination creation...');

    if (!window.addVaccinationController) {
        console.error('❌ Add Vaccination system not initialized');
        return;
    }

    try {
        // Check if vaccination already exists
        const exists = window.addVaccinationController.dataManager.vaccinationExists(name);
        if (exists) {
            console.log(`❌ Vaccination "${name}" already exists`);
            return;
        }

        console.log(`✅ Creating vaccination: ${name}`);

        // Add vaccination
        const result = window.addVaccinationController.dataManager.addVaccination({
            name,
            type,
            dose,
            timeToTake: time
        });

        console.log('✅ Vaccination created successfully:', result);

        // Show updated statistics
        const stats = window.addVaccinationController.dataManager.getStatistics();
        console.log('📊 Updated statistics:', stats);

        return result;

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
};

/**
 * Show all vaccinations
 */
window.showAllVaccinations = function() {
    console.log('💉 ALL VACCINATIONS');
    console.log('==================');

    if (!window.addVaccinationController) {
        console.error('❌ Add Vaccination system not initialized');
        return;
    }

    const vaccinations = window.addVaccinationController.dataManager.vaccinations;

    if (vaccinations.length === 0) {
        console.log('❌ No vaccinations found');
        return;
    }

    vaccinations.forEach((vaccination, index) => {
        console.log(`${index + 1}. ${vaccination.name}`);
        console.log(`   Type: ${vaccination.type}`);
        console.log(`   Dose: ${vaccination.dose}`);
        console.log(`   Time to take: ${vaccination.timeToTake}`);
        console.log(`   Date added: ${new Date(vaccination.dateAdded).toLocaleDateString()}`);
        console.log('   ---');
    });

    return vaccinations;
};

console.log(`
💉 ADD NEW VACCINATION SYSTEM LOADED
===================================

🔍 DEBUG FUNCTIONS AVAILABLE:
   • debugAddVaccination() - Check system status
   • testVaccinationCreation(name, type, dose, time) - Test creating vaccination
   • showAllVaccinations() - Show all vaccinations
   • addNewVaccination(name, type, dose, time) - Add vaccination programmatically
   • vaccinationExists(name) - Check if vaccination exists
   • getVaccinationStatistics() - Get vaccination statistics

✅ Ready for vaccination management!
`);