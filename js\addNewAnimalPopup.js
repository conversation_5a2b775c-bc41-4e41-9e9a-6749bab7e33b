/**
 * AddNewAnimal popup class
 * This file contains the implementation of the Add New Animal popup
 */

class AddNewAnimalPopup extends Popup {
    constructor() {
        super('choose-update-milch'); // Use the CSS class from addnewanimal.css
        this.animalData = {};
    }

    // Create the popup content
    createPopupContent() {
        // Create the frame-4 div with proper styling
        const frame4 = document.createElement('div');
        // Apply CSS class
        this.applyFrame4Styles(frame4);

        frame4.innerHTML = `
            <div class="frame-5">
                <img class="update" src="icons/add.png">
                <div class="text-wrapper-5">Add New Animal</div>
            </div>

            <div class="frame-6">
                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Type</div>
                        <form class="frame-111">
                            <div class="raddiv">
                                <input type="radio" name="access" id="newborn" class="radio">
                                <label for="newborn">Newborn</label>
                            </div>
                            <div class="raddiv">
                                <input type="radio" name="access" id="dairy" class="radio">
                                <label for="dairy">Dairy</label>
                            </div>
                            <div class="raddiv">
                                <input type="radio" name="access" id="fattening" class="radio">
                                <label for="fattening">Fattening</label>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Code</div>
                        <input class="data-filled">
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Herd Number</div>
                        <input class="data-filled">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Weight</div>
                        <input class="data-filled">
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Gender</div>
                        <form class="frame-11">
                            <div class="raddiv">
                                <input type="radio" name="gender" id="female" class="radio">
                                <label for="female">Female</label>
                            </div>
                            <div class="raddiv">
                                <input type="radio" name="gender" id="male" class="radio">
                                <label for="male">Male</label>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Date of weight</div>
                        <input class="data-filled" type="date">
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Date of Birth</div>
                        <input class="data-filled" type="date">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Healthcare notes</div>
                        <input class="textarea">
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Taken Vaccination</div>
                        <input class="textarea">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-8">
                        <button class="frame-9" title="Save animal data to the database">
                            <img class="update" src="icons/save.png">
                            <div class="btntext">Save Animal</div>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add close button (X mark)
        this.addCloseButton(frame4);

        return frame4;
    }

    setupEventListeners() {
        // Get save button
        const saveButton = this.popupContainer.querySelector('.frame-9');
        if (saveButton) {
            saveButton.addEventListener('click', () => this.saveAnimal());
        }

        // Get radio buttons
        const typeRadios = this.popupContainer.querySelectorAll('input[name="access"]');
        typeRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                this.animalData.type = radio.id;
            });
        });

        // Get gender radio buttons
        const genderRadios = this.popupContainer.querySelectorAll('input[name="gender"]');
        genderRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                this.animalData.gender = radio.id;
            });
        });

        // Get input fields
        const inputs = this.popupContainer.querySelectorAll('input.data-filled, input.textarea');
        inputs.forEach(input => {
            input.addEventListener('change', (e) => {
                // Determine field name based on the label
                const label = e.target.closest('.frame-7').querySelector('.text-wrapper-6').textContent;
                const fieldName = this.getLabelFieldName(label);
                this.animalData[fieldName] = e.target.value;
            });
        });
    }

    // Convert label text to camelCase field name
    getLabelFieldName(label) {
        const labelMap = {
            'Code': 'code',
            'Herd Number': 'herdNumber',
            'Weight': 'weight',
            'Date of weight': 'dateOfWeight',
            'Date of Birth': 'dateOfBirth',
            'Healthcare notes': 'healthcareNotes',
            'Taken Vaccination': 'takenVaccination'
        };

        return labelMap[label] || label.toLowerCase().replace(/\s(.)/g, ($1) => $1.toUpperCase()).replace(/\s/g, '');
    }

    // Save animal data
    saveAnimal() {
        // Validate required fields
        if (!this.animalData.code || !this.animalData.type) {
            alert('Please fill in all required fields (Code and Type are mandatory)');
            return;
        }

        // If animal is dairy type, ensure it's female
        if (this.animalData.type === 'dairy' && this.animalData.gender !== 'female') {
            alert('Dairy animals must be female');
            return;
        }

        // Save to animals table
        AnimalTableManager.addAnimal(this.animalData);

        // If dairy animal, also save to dairy table
        if (this.animalData.type === 'dairy') {
            // Code to save to dairy table would go here
            console.log('Saving to dairy table:', this.animalData);
        }

        // If newborn animal, also save to newborn table
        if (this.animalData.type === 'newborn') {
            // Code to save to newborn table would go here
            console.log('Saving to newborn table:', this.animalData);
        }

        alert('Animal saved successfully!');
        this.closePopup();
    }
}
