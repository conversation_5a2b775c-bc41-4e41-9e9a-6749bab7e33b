<!DOCTYPE html>
    <html>

    <head>
        <meta charset="utf-8" />

        <link rel="stylesheet" href="css/makenewfeed.css" />
       
    </head>

    <body>
    <div id="notification-container"></div>
        <div class="makenewfeed">
           
           
              

                    <div class="parent1">
                        <div class="frame-5">
                            <img class="update" src="icons/feed.png">

                            <div class="text-wrapper-5"> Make New Feed</div>
                        </div>

                        <div class="frame-4">
                                
                            <div class="row">
                             <div class="frame-7">
                                <div class="text-wrapper-6">Feed Name</div>
                                <input class="data-filled" placeholder="feed name"></input>
                            </div>

                             <div class="frame-7">
                                <div class="text-wrapper-6">Growth rate</div>
                                <input class="data-filled" placeholder="Growth rate"></input>
                            </div>

                            <div class="frame-7">
                                <div class="text-wrapper-6">Weight</div>
                                <input class="data-filled" placeholder="Weight"></input>
                            </div>                            <div class="frame-7">
                                <div class="text-wrapper-6">Gender</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn">Select Gender</button>
                                    <div class="dropdown-content">
                                        <a href="#" onclick="selectGender('Male')">Male</a>
                                        <a href="#" onclick="selectGender('Female')">Female</a>
                                    </div>
                                </div>
                            </div>
                            </div>

                            <div class="row">                            <div class="frame-7">
                                <div class="text-wrapper-6">Animal's Type</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn">Select Animal Type</button>
                                    <div class="dropdown-content">
                                        <a href="#" onclick="selectAnimalType('Newborn')">Newborn</a>
                                        <a href="#" onclick="selectAnimalType('Dairy')">Dairy</a>
                                        <a href="#" onclick="selectAnimalType('Fattening')">Fattening</a>
                                    </div>
                                </div>
                            </div>                             <div class="frame-7">
                                <div class="text-wrapper-6">Feed Type</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn">Select Feed Type</button>
                                    <div class="dropdown-content">
                                        <a href="#" onclick="selectFeedType('TMR')">TMR</a>
                                        <a href="#" onclick="selectFeedType('CFM')">CFM</a>
                                    </div>
                                </div>
                            </div>                              <div class="frame-7">
                                <div class="text-wrapper-6">Season</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn">Select Season</button>
                                    <div class="dropdown-content">
                                        <a href="#" onclick="selectSeason('Summer')">Summer</a>
                                        <a href="#" onclick="selectSeason('Winter')">Winter</a>
                                    </div>
                                </div>
                            </div>
                            </div>

                           
                                <div class="row">
                            <div class="frame-7">
                                <div class="text-wrapper-6"> Protein percentage</div><!--show data from backend-->
                                <div class="data-filled1" placeholder="protein "></div><!--show data from backend-->
                            </div>
                            <div class="frame-7">
                                <div class="text-wrapper-6">TDN percentage</div>
                                <div class="data-filled1" placeholder="TDN"></div>
                            </div>
                        </div>

                        
                        </div>
                        <div class="tablecontainer">                             <div class="row">
                            <div class="frame-7">
                                
                                <div class="dropdown">
                                    <button class="data-filled dropbtn">Select Ingredient Type</button>                                    <div class="dropdown-content">
                                        <a href="#" onclick="selectIngredientType('Concentrates')">Concentrates</a>
                                        <a href="#" onclick="selectIngredientType('Roughages')">Roughages</a>
                                        <a href="#" onclick="selectIngredientType('Mill by Product')">Mill by Product</a>
                                        <a href="#" onclick="selectIngredientType('Oilseed by Product')">Oilseed by Product</a>
                                        <a href="#" onclick="selectIngredientType('Forages')">Forages</a>
                                    </div>
                                </div>
                            </div>                            <div class="frame-7">
                                <div class="text-wrapper-6">Ingredients</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn" id="ingredientsDropdown">Select Ingredient</button>
                                    <div class="dropdown-content" id="ingredientsList">
                                        <!-- Will be populated dynamically based on selected type -->
                                    </div>
                                </div>
                            </div>
                            <div class="frame-7"><div class="text-wrapper-6">Price per kg</div>
                                            <input class="data-filled" placeholder="price per kg"></input></div>
                                           <div class="data-filled11"> <img class="img" src="icons/addingred.png"></div>
                         </div>
                            <table>
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        
                                    </tr>
                                </thead>
                                <tbody>

                                    <!--<tr>
                                        <td><div class="data-filled">ingrediant</td>
                                        <td><div class="frame-7"><div class="text-wrapper-6">Price per kg</div>
                                            <input class="data-filled" placeholder="price per kg"></input></div></td>
                                        <td><div class="frame-7"><div class="text-wrapper-6"></div><div class="data-filled" >weightit showes the weight of each ingred--</div></div></td>
                                        <td><img class="img" src="icons/delingred.png"></td>
                                    </tr>-->
                                </tbody>
                            </table>
                        </div>

                         <div class="frame-8">
                           <div class="frame-5" ><img class="update" src="icons/ingrediant icon.png">
                            <button class="btntext1">Finish Choose Ingrediants</button>
                        </div>
                        <button id="updateIngredientsBtn" class="frame-9" title="Update data at ingredients page">

                            <div class="btntext">Save Feed</div>
                        </button>


                    </div>

                    </div>

                   
               
            </div>
       

       
        <script src="js/makenewfeed.js"></script>
    </body>

    </html>