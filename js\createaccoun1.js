document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const form = document.querySelector('.dta-entered');
    const nameInput = document.querySelector('input[placeholder="Name"]');
    const emailInput = document.querySelector('input[type="email"]');
    const passwordInput = document.querySelector('input[type="password"]');
    const ownerRadio = document.getElementById('owner');
    const engineerRadio = document.getElementById('engineer');
    const createAccountBtn = document.querySelector('.component');
    
    // User constructor function
    function User(name, email, password, role) {
        this.name = name;
        this.email = email;
        this.password = password;
        this.role = role;
        this.dateCreated = new Date().toISOString();
    }
    
    // Handle create account button click
    createAccountBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Get form values
        const name = nameInput.value.trim();
        const email = emailInput.value.trim();
        const password = passwordInput.value.trim();
        
        // Get selected role
        let role = '';
        if (ownerRadio.checked) {
            role = 'owner';
        } else if (engineerRadio.checked) {
            role = 'engineer';
        }
        
        // Validate form
        if (!name) {
            alert('Please enter your name');
            nameInput.focus();
            return;
        }
        
        if (!email) {
            alert('Please enter your email');
            emailInput.focus();
            return;
        }
        
        if (!validateEmail(email)) {
            alert('Please enter a valid email address');
            emailInput.focus();
            return;
        }
        
        if (!password) {
            alert('Please enter a password');
            passwordInput.focus();
            return;
        }
        
        if (password.length < 6) {
            alert('Password must be at least 6 characters long');
            passwordInput.focus();
            return;
        }
        
        if (!role) {
            alert('Please select your role (Owner or Engineer)');
            return;
        }
        
        // Create new user object
        const newUser = new User(name, email, password, role);
        
        // Save user data
        saveUser(newUser);
    });
    
    // Function to validate email format
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    // Function to save user data
    function saveUser(user) {
        // In a real application, this would send data to a server
        console.log('Creating account for:', user);
        
        // For demo purposes, store in localStorage
        let users = JSON.parse(localStorage.getItem('users') || '[]');
        
        // Check if email already exists
        const emailExists = users.some(existingUser => existingUser.email === user.email);
        if (emailExists) {
            alert('An account with this email already exists');
            return;
        }
        
        // Add new user
        users.push(user);
        localStorage.setItem('users', JSON.stringify(users));
        
        // Store current user in session
        sessionStorage.setItem('currentUser', JSON.stringify(user));
        
        alert('Account created successfully!');
        
        // Redirect to dashboard or home page
        window.location.href = 'dashboard.html';
    }
    
    // Google sign-in button
    const googleSignInBtn = document.querySelector('.div-2');
    if (googleSignInBtn) {
        googleSignInBtn.addEventListener('click', function() {
            alert('Google sign-in functionality would be implemented here');
            // In a real application, this would integrate with Google OAuth
        });
    }
});