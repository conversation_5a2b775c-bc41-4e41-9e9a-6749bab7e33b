@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0; /* Changed from 0% */
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2; /* Simpler, or choose the -webkit-focus-ring-color if preferred */
  /* outline: -webkit-focus-ring-color auto 5px !important; */ /* This one was overriding the previous */
}
a {
  text-decoration: none;
}
.container{
    width: 100%;
    height: 100%;
    background-color: transparent;
}

.overlay {
   position:absolute ;
    top: 20%;
    left:70%;




}
.delete-popup{
    padding-top: 2%;
    position: relative;
    width: 400px;

    height: 250px;
    background-color: #fff;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}
 .delete-popup img{
  margin-top: 7% ;
    width: 55px;
    height: 55px;
}
.statement{
    margin-top: 5%;
    font-size: 30px;
    font-weight: bold;
    color: #333;
}

.click{
    margin-top: 3%;
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    padding: 0 20px;
}

.delete-popup button{
    margin-top: 5%;
    width: 45%;
    height: 40px;
    border-radius: 12px;
    border: none;
    cursor: pointer;
     background-color:#32CD32;
}


.txt{
    color:#fff;
    font-size: 16px;
    font-weight: bold;
}
