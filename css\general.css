body {
  margin: 0;
  line-height: normal;
}

.popup-overlay {
  display: flex;
  flex-direction: column;
  position: fixed;
  inset: 0;
}

:root {
  /* Fonts */
  --highlights: <PERSON>o;
  --font-inter: Inter;
  --font-roboto-serif: "Roboto Serif";
  --font-source-code-pro: "Source Code Pro";
  --font-abeezee: <PERSON><PERSON><PERSON><PERSON>;
  --font-rem: REM;

  /* font sizes */
  --font-size-3xs: 10px;
  --font-size-xs: 12px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-lgi: 19px;
  --font-size-xl: 20px;
  --font-size-3xl: 22px;
  --font-size-5xl: 24px;
  --font-size-7xl: 26px;
  --font-size-10xl: 29px;
  --font-size-11xl: 30px;
  --highlights-size: 32px;
  --font-size-17xl: 36px;
  --font-size-58xl: 77px;

  /* Colors */
  --color-black: #000;
  --color-darkslategray-100: #0b291a;
  --color-darkslategray-200: rgba(11, 41, 26, 0.6);
  --color-darkslategray-300: rgba(51, 51, 51, 0.2);
  --color-darkslategray-400: rgba(51, 51, 51, 0.09);
  --color-floralwhite: #fbfaf0;
  --color-gainsboro: #e3e4e4;
  --color-gray-100: #1e2423;
  --color-gray-200: rgba(0, 0, 0, 0.2);
  --color-gray-300: rgba(0, 0, 0, 0.15);
  --color-gray-400: rgba(0, 0, 0, 0.9);
  --color-gray-500: rgba(0, 0, 26, 0.15);
  --color-gray-600: rgba(0, 0, 0, 0.7);
  --color-gray-700: rgba(0, 0, 26, 0.35);
  --color-gray-800: rgba(0, 0, 26, 0.3);
  --color-lightgray: #d7d7d5;
  --color-limegreen-100: #32cd32;
  --color-white: #fff;
  --color-whitesmoke: #f9f9f9;
  --color-yellowgreen-100: rgba(174, 223, 50, 0.8);

  /* Gaps */
  --gap-0: 0px;
  --gap-12xs: 1px;
  --gap-11xs-9: 1.9px;
  --gap-11xs: 2px;
  --gap-10xs: 3px;
  --gap-9xs: 4px;
  --gap-5xs: 8px;
  --gap-3xs: 10px;
  --gap-xs: 12px;
  --gap-smi: 13px;
  --gap-mini: 15px;
  --gap-base: 16px;
  --gap-mid: 17px;
  --gap-lgi: 19px;
  --gap-xl: 20px;
  --gap-4xl: 23px;
  --gap-5xl: 24px;
  --gap-8xl: 27px;
  --gap-14xl: 33px;
  --gap-15xl: 34px;
  --gap-27xl-3: 46.3px;
  --gap-29xl: 48px;
  --gap-101xl: 120px;

  /* Paddings */
  --padding-12xs: 1px;
  --padding-10xs: 3px;
  --padding-9xs: 4px;
  --padding-8xs: 5px;
  --padding-7xs: 6px;
  --padding-6xs: 7px;
  --padding-5xs: 8px;
  --padding-4xs: 9px;
  --padding-3xs: 10px;
  --padding-2xs: 11px;
  --padding-xs: 12px;
  --padding-smi: 13px;
  --padding-sm: 14px;
  --padding-mini: 15px;
  --padding-base: 16px;
  --padding-mid: 17px;
  --padding-lg: 18px;
  --padding-lgi: 19px;
  --padding-xl: 20px;
  --padding-2xl: 21px;
  --padding-4xl: 23px;
  --padding-5xl: 24px;
  --padding-6xl: 25px;
  --padding-7xl: 26px;
  --padding-9xl: 28px;
  --padding-11xl: 30px;
  --padding-13xl: 32px;
  --padding-14xl: 33px;
  --padding-15xl: 34px;
  --padding-16xl: 35px;
  --padding-19xl: 38px;
  --padding-22xl: 41px;
  --padding-24xl: 43px;
  --padding-26xl: 45px;
  --padding-35xl: 54px;
  --padding-71xl: 90px;
  --padding-1355xl: 1374px;

  /* Border radiuses */
  --br-7xs: 6px;
  --br-base: 16px;
  --br-xl: 20px;
  --br-11xl: 30px;
}
