// Function to validate numeric input
function validateNumericInput(value, fieldName) {
    const num = parseFloat(value);
    if (isNaN(num) || num < 0) {
        showNotification(`Please enter a valid number for ${fieldName}`, 'error');
        return false;
    }
    return true;
}

// Function to show notifications
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    const container = document.getElementById('notification-container');
    if (!container) {
        const newContainer = document.createElement('div');
        newContainer.id = 'notification-container';
        document.body.appendChild(newContainer);
    }
    
    (container || document.getElementById('notification-container')).appendChild(notification);
    
    setTimeout(() => {
        notification.style.animation = 'fadeOut 0.5s forwards';
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 3000);
}

// Function to create table row for ingredient
function createTableRow(ingredient, price, weight) {
    const tr = document.createElement('tr');
    tr.innerHTML = `
        <td><div class="data-filled">${ingredient}</div></td>
        <td>
            <div class="frame-7">
                <div class="text-wrapper-6">Price per kg</div>
                <div class="data-filled">${price}</div>
            </div>
        </td>
        <td>
            <div class="frame-7">
                <div class="text-wrapper-6"></div>
                <div class="data-filled">${weight}</div>
            </div>
        </td>
        <td>
            <img class="img delete-ingredient" src="icons/delingred.png">
        </td>
    `;
    return tr;
}// Function to update nutrient percentages
function updateNutrientPercentages() {
    const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
    const rows = document.querySelectorAll('table tbody tr');
    
    let totalProtein = 0;
    let totalTDN = 0;
    let totalWeight = 0;
    
    rows.forEach(row => {
        const name = row.querySelector('td:first-child .data-filled').textContent;
        const weight = parseFloat(row.querySelector('td:nth-child(3) .data-filled').textContent);
        const ingredient = ingredients.find(i => i.name === name);
        
        if (ingredient && !isNaN(weight)) {
            // Convert percentage to decimal for calculation
            const proteinPercent = parseFloat(ingredient.proteins) / 100;
            const tdnPercent = parseFloat(ingredient.tdn) / 100;
            
            totalProtein += (proteinPercent * weight);
            totalTDN += (tdnPercent * weight);
            totalWeight += weight;
        }
    });
    
    if (totalWeight > 0) {
        const proteinPercentage = (totalProtein / totalWeight).toFixed(2);
        const tdnPercentage = (totalTDN / totalWeight).toFixed(2);
        
        const proteinElement = document.querySelector('.row .frame-7 .data-filled1');
        const tdnElement = document.querySelectorAll('.row .frame-7 .data-filled1')[1];
        
        if (proteinElement) proteinElement.textContent = proteinPercentage + '%';
        if (tdnElement) tdnElement.textContent = tdnPercentage + '%';
    }
}

// Function to remove ingredient
function removeIngredient(element) {
    const row = element.closest('tr');
    row.remove();
    updateNutrientPercentages();
}

document.addEventListener('DOMContentLoaded', () => {
    // Setup all dropdowns
    const dropdowns = {
        gender: {
            btn: document.getElementById('genderBtn'),
            content: document.getElementById('genderDropdown')
        },
        animalType: {
            btn: document.getElementById('animalTypeBtn'),
            content: document.getElementById('animalTypeDropdown')
        },
        feedType: {
            btn: document.getElementById('feedTypeBtn'),
            content: document.getElementById('feedTypeDropdown')
        },
        season: {
            btn: document.getElementById('seasonBtn'),
            content: document.getElementById('seasonDropdown')
        },
        ingredientType: {
            btn: document.getElementById('ingredientTypeBtn'),
            content: document.getElementById('ingredientTypeDropdown')
        },
        ingredients: {
            btn: document.getElementById('ingredientsDropdown'),
            content: document.getElementById('ingredientsList')
        }
    };

    // Function to close all dropdowns
    function closeAllDropdowns() {
        Object.values(dropdowns).forEach(({ content }) => {
            if (content) content.classList.remove('show');
        });
    }

    // Add click handlers to all dropdown buttons
    Object.entries(dropdowns).forEach(([key, { btn, content }]) => {
        if (btn && content) {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                // Close other dropdowns
                closeAllDropdowns();
                // Toggle current dropdown
                content.classList.toggle('show');
            });

            // Add click handlers to dropdown options
            content.querySelectorAll('a').forEach(option => {
                option.addEventListener('click', (e) => {
                    e.preventDefault();
                    btn.textContent = option.textContent;
                    closeAllDropdowns();

                    // Special handling for ingredient type selection
                    if (key === 'ingredientType') {
                        updateIngredientsList(option.textContent);
                    }
                });
            });
        }
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
        if (!e.target.matches('.dropbtn')) {
            closeAllDropdowns();
        }
    });    // Function to update ingredients dropdown based on type
    function updateIngredientsList(selectedType) {
        const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]')
            .filter(ing => ing.type.toLowerCase() === selectedType.toLowerCase());

        const ingredientsList = document.getElementById('ingredientsList');
        ingredientsList.innerHTML = '';

        if (ingredients.length === 0) {
            const noIngredients = document.createElement('a');
            noIngredients.href = '#';
            noIngredients.textContent = 'No ingredients found';
            noIngredients.style.color = '#666';
            ingredientsList.appendChild(noIngredients);
            return;
        }

        ingredients.forEach(ing => {
            const option = document.createElement('a');
            option.href = '#';
            option.textContent = ing.name;
            option.addEventListener('click', (e) => {
                e.preventDefault();
                dropdowns.ingredients.btn.textContent = ing.name;
                closeAllDropdowns();
            });
            ingredientsList.appendChild(option);
        });
    }

    // Get feed code from either URL or sessionStorage
    const urlParams = new URLSearchParams(window.location.search);
    const feedCode = urlParams.get('id');
    const storedFeedCode = sessionStorage.getItem('editFeedCode');
    const targetFeedCode = feedCode || storedFeedCode;

    console.log('Looking for feed with code:', targetFeedCode);

    if (targetFeedCode) {
        const feeds = JSON.parse(localStorage.getItem('feeds') || '[]');
        console.log('Available feeds:', feeds);
        
        const feed = feeds.find(f => f.code === targetFeedCode);
        console.log('Found feed:', feed);

        if (feed) {
            // Populate form fields
            document.getElementById('feedName').value = feed.feedName || '';
            document.getElementById('growthRate').value = feed.growthRate || '';
            document.getElementById('weight').value = feed.weight || '';
            
            // Set dropdown values if they exist
            if (feed.gender) dropdowns.gender.btn.textContent = feed.gender;
            if (feed.animalType) dropdowns.animalType.btn.textContent = feed.animalType;
            if (feed.feedType) dropdowns.feedType.btn.textContent = feed.feedType;
            if (feed.season) dropdowns.season.btn.textContent = feed.season;

            // Load ingredients into the table
            const tbody = document.querySelector('table tbody');
            tbody.innerHTML = ''; // Clear existing rows
            
            if (feed.ingredients && feed.ingredients.length > 0) {
                feed.ingredients.forEach(ingredient => {
                    const newRow = createTableRow(
                        ingredient.name,
                        ingredient.pricePerKg,
                        ingredient.weight
                    );
                    tbody.appendChild(newRow);
                });
                
                // Update nutrient percentages after loading ingredients
                updateNutrientPercentages();
            }

            // Clean up session storage
            sessionStorage.removeItem('editFeedCode');
        } else {
            showNotification('Feed not found', 'error');
            setTimeout(() => {
                window.location.href = 'feed.html';
            }, 2000);
        }
    }

    // Save button handler updates
    document.querySelector('.frame-9').onclick = () => {
        // Get values
        const name = document.getElementById('feedName').value.trim();
        const growthRate = document.getElementById('growthRate').value;
        const weight = document.getElementById('weight').value;
        const gender = document.getElementById('genderBtn').textContent;
        const animalType = document.getElementById('animalTypeBtn').textContent;
        const feedType = document.getElementById('feedTypeBtn').textContent;
        const season = document.getElementById('seasonBtn').textContent;
        
        // Validate inputs
        if (!name || !growthRate || !weight || 
            gender === 'Select Gender' || 
            animalType === 'Select Animal Type' ||
            feedType === 'Select Feed Type' ||
            season === 'Select Season') {
            showNotification('Please fill in all required fields', 'error');
            return;
        }
        
        // Collect ingredients
        const ingredients = [];
        document.querySelectorAll('table tbody tr').forEach(row => {
            ingredients.push({
                name: row.querySelector('td:first-child .data-filled').textContent,
                pricePerKg: parseFloat(row.querySelector('td:nth-child(2) .data-filled').textContent),
                weight: parseFloat(row.querySelector('td:nth-child(3) .data-filled').textContent)
            });
        });
        
        if (ingredients.length < 3) {
            showNotification('Please add at least 3 ingredients', 'error');
            return;
        }

        // Update feed in storage
        const feeds = JSON.parse(localStorage.getItem('feeds') || '[]');
        const index = feeds.findIndex(f => f.code === targetFeedCode);
        
        if (index !== -1) {
            const existingFeed = feeds[index];
            feeds[index] = {
                ...existingFeed,
                feedName: name,
                growthRate: parseFloat(growthRate),
                weight: parseFloat(weight),
                gender,
                animalType,
                feedType,
                season,
                ingredients,
                lastUpdated: new Date().toISOString()
            };
            
            localStorage.setItem('feeds', JSON.stringify(feeds));
            showNotification('Feed updated successfully', 'success');
            
            setTimeout(() => {
                window.location.href = 'feed.html';
            }, 2000);
        } else {
            showNotification('Failed to update feed', 'error');
        }
    };    // Add/Update ingredient button handler
    const addIngredientBtn = document.getElementById('addIngredientBtn');
    if (addIngredientBtn) {
        addIngredientBtn.addEventListener('click', () => {
            const ingredientType = document.getElementById('ingredientTypeBtn').textContent;
            const selectedIngredient = document.getElementById('ingredientsDropdown').textContent;
            const priceInput = document.getElementById('pricePerKg');
            const weightInput = document.getElementById('ingredientWeight');
            const table = document.querySelector('table');
            
            // Validate inputs
            if (ingredientType === 'Select Ingredient Type' || 
                selectedIngredient === 'Select Ingredient' || 
                !priceInput.value || 
                !weightInput.value) {
                showNotification('Please fill in all ingredient fields', 'error');
                return;
            }

            // Validate numeric inputs
            if (!validateNumericInput(priceInput.value, 'price') || 
                !validateNumericInput(weightInput.value, 'weight')) {
                return;
            }

            const tbody = document.querySelector('table tbody');
            const editingIndex = table.dataset.editingRowIndex;

            if (editingIndex !== undefined) {
                // Update existing row
                const rows = tbody.querySelectorAll('tr');
                const rowToUpdate = rows[editingIndex];
                
                if (rowToUpdate) {
                    // Update row content
                    rowToUpdate.replaceWith(createTableRow(
                        selectedIngredient,
                        priceInput.value,
                        weightInput.value
                    ));
                    
                    showNotification('Ingredient updated successfully', 'success');
                    delete table.dataset.editingRowIndex;
                    rowToUpdate.classList.remove('selected-row');
                }
            } else {
                // Check for duplicate when adding new ingredient
                const existingRows = tbody.querySelectorAll('tr');
                for (const row of existingRows) {
                    const ingredientName = row.querySelector('td:first-child .data-filled').textContent;
                    if (ingredientName === selectedIngredient) {
                        showNotification('This ingredient is already added', 'error');
                        return;
                    }
                }

                // Add new row
                const newRow = createTableRow(
                    selectedIngredient,
                    priceInput.value,
                    weightInput.value
                );
                tbody.appendChild(newRow);
                showNotification('Ingredient added successfully', 'success');
            }

            // Clear inputs
            document.getElementById('ingredientTypeBtn').textContent = 'Select Ingredient Type';
            document.getElementById('ingredientsDropdown').textContent = 'Select Ingredient';
            priceInput.value = '';
            weightInput.value = '';

            // Update nutrient percentages
            updateNutrientPercentages();
        });
    }
    
    // Add click handler for table rows
    const tableBody = document.querySelector('table tbody');
    if (tableBody) {
        tableBody.addEventListener('click', handleRowClick);
    }

    // Function to validate form data
    function validateFormData() {
        // Get all required form elements
        const feedNameInput = document.getElementById('feedName');
        const growthRateInput = document.getElementById('growthRate');
        const weightInput = document.getElementById('weight');
        const genderBtn = document.getElementById('genderBtn');
        const animalTypeBtn = document.getElementById('animalTypeBtn');
        const feedTypeBtn = document.getElementById('feedTypeBtn');
        const seasonBtn = document.getElementById('seasonBtn');

        // Validate feed name
        if (!feedNameInput || !feedNameInput.value.trim()) {
            showNotification('Please enter a feed name', 'error');
            return false;
        }

        // Validate growth rate
        if (!growthRateInput || !growthRateInput.value.trim()) {
            showNotification('Please enter a growth rate', 'error');
            return false;
        }

        // Validate weight
        if (!weightInput || !weightInput.value.trim()) {
            showNotification('Please enter a weight', 'error');
            return false;
        }

        // Validate dropdowns
        if (genderBtn.textContent === 'Select Gender') {
            showNotification('Please select a gender', 'error');
            return false;
        }

        if (animalTypeBtn.textContent === 'Select Animal Type') {
            showNotification('Please select an animal type', 'error');
            return false;
        }

        if (feedTypeBtn.textContent === 'Select Feed Type') {
            showNotification('Please select a feed type', 'error');
            return false;
        }

        if (seasonBtn.textContent === 'Select Season') {
            showNotification('Please select a season', 'error');
            return false;
        }

        return true;
    }

    // Function to validate ingredients
    function validateIngredients() {
        const ingredientRows = document.querySelectorAll('table tbody tr');
        
        // Check minimum ingredients requirement (3)
        if (ingredientRows.length < 3) {
            showNotification('Please add at least 3 ingredients to the feed', 'error');
            return false;
        }

        // Check that each ingredient has a valid price and weight
        const invalidIngredients = Array.from(ingredientRows).filter(row => {
            const price = row.querySelector('td:nth-child(2) .data-filled').textContent;
            const weight = row.querySelector('td:nth-child(3) .data-filled').textContent;
            return !price || isNaN(parseFloat(price)) || !weight || isNaN(parseFloat(weight));
        });

        if (invalidIngredients.length > 0) {
            showNotification('All ingredients must have valid price and weight values', 'error');
            return false;
        }

        return true;
    }

    // Function to collect and save feed data temporarily
    function collectAndSaveFeedData() {
        const feedData = {
            feedName: document.getElementById('feedName').value.trim(),
            growthRate: parseFloat(document.getElementById('growthRate').value),
            weight: parseFloat(document.getElementById('weight').value),
            gender: document.getElementById('genderBtn').textContent,
            animalType: document.getElementById('animalTypeBtn').textContent,
            feedType: document.getElementById('feedTypeBtn').textContent,
            season: document.getElementById('seasonBtn').textContent,
            ingredients: []
        };

        // Collect ingredients data
        document.querySelectorAll('table tbody tr').forEach(row => {
            feedData.ingredients.push({
                name: row.querySelector('td:first-child .data-filled').textContent,
                pricePerKg: parseFloat(row.querySelector('td:nth-child(2) .data-filled').textContent),
                weight: parseFloat(row.querySelector('td:nth-child(3) .data-filled').textContent)
            });
        });

        // Store data temporarily
        sessionStorage.setItem('tempFeedData', JSON.stringify(feedData));
        return true;
    }    // Add click handler for Finish Choose Ingredients button
    const finishChooseIngredientsBtn = document.querySelector('.btntext1');
    if (finishChooseIngredientsBtn) {
        finishChooseIngredientsBtn.addEventListener('click', () => {
            // Validate form data
            if (!validateFormData()) {
                return;
            }

            // Validate ingredients
            if (!validateIngredients()) {
                return;
            }

            // Save data temporarily
            if (collectAndSaveFeedData()) {
                showNotification('Feed data saved successfully!', 'success');
            } else {
                showNotification('Failed to save feed data', 'error');
                return;
            }
        });
    }

    // Add event delegation for delete buttons
    document.querySelector('table tbody').addEventListener('click', (e) => {
        if (e.target.classList.contains('delete-ingredient')) {
            const row = e.target.closest('tr');
            if (row) {
                row.remove();
                updateNutrientPercentages();
            }
        }
    });

    // Function to populate input fields with row data
function populateInputFieldsFromRow(row) {
    // Get values from the row
    const ingredient = row.querySelector('td:first-child .data-filled').textContent;
    const price = row.querySelector('td:nth-child(2) .data-filled').textContent;
    const weight = row.querySelector('td:nth-child(3) .data-filled').textContent;
    
    // Get all ingredients to find the type
    const allIngredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
    const ingredientData = allIngredients.find(i => i.name === ingredient);
    
    // Set ingredient type if found
    if (ingredientData) {
        const ingredientTypeBtn = document.getElementById('ingredientTypeBtn');
        ingredientTypeBtn.textContent = ingredientData.type;
        // Update ingredients dropdown for this type
        updateIngredientsList(ingredientData.type);
    }
    
    // Set the ingredient in dropdown
    const ingredientsDropdown = document.getElementById('ingredientsDropdown');
    if (ingredientsDropdown) {
        ingredientsDropdown.textContent = ingredient;
    }
    
    // Set price and weight inputs
    const priceInput = document.getElementById('pricePerKg');
    const weightInput = document.getElementById('ingredientWeight');
    if (priceInput) priceInput.value = price;
    if (weightInput) weightInput.value = weight;
    
    // Store the row being edited
    document.querySelector('table').dataset.editingRowIndex = Array.from(row.parentNode.children).indexOf(row);
}

// Function to handle row click
function handleRowClick(e) {
    const row = e.target.closest('tr');
    if (!row) return;
    
    // Don't trigger if clicking the delete button
    if (e.target.classList.contains('delete-ingredient')) return;
    
    // Remove highlight from other rows
    document.querySelectorAll('table tbody tr').forEach(r => r.classList.remove('selected-row'));
    
    // Highlight selected row
    row.classList.add('selected-row');
    
    // Populate input fields
    populateInputFieldsFromRow(row);
}

// Style for selected row
const style = document.createElement('style');
style.textContent = `
    .selected-row {
        background-color: #f0f0f0;
    }
    table tbody tr {
        cursor: pointer;
    }
    table tbody tr:hover {
        background-color: #f5f5f5;
    }
`;
document.head.appendChild(style);

// Add event listener for row clicks
document.querySelector('table tbody').addEventListener('click', handleRowClick);
});