/**
 * Navigation Components JavaScript
 * Handles the functionality of the sidebar and navbar
 */

document.addEventListener('DOMContentLoaded', function() {
  // Handle profile dropdown toggle
  setupNavProfilePopup();
  
  function setupNavProfilePopup() {
  const navProfileContainer = document.getElementById("navProfileContainer");
  if (navProfileContainer) {
    navProfileContainer.addEventListener("click", function () {
      const popup = document.getElementById("navProfilePopup");
      if (!popup) return;
      const popupStyle = popup.style;
      if (popupStyle) {
        popupStyle.display = "flex";
        popupStyle.zIndex = 100;
        popupStyle.backgroundColor = "rgba(113, 113, 113, 0.3)";
        popupStyle.alignItems = "center";
        popupStyle.justifyContent = "center";
      }
      popup.setAttribute("closable", "");

      const onClick =
        popup.onClick ||
        function (e) {
          if (e.target === popup && popup.hasAttribute("closable")) {
            popupStyle.display = "none";
          }
        };
      popup.addEventListener("click", onClick);
    });
  }
}

  
  // Highlight current page in sidebar
  const currentPage = window.location.pathname.split('/').pop();
  const sidebarLinks = document.querySelectorAll('.side-bar a');
  
  sidebarLinks.forEach(link => {
    const linkHref = link.getAttribute('href');
    if (linkHref === currentPage) {
      link.parentElement.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
      link.style.fontWeight = 'bold';
    }
  });
  
  // Add hover effects to sidebar links
  sidebarLinks.forEach(link => {
    link.addEventListener('mouseover', function() {
      if (!this.parentElement.style.backgroundColor) {
        this.parentElement.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
      }
    });
    
    link.addEventListener('mouseout', function() {
      if (this.parentElement.style.backgroundColor === 'rgba(255, 255, 255, 0.05)') {
        this.parentElement.style.backgroundColor = '';
      }
    });
  });
  
  // Make sidebar responsive
  function adjustLayout() {
    const windowWidth = window.innerWidth;
    const sideBar = document.querySelector('.side-bar');
    const mainContent = document.querySelector('main') || document.querySelector('.content-container');
    
    if (windowWidth <= 700) {
      if (sideBar) {
        sideBar.style.position = 'relative';
        sideBar.style.width = '100%';
        sideBar.style.height = 'auto';
        sideBar.style.margin = '0 0 20px 0';
      }
      
      if (mainContent) {
        mainContent.style.marginLeft = '0';
        mainContent.style.width = '100%';
      }
    } else {
      if (sideBar) {
        sideBar.style.position = 'fixed';
        sideBar.style.width = '17%';
        sideBar.style.height = '98%';
        sideBar.style.margin = '10px';
      }
      
      if (mainContent) {
        mainContent.style.marginLeft = 'calc(17% + 20px)';
        mainContent.style.width = 'calc(83% - 30px)';
      }
    }
  }
  
  // Initial layout adjustment
  adjustLayout();
  
  // Adjust layout on window resize
  window.addEventListener('resize', adjustLayout);
});



