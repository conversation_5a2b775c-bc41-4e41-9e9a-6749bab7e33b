/**
 * Record Milk Production System
 * Comprehensive OOP-based system for recording daily milk production
 * Integrates with existing dairy animal management system
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */

// ==================== DATA MANAGER ====================

/**
 * Handles all data operations for milk production recording
 */
class RecordMilkDataManager {
    constructor() {
        this.dairyAnimals = [];
        this.loadData();
    }

    /**
     * Load dairy animals data from localStorage
     */
    loadData() {
        try {
            this.dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
            console.log(`📊 Loaded ${this.dairyAnimals.length} dairy animals`);
        } catch (error) {
            console.error('❌ Error loading dairy animals:', error);
            this.dairyAnimals = [];
        }
    }

    /**
     * Save dairy animals data to localStorage
     */
    saveData() {
        try {
            localStorage.setItem('dairyAnimals', JSON.stringify(this.dairyAnimals));
            console.log('✅ Dairy animals data saved successfully');
            return true;
        } catch (error) {
            console.error('❌ Error saving dairy animals:', error);
            return false;
        }
    }

    /**
     * Find dairy animal by code
     * @param {string} code - Animal code
     * @returns {Object|null} - Found dairy animal or null
     */
    findDairyAnimalByCode(code) {
        if (!code || !code.trim()) {
            return null;
        }

        const normalizedCode = code.trim().toLowerCase();
        return this.dairyAnimals.find(animal =>
            animal.code && animal.code.toLowerCase() === normalizedCode
        );
    }

    /**
     * Record milk production for a dairy animal
     * @param {string} animalCode - Animal code
     * @param {Object} milkData - Milk production data
     * @returns {Object} - Updated dairy animal
     */
    recordMilkProduction(animalCode, milkData) {
        const animal = this.findDairyAnimalByCode(animalCode);

        if (!animal) {
            throw new Error(`Dairy animal with code "${animalCode}" not found`);
        }

        const today = new Date().toISOString().split('T')[0];
        const timestamp = new Date().toISOString();

        // Initialize milk production records if not exists
        if (!animal.milkProductionRecords) {
            animal.milkProductionRecords = [];
        }

        // Check if there's already a record for today
        const existingRecordIndex = animal.milkProductionRecords.findIndex(
            record => record.date === today
        );

        const newRecord = {
            date: today,
            milkProduction: parseFloat(milkData.milkProduction) || 0,
            fatPercentage: parseFloat(milkData.fatPercentage) || 0,
            recordedAt: timestamp,
            recordedBy: 'recordmilk_system'
        };

        if (existingRecordIndex >= 0) {
            // Update existing record for today
            animal.milkProductionRecords[existingRecordIndex] = newRecord;
            console.log(`📝 Updated existing milk record for ${animalCode} on ${today}`);
        } else {
            // Add new record
            animal.milkProductionRecords.push(newRecord);
            console.log(`📝 Added new milk record for ${animalCode} on ${today}`);
        }

        // Update calculated fields
        this.updateCalculatedFields(animal);

        // Update last milking date
        animal.lastMilkingDate = today;
        animal.updatedAt = timestamp;

        // Save data
        if (!this.saveData()) {
            throw new Error('Failed to save milk production data');
        }

        console.log(`✅ Milk production recorded for ${animalCode}:`, newRecord);
        return animal;
    }

    /**
     * Update calculated fields for dairy animal
     * @param {Object} animal - Dairy animal object
     */
    updateCalculatedFields(animal) {
        if (!animal.milkProductionRecords || animal.milkProductionRecords.length === 0) {
            animal.totalMilkProduced = 0;
            animal.averageDailyMilk = 0;
            animal.lastMilkProduction = 0;
            return;
        }

        // Calculate total milk produced
        animal.totalMilkProduced = animal.milkProductionRecords.reduce(
            (total, record) => total + (record.milkProduction || 0), 0
        );

        // Calculate average daily milk (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentRecords = animal.milkProductionRecords.filter(record =>
            new Date(record.date) >= thirtyDaysAgo
        );

        if (recentRecords.length > 0) {
            const totalRecentMilk = recentRecords.reduce(
                (total, record) => total + (record.milkProduction || 0), 0
            );
            animal.averageDailyMilk = Math.round((totalRecentMilk / recentRecords.length) * 100) / 100;
        } else {
            animal.averageDailyMilk = 0;
        }

        // Set last milk production (today's production)
        const today = new Date().toISOString().split('T')[0];
        const todayRecord = animal.milkProductionRecords.find(record => record.date === today);
        animal.lastMilkProduction = todayRecord ? todayRecord.milkProduction : 0;

        // Update fat percentage if provided
        if (todayRecord && todayRecord.fatPercentage) {
            animal.fatPercentage = todayRecord.fatPercentage;
        }

        console.log(`📊 Updated calculated fields for ${animal.code}:`, {
            totalMilkProduced: animal.totalMilkProduced,
            averageDailyMilk: animal.averageDailyMilk,
            lastMilkProduction: animal.lastMilkProduction
        });
    }

    /**
     * Get milk production statistics for an animal
     * @param {string} animalCode - Animal code
     * @returns {Object} - Statistics object
     */
    getMilkProductionStats(animalCode) {
        const animal = this.findDairyAnimalByCode(animalCode);

        if (!animal || !animal.milkProductionRecords) {
            return null;
        }

        const records = animal.milkProductionRecords;
        const totalRecords = records.length;

        if (totalRecords === 0) {
            return {
                totalRecords: 0,
                totalMilkProduced: 0,
                averageDailyMilk: 0,
                highestProduction: 0,
                lowestProduction: 0,
                lastRecordDate: null
            };
        }

        const milkAmounts = records.map(r => r.milkProduction || 0);

        return {
            totalRecords,
            totalMilkProduced: animal.totalMilkProduced || 0,
            averageDailyMilk: animal.averageDailyMilk || 0,
            highestProduction: Math.max(...milkAmounts),
            lowestProduction: Math.min(...milkAmounts),
            lastRecordDate: animal.lastMilkingDate
        };
    }

    /**
     * Trigger storage events for other pages to update
     */
    triggerStorageEvents() {
        window.dispatchEvent(new StorageEvent('storage', {
            key: 'dairyAnimals',
            newValue: JSON.stringify(this.dairyAnimals)
        }));
        console.log('📡 Storage events triggered for dairy animals update');
    }
}

// ==================== VALIDATION MANAGER ====================

/**
 * Handles form validation for milk production recording
 */
class RecordMilkValidationManager {
    /**
     * Validate animal code
     * @param {string} code - Animal code
     * @returns {Array} - Array of error messages
     */
    validateAnimalCode(code) {
        const errors = [];

        if (!code || !code.trim()) {
            errors.push('Animal code is required');
        } else if (code.trim().length < 1) {
            errors.push('Animal code must be at least 1 character');
        }

        return errors;
    }

    /**
     * Validate milk production amount
     * @param {string} milkProduction - Milk production amount
     * @returns {Array} - Array of error messages
     */
    validateMilkProduction(milkProduction) {
        const errors = [];

        if (!milkProduction || !milkProduction.trim()) {
            errors.push('Milk production amount is required');
        } else {
            const amount = parseFloat(milkProduction);
            if (isNaN(amount)) {
                errors.push('Milk production must be a valid number');
            } else if (amount < 0) {
                errors.push('Milk production cannot be negative');
            } else if (amount > 100) {
                errors.push('Milk production seems unusually high (>100L). Please verify.');
            }
        }

        return errors;
    }

    /**
     * Validate fat percentage
     * @param {string} fatPercentage - Fat percentage
     * @returns {Array} - Array of error messages
     */
    validateFatPercentage(fatPercentage) {
        const errors = [];

        if (fatPercentage && fatPercentage.trim()) {
            const percentage = parseFloat(fatPercentage);
            if (isNaN(percentage)) {
                errors.push('Fat percentage must be a valid number');
            } else if (percentage < 0) {
                errors.push('Fat percentage cannot be negative');
            } else if (percentage > 20) {
                errors.push('Fat percentage seems unusually high (>20%). Please verify.');
            }
        }

        return errors;
    }

    /**
     * Validate all form data
     * @param {Object} formData - Form data object
     * @returns {Array} - Array of all error messages
     */
    validateFormData(formData) {
        const allErrors = [];

        allErrors.push(...this.validateAnimalCode(formData.animalCode));
        allErrors.push(...this.validateMilkProduction(formData.milkProduction));
        allErrors.push(...this.validateFatPercentage(formData.fatPercentage));

        return allErrors;
    }
}

// ==================== UI MANAGER ====================

/**
 * Handles UI interactions and notifications
 */
class RecordMilkUIManager {
    constructor() {
        this.notificationTimeout = null;
    }

    /**
     * Show notification message
     * @param {string} message - Message to display
     * @param {string} type - Type of notification (success, error, warning, info)
     * @param {number} duration - Duration in milliseconds
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Clear existing notification
        this.clearNotification();

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 25px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 400px;
            text-align: center;
            animation: slideDown 0.3s ease-out;
        `;

        // Set colors based on type
        const colors = {
            success: { bg: '#d4edda', color: '#155724', border: '#c3e6cb' },
            error: { bg: '#f8d7da', color: '#721c24', border: '#f5c6cb' },
            warning: { bg: '#fff3cd', color: '#856404', border: '#ffeaa7' },
            info: { bg: '#d1ecf1', color: '#0c5460', border: '#bee5eb' }
        };

        const colorScheme = colors[type] || colors.info;
        notification.style.backgroundColor = colorScheme.bg;
        notification.style.color = colorScheme.color;
        notification.style.border = `1px solid ${colorScheme.border}`;

        notification.textContent = message;

        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                to { transform: translateX(-50%) translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(notification);

        // Auto-remove notification
        this.notificationTimeout = setTimeout(() => {
            this.clearNotification();
        }, duration);

        console.log(`📢 Notification (${type}): ${message}`);
    }

    /**
     * Clear current notification
     */
    clearNotification() {
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        if (this.notificationTimeout) {
            clearTimeout(this.notificationTimeout);
            this.notificationTimeout = null;
        }
    }

    /**
     * Show field error
     * @param {HTMLElement} fieldElement - Field element
     * @param {string} errorMessage - Error message
     */
    showFieldError(fieldElement, errorMessage) {
        this.clearFieldError(fieldElement);

        fieldElement.style.borderColor = '#dc3545';
        fieldElement.style.backgroundColor = '#fff5f5';

        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error-message';
        errorElement.style.cssText = `
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            font-family: Arial, sans-serif;
            position:relative;
            top:25%;
        `;
        errorElement.textContent = errorMessage;

        fieldElement.parentNode.appendChild(errorElement);
    }

    /**
     * Clear field error
     * @param {HTMLElement} fieldElement - Field element
     */
    clearFieldError(fieldElement) {
        fieldElement.style.borderColor = '';
        fieldElement.style.backgroundColor = '';

        const errorElement = fieldElement.parentNode.querySelector('.field-error-message');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * Clear all field errors
     */
    clearAllFieldErrors() {
        const errorElements = document.querySelectorAll('.field-error-message');
        errorElements.forEach(element => element.remove());

        const inputElements = document.querySelectorAll('input');
        inputElements.forEach(input => {
            input.style.borderColor = '';
            input.style.backgroundColor = '';
        });
    }

    /**
     * Highlight field success
     * @param {HTMLElement} fieldElement - Field element
     */
    highlightFieldSuccess(fieldElement) {
        fieldElement.style.borderColor = '#28a745';
        fieldElement.style.backgroundColor = '#f8fff9';

        setTimeout(() => {
            fieldElement.style.borderColor = '';
            fieldElement.style.backgroundColor = '';
        }, 2000);
    }

    /**
     * Show loading state on button (preserves original HTML structure)
     * @param {HTMLElement} buttonElement - Button element
     */
    showLoading(buttonElement) {
        if (buttonElement) {
            buttonElement.disabled = true;
            buttonElement.style.opacity = '0.7';

            // Find the .btntext element inside the button
            const btnTextElement = buttonElement.querySelector('.btntext');
            if (btnTextElement) {
                const originalText = btnTextElement.textContent;
                btnTextElement.textContent = 'Saving...';
                btnTextElement.dataset.originalText = originalText;
            }
        }
    }

    /**
     * Hide loading state on button (restores original HTML structure)
     * @param {HTMLElement} buttonElement - Button element
     */
    hideLoading(buttonElement) {
        if (buttonElement) {
            buttonElement.disabled = false;
            buttonElement.style.opacity = '';

            // Find the .btntext element inside the button
            const btnTextElement = buttonElement.querySelector('.btntext');
            if (btnTextElement) {
                const originalText = btnTextElement.dataset.originalText;
                if (originalText) {
                    btnTextElement.textContent = originalText;
                    delete btnTextElement.dataset.originalText;
                }
            }
        }
    }
}

// ==================== MAIN CONTROLLER ====================

/**
 * Main controller for Record Milk Production system
 */
class RecordMilkController {
    constructor() {
        this.dataManager = new RecordMilkDataManager();
        this.validationManager = new RecordMilkValidationManager();
        this.uiManager = new RecordMilkUIManager();

        this.elements = {};
        this.formData = {};

        console.log('🎯 RecordMilkController initialized');
    }

    /**
     * Initialize the system
     */
    init() {
        try {
            this.initializeElements();
            this.setupEventListeners();
            this.setupRealTimeValidation();

            console.log('✅ Record Milk system initialized successfully');
            this.uiManager.showNotification('Record Milk system ready!', 'info', 3000);

        } catch (error) {
            console.error('❌ Failed to initialize Record Milk system:', error);
            this.uiManager.showNotification('Failed to initialize system. Please refresh the page.', 'error');
        }
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        // Get form inputs based on HTML structure
        const inputs = document.querySelectorAll('.data-filled');

        if (inputs.length >= 3) {
            this.elements.codeInput = inputs[0];
            this.elements.milkProductionInput = inputs[1];
            this.elements.fatPercentageInput = inputs[2];
        } else {
            throw new Error('Required form inputs not found');
        }

        // Get save button
        this.elements.saveButton = document.querySelector('.frame-9');
        if (!this.elements.saveButton) {
            throw new Error('Save button not found');
        }

        console.log('📋 DOM elements initialized:', Object.keys(this.elements));
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Save button click
        this.elements.saveButton.addEventListener('click', () => {
            this.handleSave();
        });

        // Enter key on inputs
        Object.values(this.elements).forEach(element => {
            if (element && element.tagName === 'INPUT') {
                element.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleSave();
                    }
                });
            }
        });

        console.log('🔗 Event listeners setup complete');
    }

    /**
     * Setup real-time validation
     */
    setupRealTimeValidation() {
        // Animal code input - real-time animal lookup
        if (this.elements.codeInput) {
            this.elements.codeInput.addEventListener('input', (e) => {
                this.handleCodeInput(e);
            });

            this.elements.codeInput.addEventListener('blur', (e) => {
                this.validateAnimalCode(e.target.value);
            });
        }

        // Milk production input - real-time validation
        if (this.elements.milkProductionInput) {
            this.elements.milkProductionInput.addEventListener('input', (e) => {
                this.handleMilkProductionInput(e);
            });
        }

        // Fat percentage input - real-time validation
        if (this.elements.fatPercentageInput) {
            this.elements.fatPercentageInput.addEventListener('input', (e) => {
                this.handleFatPercentageInput(e);
            });
        }

        console.log('✅ Real-time validation setup complete');
    }

    /**
     * Handle animal code input
     * @param {Event} event - Input event
     */
    handleCodeInput(event) {
        const code = event.target.value.trim();
        this.formData.animalCode = code;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Real-time animal lookup (debounced)
        clearTimeout(this.codeInputTimeout);
        this.codeInputTimeout = setTimeout(() => {
            if (code) {
                this.validateAnimalCode(code);
            }
        }, 500);
    }

    /**
     * Validate animal code and show feedback
     * @param {string} code - Animal code
     */
    validateAnimalCode(code) {
        if (!code || !code.trim()) {
            return;
        }

        const animal = this.dataManager.findDairyAnimalByCode(code);

        if (animal) {
            this.uiManager.highlightFieldSuccess(this.elements.codeInput);
            this.uiManager.showNotification(`Dairy animal "${animal.code}" found!`, 'success', 2000);

            // Show animal info
            console.log('🐄 Found dairy animal:', {
                code: animal.code,
                herdNumber: animal.herdNumber,
                lastMilkingDate: animal.lastMilkingDate,
                averageDailyMilk: animal.averageDailyMilk
            });
        } else {
            this.uiManager.showFieldError(this.elements.codeInput, 'Dairy animal not found');
        }
    }

    /**
     * Handle milk production input
     * @param {Event} event - Input event
     */
    handleMilkProductionInput(event) {
        const value = event.target.value.trim();
        this.formData.milkProduction = value;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Real-time validation
        if (value) {
            const errors = this.validationManager.validateMilkProduction(value);
            if (errors.length > 0) {
                this.uiManager.showFieldError(event.target, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(event.target);
            }
        }
    }

    /**
     * Handle fat percentage input
     * @param {Event} event - Input event
     */
    handleFatPercentageInput(event) {
        const value = event.target.value.trim();
        this.formData.fatPercentage = value;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Real-time validation
        if (value) {
            const errors = this.validationManager.validateFatPercentage(value);
            if (errors.length > 0) {
                this.uiManager.showFieldError(event.target, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(event.target);
            }
        }
    }

    /**
     * Collect form data
     * @returns {Object} - Form data object
     */
    collectFormData() {
        return {
            animalCode: this.elements.codeInput.value.trim(),
            milkProduction: this.elements.milkProductionInput.value.trim(),
            fatPercentage: this.elements.fatPercentageInput.value.trim()
        };
    }

    /**
     * Validate entire form
     * @returns {boolean} - Validation result
     */
    validateForm() {
        const formData = this.collectFormData();
        const errors = this.validationManager.validateFormData(formData);

        // Clear all previous errors
        this.uiManager.clearAllFieldErrors();

        if (errors.length > 0) {
            // Show first error as notification
            this.uiManager.showNotification(errors[0], 'error');

            // Show field-specific errors
            this.showFieldErrors(errors, formData);
            return false;
        }

        // Additional check: verify animal exists
        const animal = this.dataManager.findDairyAnimalByCode(formData.animalCode);
        if (!animal) {
            this.uiManager.showFieldError(this.elements.codeInput, 'Dairy animal not found');
            this.uiManager.showNotification('Please enter a valid dairy animal code', 'error');
            return false;
        }

        return true;
    }

    /**
     * Show field-specific errors
     * @param {Array} errors - Array of error messages
     */
    showFieldErrors(errors) {
        errors.forEach(error => {
            if (error.toLowerCase().includes('code')) {
                this.uiManager.showFieldError(this.elements.codeInput, error);
            } else if (error.toLowerCase().includes('milk production')) {
                this.uiManager.showFieldError(this.elements.milkProductionInput, error);
            } else if (error.toLowerCase().includes('fat percentage')) {
                this.uiManager.showFieldError(this.elements.fatPercentageInput, error);
            }
        });
    }

    /**
     * Handle save button click
     */
    async handleSave() {
        console.log('💾 Save button clicked');

        if (!this.validateForm()) {
            console.log('❌ Form validation failed');
            return;
        }

        const formData = this.collectFormData();
        console.log('📝 Form data collected:', formData);

        // Show loading state
        this.uiManager.showLoading(this.elements.saveButton);

        try {
            // Record milk production
            const updatedAnimal = this.dataManager.recordMilkProduction(formData.animalCode, {
                milkProduction: formData.milkProduction,
                fatPercentage: formData.fatPercentage
            });

            console.log('✅ Milk production recorded successfully:', updatedAnimal);

            // Show success message with details
            const stats = this.dataManager.getMilkProductionStats(formData.animalCode);
            this.uiManager.showNotification(
                `Milk production recorded for ${formData.animalCode}! Today: ${formData.milkProduction}L, Average: ${stats.averageDailyMilk}L`,
                'success',
                5000
            );

            // Highlight updated fields
            this.highlightUpdatedFields();

            // Trigger storage events for other pages to update
            this.dataManager.triggerStorageEvents();

            // Clear form after delay
            setTimeout(() => {
                this.clearForm();
            }, 2000);

        } catch (error) {
            console.error('❌ Error recording milk production:', error);
            this.uiManager.showNotification(
                `Failed to record milk production: ${error.message}`,
                'error'
            );
        } finally {
            // Hide loading state
            
            this.uiManager.hideLoading(this.elements.saveButton);
        }
         // Navigate back to vaccination page
         // window.location.href = './vaccination.html';
         window.parent.location.reload();
    }

    /**
     * Highlight updated fields with success styling
     */
    highlightUpdatedFields() {
        const fields = [
            this.elements.codeInput,
            this.elements.milkProductionInput,
            this.elements.fatPercentageInput
        ];

        fields.forEach(field => {
            if (field) {
                this.uiManager.highlightFieldSuccess(field);
            }
        });
    }

    /**
     * Clear form to initial state
     */
    clearForm() {
        // Clear form data
        this.formData = {};

        // Clear all input fields
        Object.values(this.elements).forEach(element => {
            if (element && element.tagName === 'INPUT') {
                element.value = '';
            }
        });

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        console.log('🔄 Form cleared');
    }

    /**
     * Get milk production statistics for display
     * @param {string} animalCode - Animal code
     * @returns {Object|null} - Statistics or null
     */
    getAnimalStats(animalCode) {
        return this.dataManager.getMilkProductionStats(animalCode);
    }
}

// ==================== INITIALIZATION ====================

/**
 * Initialize the Record Milk system when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing Record Milk system...');

    try {
        // Create and initialize the controller
        const recordMilkController = new RecordMilkController();
        recordMilkController.init();

        // Make it globally accessible for debugging and external access
        window.recordMilkController = recordMilkController;

        console.log('🎉 Record Milk system ready!');
        console.log('Available global methods:');
        console.log('- window.recordMilkController.getAnimalStats(code)');
        console.log('- window.recordMilkController.clearForm()');

    } catch (error) {
        console.error('💥 Failed to initialize Record Milk system:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        errorDiv.textContent = 'Failed to initialize the record milk form. Please refresh the page.';
        document.body.appendChild(errorDiv);
    }
});

// ==================== UTILITY FUNCTIONS ====================

/**
 * Record milk production for an animal (utility function)
 * @param {string} animalCode - Animal code
 * @param {number} milkProduction - Milk production amount
 * @param {number} fatPercentage - Fat percentage (optional)
 * @returns {Object|null} - Updated animal or null
 */
window.recordMilkProduction = function(animalCode, milkProduction, fatPercentage = null) {
    if (!window.recordMilkController) {
        console.error('Record Milk system not initialized');
        return null;
    }

    try {
        return window.recordMilkController.dataManager.recordMilkProduction(animalCode, {
            milkProduction,
            fatPercentage
        });
    } catch (error) {
        console.error('Error recording milk production:', error);
        return null;
    }
};

/**
 * Get milk production statistics for an animal (utility function)
 * @param {string} animalCode - Animal code
 * @returns {Object|null} - Statistics or null
 */
window.getMilkProductionStats = function(animalCode) {
    if (!window.recordMilkController) {
        console.error('Record Milk system not initialized');
        return null;
    }

    return window.recordMilkController.dataManager.getMilkProductionStats(animalCode);
};

/**
 * Find dairy animal by code (utility function)
 * @param {string} code - Animal code
 * @returns {Object|null} - Found animal or null
 */
window.findDairyAnimal = function(code) {
    if (!window.recordMilkController) {
        console.error('Record Milk system not initialized');
        return null;
    }

    return window.recordMilkController.dataManager.findDairyAnimalByCode(code);
};

// ==================== DEBUG FUNCTIONS ====================

/**
 * Debug function to check system status
 */
window.debugRecordMilk = function() {
    console.log('=== Record Milk Debug Info ===');

    if (!window.recordMilkController) {
        console.error('❌ Record Milk system not initialized');
        return;
    }

    console.log('✅ System initialized');
    console.log('📊 Dairy animals loaded:', window.recordMilkController.dataManager.dairyAnimals.length);
    console.log('📝 Form data:', window.recordMilkController.formData);
    console.log('🔍 Elements:', Object.keys(window.recordMilkController.elements));

    // Check form elements
    const elements = window.recordMilkController.elements;
    console.log('Form elements status:');
    Object.entries(elements).forEach(([key, element]) => {
        console.log(`  ${key}: ${element ? '✅ Found' : '❌ Missing'}`);
    });
};

/**
 * Test milk production recording
 * @param {string} animalCode - Animal code to test
 * @param {number} milkAmount - Milk amount to test
 */
window.testMilkRecording = function(animalCode = 'TEST001', milkAmount = 25.5) {
    console.log('🧪 Testing milk production recording...');

    if (!window.recordMilkController) {
        console.error('❌ Record Milk system not initialized');
        return;
    }

    try {
        // Check if animal exists
        const animal = window.recordMilkController.dataManager.findDairyAnimalByCode(animalCode);
        if (!animal) {
            console.log(`❌ Dairy animal "${animalCode}" not found`);
            console.log('Available dairy animals:');
            window.recordMilkController.dataManager.dairyAnimals.forEach(a => {
                console.log(`  - ${a.code} (${a.herdNumber || 'No herd number'})`);
            });
            return;
        }

        console.log(`✅ Found dairy animal: ${animal.code}`);

        // Record milk production
        const result = window.recordMilkController.dataManager.recordMilkProduction(animalCode, {
            milkProduction: milkAmount,
            fatPercentage: 3.5
        });

        console.log('✅ Milk production recorded successfully:', result);

        // Show updated statistics
        const stats = window.recordMilkController.dataManager.getMilkProductionStats(animalCode);
        console.log('📊 Updated statistics:', stats);

        return result;

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
};

/**
 * Show all dairy animals with their milk production data
 */
window.showDairyAnimalsWithMilk = function() {
    console.log('🥛 DAIRY ANIMALS WITH MILK PRODUCTION DATA');
    console.log('==========================================');

    if (!window.recordMilkController) {
        console.error('❌ Record Milk system not initialized');
        return;
    }

    const dairyAnimals = window.recordMilkController.dataManager.dairyAnimals;

    if (dairyAnimals.length === 0) {
        console.log('❌ No dairy animals found');
        return;
    }

    dairyAnimals.forEach((animal, index) => {
        console.log(`${index + 1}. ${animal.code} (${animal.herdNumber || 'No herd number'})`);
        console.log(`   Total milk produced: ${animal.totalMilkProduced || 0}L`);
        console.log(`   Average daily milk: ${animal.averageDailyMilk || 0}L`);
        console.log(`   Last milking date: ${animal.lastMilkingDate || 'Never'}`);
        console.log(`   Records count: ${animal.milkProductionRecords ? animal.milkProductionRecords.length : 0}`);
        console.log('   ---');
    });

    return dairyAnimals;
};

console.log(`
🥛 RECORD MILK PRODUCTION SYSTEM LOADED
======================================

🔍 DEBUG FUNCTIONS AVAILABLE:
   • debugRecordMilk() - Check system status
   • testMilkRecording(code, amount) - Test recording milk production
   • showDairyAnimalsWithMilk() - Show all dairy animals with milk data
   • recordMilkProduction(code, amount, fatPercentage) - Record milk production
   • getMilkProductionStats(code) - Get animal milk statistics
   • findDairyAnimal(code) - Find dairy animal by code

✅ Ready for milk production recording!
`);