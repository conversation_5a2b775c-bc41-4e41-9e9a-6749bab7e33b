<!DOCTYPE html>
<html>
    <head>
         <meta charset="utf-8" />
         <link rel="stylesheet" href="css/deletepopstyle.css" />
         <title>Delete Confirmation</title>
    </head>
    <body>
        <div class="container">
            <div class="overlay">
                <div class="delete-popup">
                    <img src="icons/delete.png" alt="Delete">
                    <div class="statement">Delete ?</div>
                   
                    <div class="click">
                        <button class="cancel-btn">
                            <div class="txt">Cancel</div>
                        </button>
                        <button class="delete-btn">
                            <div class="txt">Delete</div>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Include the delete popup confirmation system -->
        <script src="js/deletePopupConfirmation.js"></script>

        <script>
            // Initialize the delete popup when this page loads
            document.addEventListener('DOMContentLoaded', () => {
                console.log('🗑️ Delete popup page loaded');

                // Get buttons
                const cancelBtn = document.querySelector('.cancel-btn');
                const deleteBtn = document.querySelector('.delete-btn');

                // Add event listeners for the static popup
                if (cancelBtn) {
                    cancelBtn.addEventListener('click', () => {
                        console.log('Cancel clicked');
                        // Close popup or go back
                        if (window.history.length > 1) {
                            window.history.back();
                        } else {
                            window.close();
                        }
                    });
                }

                if (deleteBtn) {
                    deleteBtn.addEventListener('click', () => {
                        console.log('Delete clicked');
                        // This would typically be handled by the calling page
                        // For demo purposes, show an alert
                        alert('Delete confirmed! (This is a demo)');

                        // Close popup or go back
                        if (window.history.length > 1) {
                            window.history.back();
                        } else {
                            window.close();
                        }
                    });
                }

                // Handle escape key
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape') {
                        if (window.history.length > 1) {
                            window.history.back();
                        } else {
                            window.close();
                        }
                    }
                });
            });
        </script>
    </body>
</html>