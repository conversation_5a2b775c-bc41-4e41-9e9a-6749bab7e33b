document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const emailInput = document.querySelector('input[type="email"]');
    const resetPasswordBtn = document.querySelector('.component');
    
    // Handle reset password button click
    resetPasswordBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Get email value
        const email = emailInput.value.trim();
        
        // Validate email
        if (!email) {
            alert('Please enter your email address');
            emailInput.focus();
            return;
        }
        
        if (!validateEmail(email)) {
            alert('Please enter a valid email address');
            emailInput.focus();
            return;
        }
        
        // Check if user exists with this email
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const userExists = users.some(user => user.email === email);
        
        if (!userExists) {
            alert('No account found with this email address. Please check and try again.');
            emailInput.focus();
            return;
        }
        
        // Store email in sessionStorage for the next step
        sessionStorage.setItem('resetPasswordEmail', email);
        
        // In a real application, you would send a verification email here
        // For this demo, we'll simulate sending an email and proceed directly
        
        // Show success message
        alert('Email verification successful! Please create a new password.');
        
        // Redirect to password reset page
        window.location.href = 'forgetpass.html';
    });
    
    // Add event listener for Enter key on email input
    emailInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            resetPasswordBtn.click();
        }
    });
    
    // Function to validate email format
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
   
    
    // Optional: Add loading animation during "email sending"
    function showLoading() {
        // Disable button and show loading state
        resetPasswordBtn.disabled = true;
        
        const originalText = resetPasswordBtn.querySelector('.cancel').textContent;
        resetPasswordBtn.querySelector('.cancel').textContent = 'Sending...';
        
        // Create and add spinner
        const spinner = document.createElement('div');
        spinner.className = 'spinner';
        spinner.style.display = 'inline-block';
        spinner.style.width = '16px';
        spinner.style.height = '16px';
        spinner.style.border = '2px solid rgba(255,255,255,0.3)';
        spinner.style.borderRadius = '50%';
        spinner.style.borderTopColor = 'white';
        spinner.style.animation = 'spin 1s linear infinite';
        spinner.style.marginLeft = '10px';
        
        // Add keyframes for spinner animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                to { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
        
        resetPasswordBtn.querySelector('.cancel').appendChild(spinner);
        
        // Return function to hide loading state
        return function hideLoading() {
            resetPasswordBtn.disabled = false;
            resetPasswordBtn.querySelector('.cancel').textContent = originalText;
        };
    }
    
    // Modify the reset button click handler to include loading animation
    resetPasswordBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Get email value
        const email = emailInput.value.trim();
        
        // Validate email
        if (!email) {
            alert('Please enter your email address');
            emailInput.focus();
            return;
        }
        
        if (!validateEmail(email)) {
            alert('Please enter a valid email address');
            emailInput.focus();
            return;
        }
        
        // Check if user exists with this email
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const userExists = users.some(user => user.email === email);
        
        if (!userExists) {
            alert('No account found with this email address. Please check and try again.');
            emailInput.focus();
            return;
        }
        
        // Show loading animation
        const hideLoading = showLoading();
        
        // Store email in sessionStorage for the next step
        sessionStorage.setItem('resetPasswordEmail', email);
        
        // Simulate API call delay
        setTimeout(function() {
            // Hide loading animation
            hideLoading();
            
            // Show success message
            alert('Email verification successful! Please create a new password.');
            
            // Redirect to password reset page
            window.location.href = 'forgetpass.html';
        }, 1500); // Simulate 1.5 second delay
    });
});