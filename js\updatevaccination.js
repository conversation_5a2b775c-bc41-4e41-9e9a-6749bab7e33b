/**
 * Update Vaccination System
 * Comprehensive OOP-based system for updating existing vaccinations
 * Integrates with existing vaccination management system
 *
 * <AUTHOR> Management System
 * @version 1.0.0
 */

// ==================== DATA MANAGER ====================

/**
 * Handles all data operations for vaccination updates
 */
class UpdateVaccinationDataManager {
    constructor() {
        this.vaccinations = [];
        this.currentVaccinationIndex = null;
        this.originalVaccination = null;
        this.loadData();
        this.loadSelectedVaccination();
    }

    /**
     * Load vaccinations data from localStorage
     */
    loadData() {
        try {
            this.vaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');
            console.log(`📊 Loaded ${this.vaccinations.length} vaccinations`);
        } catch (error) {
            console.error('❌ Error loading vaccinations:', error);
            this.vaccinations = [];
        }
    }

    /**
     * Load selected vaccination from sessionStorage
     */
    loadSelectedVaccination() {
        try {
            const vaccinationIndex = sessionStorage.getItem('updateVaccinationId');
            if (vaccinationIndex !== null) {
                this.currentVaccinationIndex = parseInt(vaccinationIndex);
                this.originalVaccination = this.vaccinations[this.currentVaccinationIndex];

                if (this.originalVaccination) {
                    console.log(`📝 Loaded vaccination for update:`, this.originalVaccination);
                    this.isPreselectedMode = true;
                } else {
                    console.error('❌ Vaccination not found at index:', this.currentVaccinationIndex);
                    this.isPreselectedMode = false;
                }
            } else {
                console.log('📝 No vaccination preselected - entering search mode');
                this.isPreselectedMode = false;
            }
        } catch (error) {
            console.error('❌ Error loading selected vaccination:', error);
            this.isPreselectedMode = false;
        }
    }

    /**
     * Find vaccination by name
     * @param {string} name - Vaccination name
     * @returns {Object|null} - Found vaccination with index or null
     */
    findVaccinationByName(name) {
        if (!name || !name.trim()) {
            return null;
        }

        const normalizedName = name.trim().toLowerCase();
        const foundIndex = this.vaccinations.findIndex(vaccination =>
            vaccination.name && vaccination.name.toLowerCase() === normalizedName
        );

        if (foundIndex >= 0) {
            return {
                vaccination: this.vaccinations[foundIndex],
                index: foundIndex
            };
        }

        return null;
    }

    /**
     * Load vaccination by name for editing
     * @param {string} name - Vaccination name
     * @returns {boolean} - True if found and loaded
     */
    loadVaccinationByName(name) {
        const result = this.findVaccinationByName(name);

        if (result) {
            this.currentVaccinationIndex = result.index;
            this.originalVaccination = result.vaccination;
            this.isPreselectedMode = false; // Mark as search mode

            console.log(`📝 Loaded vaccination by name: ${name}`, this.originalVaccination);
            return true;
        }

        console.log(`❌ Vaccination not found: ${name}`);
        return false;
    }

    /**
     * Save vaccinations data to localStorage
     */
    saveData() {
        try {
            localStorage.setItem('vaccinations', JSON.stringify(this.vaccinations));
            console.log('✅ Vaccinations data saved successfully');
            return true;
        } catch (error) {
            console.error('❌ Error saving vaccinations:', error);
            return false;
        }
    }

    /**
     * Check if vaccination name already exists (excluding current vaccination)
     * @param {string} name - Vaccination name
     * @returns {boolean} - True if exists
     */
    vaccinationExists(name) {
        if (!name || !name.trim()) {
            return false;
        }

        const normalizedName = name.trim().toLowerCase();
        return this.vaccinations.some((vaccination, index) =>
            index !== this.currentVaccinationIndex &&
            vaccination.name && vaccination.name.toLowerCase() === normalizedName
        );
    }

    /**
     * Update existing vaccination
     * @param {Object} vaccinationData - Updated vaccination data
     * @returns {Object} - Updated vaccination
     */
    updateVaccination(vaccinationData) {
        if (this.currentVaccinationIndex === null || !this.originalVaccination) {
            throw new Error('No vaccination selected for update');
        }

        const timestamp = new Date().toISOString();

        // Create updated vaccination object
        const updatedVaccination = {
            ...this.originalVaccination, // Preserve original metadata
            name: vaccinationData.name.trim(),
            type: vaccinationData.type.trim(),
            dose: vaccinationData.dose.trim(),
            timeToTake: vaccinationData.timeToTake,
            updatedAt: timestamp,
            lastModified: timestamp,
            source: 'updatevaccination_form'
        };

        // Update in array
        this.vaccinations[this.currentVaccinationIndex] = updatedVaccination;

        // Save data
        if (!this.saveData()) {
            throw new Error('Failed to save vaccination data');
        }

        console.log(`✅ Vaccination "${updatedVaccination.name}" updated successfully`);
        return updatedVaccination;
    }

    /**
     * Get vaccination statistics
     * @returns {Object} - Statistics object
     */
    getStatistics() {
        const stats = {
            totalVaccinations: this.vaccinations.length,
            typeDistribution: {},
            recentlyUpdated: 0
        };

        // Calculate type distribution
        this.vaccinations.forEach(vaccination => {
            const type = vaccination.type || 'Unknown';
            stats.typeDistribution[type] = (stats.typeDistribution[type] || 0) + 1;
        });

        // Calculate recently updated (last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

        stats.recentlyUpdated = this.vaccinations.filter(vaccination =>
            vaccination.updatedAt && new Date(vaccination.updatedAt) >= sevenDaysAgo
        ).length;

        return stats;
    }

    /**
     * Trigger storage events for other pages to update
     */
    triggerStorageEvents() {
        window.dispatchEvent(new StorageEvent('storage', {
            key: 'vaccinations',
            newValue: JSON.stringify(this.vaccinations)
        }));
        console.log('📡 Storage events triggered for vaccinations update');
    }

    /**
     * Clear session data
     */
    clearSession() {
        sessionStorage.removeItem('updateVaccinationId');
        console.log('🔄 Session data cleared');
    }
}

// ==================== VALIDATION MANAGER ====================

/**
 * Handles form validation for vaccination updates
 */
class UpdateVaccinationValidationManager {
    /**
     * Validate vaccination name
     * @param {string} name - Vaccination name
     * @returns {Array} - Array of error messages
     */
    validateName(name) {
        const errors = [];

        if (!name || !name.trim()) {
            errors.push('Vaccination name is required');
        } else if (name.trim().length < 2) {
            errors.push('Vaccination name must be at least 2 characters');
        } else if (name.trim().length > 100) {
            errors.push('Vaccination name must be less than 100 characters');
        }

        return errors;
    }

    /**
     * Validate vaccination type
     * @param {string} type - Vaccination type
     * @returns {Array} - Array of error messages
     */
    validateType(type) {
        const errors = [];

        if (!type || !type.trim()) {
            errors.push('Vaccination type is required');
        } else if (type.trim().length < 2) {
            errors.push('Vaccination type must be at least 2 characters');
        }

        return errors;
    }

    /**
     * Validate dose
     * @param {string} dose - Dose amount
     * @returns {Array} - Array of error messages
     */
    validateDose(dose) {
        const errors = [];

        if (!dose || !dose.trim()) {
            errors.push('Dose is required');
        } else if (dose.trim().length < 1) {
            errors.push('Dose must be specified');
        }

        return errors;
    }

    /**
     * Validate time to take
     * @param {string} timeToTake - Time to take
     * @returns {Array} - Array of error messages
     */
    validateTimeToTake(timeToTake) {
        const errors = [];

        if (!timeToTake || !timeToTake.trim()) {
            errors.push('Time to take is required');
        } else {
            // Validate time format (HH:MM)
            const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
            if (!timeRegex.test(timeToTake)) {
                errors.push('Please enter a valid time (HH:MM format)');
            }
        }

        return errors;
    }

    /**
     * Validate all form data
     * @param {Object} formData - Form data object
     * @returns {Array} - Array of all error messages
     */
    validateFormData(formData) {
        const allErrors = [];

        allErrors.push(...this.validateName(formData.name));
        allErrors.push(...this.validateType(formData.type));
        allErrors.push(...this.validateDose(formData.dose));
        allErrors.push(...this.validateTimeToTake(formData.timeToTake));

        return allErrors;
    }
}

// ==================== UI MANAGER ====================

/**
 * Handles UI interactions and notifications
 */
class UpdateVaccinationUIManager {
    constructor() {
        this.notificationTimeout = null;
    }

    /**
     * Show notification message
     * @param {string} message - Message to display
     * @param {string} type - Type of notification (success, error, warning, info)
     * @param {number} duration - Duration in milliseconds
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Clear existing notification
        this.clearNotification();

        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 15px 25px;
            border-radius: 8px;
            font-family: Arial, sans-serif;
            font-size: 14px;
            font-weight: 500;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 400px;
            text-align: center;
            animation: slideDown 0.3s ease-out;
        `;

        // Set colors based on type
        const colors = {
            success: { bg: '#d4edda', color: '#155724', border: '#c3e6cb' },
            error: { bg: '#f8d7da', color: '#721c24', border: '#f5c6cb' },
            warning: { bg: '#fff3cd', color: '#856404', border: '#ffeaa7' },
            info: { bg: '#d1ecf1', color: '#0c5460', border: '#bee5eb' }
        };

        const colorScheme = colors[type] || colors.info;
        notification.style.backgroundColor = colorScheme.bg;
        notification.style.color = colorScheme.color;
        notification.style.border = `1px solid ${colorScheme.border}`;

        notification.textContent = message;

        // Add animation styles
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideDown {
                from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
                to { transform: translateX(-50%) translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        document.body.appendChild(notification);

        // Auto-remove notification
        this.notificationTimeout = setTimeout(() => {
            this.clearNotification();
        }, duration);

        console.log(`📢 Notification (${type}): ${message}`);
    }

    /**
     * Clear current notification
     */
    clearNotification() {
        const existingNotification = document.querySelector('.notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        if (this.notificationTimeout) {
            clearTimeout(this.notificationTimeout);
            this.notificationTimeout = null;
        }
    }

    /**
     * Show field error
     * @param {HTMLElement} fieldElement - Field element
     * @param {string} errorMessage - Error message
     */
    showFieldError(fieldElement, errorMessage) {
        this.clearFieldError(fieldElement);

        fieldElement.style.borderColor = '#dc3545';
        fieldElement.style.backgroundColor = '#fff5f5';

        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error-message';
        errorElement.style.cssText = `
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
            font-family: Arial, sans-serif;
        `;
        errorElement.textContent = errorMessage;

        fieldElement.parentNode.appendChild(errorElement);
    }

    /**
     * Clear field error
     * @param {HTMLElement} fieldElement - Field element
     */
    clearFieldError(fieldElement) {
        fieldElement.style.borderColor = '';
        fieldElement.style.backgroundColor = '';

        const errorElement = fieldElement.parentNode.querySelector('.field-error-message');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * Clear all field errors
     */
    clearAllFieldErrors() {
        const errorElements = document.querySelectorAll('.field-error-message');
        errorElements.forEach(element => element.remove());

        const inputElements = document.querySelectorAll('input');
        inputElements.forEach(input => {
            input.style.borderColor = '';
            input.style.backgroundColor = '';
        });
    }

    /**
     * Highlight field success
     * @param {HTMLElement} fieldElement - Field element
     */
    highlightFieldSuccess(fieldElement) {
        fieldElement.style.borderColor = '#28a745';
        fieldElement.style.backgroundColor = '#f8fff9';

        setTimeout(() => {
            fieldElement.style.borderColor = '';
            fieldElement.style.backgroundColor = '';
        }, 2000);
    }

    /**
     * Highlight field as modified
     * @param {HTMLElement} fieldElement - Field element
     */
    highlightFieldModified(fieldElement) {
        fieldElement.style.borderColor = '#ffc107';
        fieldElement.style.backgroundColor = '#fffbf0';
    }

    /**
     * Show loading state on button (preserves original HTML structure)
     * @param {HTMLElement} buttonElement - Button element
     */
    showLoading(buttonElement) {
        if (buttonElement) {
            buttonElement.disabled = true;
            buttonElement.style.opacity = '0.7';

            // Find the .btntext element inside the button
            const btnTextElement = buttonElement.querySelector('.btntext');
            if (btnTextElement) {
                const originalText = btnTextElement.textContent;
                btnTextElement.textContent = 'Updating...';
                btnTextElement.dataset.originalText = originalText;
            }
        }
    }

    /**
     * Hide loading state on button (restores original HTML structure)
     * @param {HTMLElement} buttonElement - Button element
     */
    hideLoading(buttonElement) {
        if (buttonElement) {
            buttonElement.disabled = false;
            buttonElement.style.opacity = '';

            // Find the .btntext element inside the button
            const btnTextElement = buttonElement.querySelector('.btntext');
            if (btnTextElement) {
                const originalText = btnTextElement.dataset.originalText;
                if (originalText) {
                    btnTextElement.textContent = originalText;
                    delete btnTextElement.dataset.originalText;
                }
            }
        }
    }
}

// ==================== MAIN CONTROLLER ====================

/**
 * Main controller for Update Vaccination system
 */
class UpdateVaccinationController {
    constructor() {
        this.dataManager = new UpdateVaccinationDataManager();
        this.validationManager = new UpdateVaccinationValidationManager();
        this.uiManager = new UpdateVaccinationUIManager();

        this.elements = {};
        this.formData = {};
        this.originalFormData = {};

        console.log('🎯 UpdateVaccinationController initialized');
    }

    /**
     * Initialize the system
     */
    init() {
        try {
            this.initializeElements();
            this.setupEventListeners();
            this.setupRealTimeValidation();

            // Check if vaccination is preselected or if we're in search mode
            if (this.dataManager.isPreselectedMode && this.dataManager.originalVaccination) {
                // Preselected mode - populate form immediately
                this.populateForm();
                console.log('✅ Update Vaccination system initialized in preselected mode');
                this.uiManager.showNotification(
                    `Editing vaccination: ${this.dataManager.originalVaccination.name}`,
                    'info',
                    3000
                );
            } else {
                // Search mode - show instructions
                console.log('✅ Update Vaccination system initialized in search mode');
                this.uiManager.showNotification(
                    'Enter vaccination name to search and update',
                    'info',
                    4000
                );
                this.showSearchInstructions();
            }

        } catch (error) {
            console.error('❌ Failed to initialize Update Vaccination system:', error);
            this.uiManager.showNotification('Failed to initialize system. Please refresh the page.', 'error');
        }
    }

    /**
     * Show search instructions for name-based search
     */
    showSearchInstructions() {
        // Focus on name input for immediate search
        if (this.elements.nameInput) {
            this.elements.nameInput.focus();
            this.elements.nameInput.placeholder = 'Enter vaccination name to search...';
        }

        // Disable other fields until vaccination is found
        this.setFieldsEnabled(false, true); // Keep name field enabled
    }

    /**
     * Enable/disable form fields
     * @param {boolean} enabled - Whether fields should be enabled
     * @param {boolean} keepNameEnabled - Whether to keep name field enabled
     */
    setFieldsEnabled(enabled, keepNameEnabled = false) {
        const fields = [
            { element: this.elements.typeInput, name: 'type' },
            { element: this.elements.doseInput, name: 'dose' },
            { element: this.elements.timeToTakeInput, name: 'time' }
        ];

        if (!keepNameEnabled) {
            fields.push({ element: this.elements.nameInput, name: 'name' });
        }

        fields.forEach(field => {
            if (field.element) {
                field.element.disabled = !enabled;
                field.element.style.opacity = enabled ? '1' : '0.6';
                field.element.style.backgroundColor = enabled ? '' : '#f8f9fa';
            }
        });

        // Save button
        if (this.elements.saveButton) {
            this.elements.saveButton.disabled = !enabled;
            this.elements.saveButton.style.opacity = enabled ? '1' : '0.6';
        }
    }

    /**
     * Handle case when no vaccination is selected (legacy method for compatibility)
     */
    handleNoVaccinationSelected() {
        // This method is kept for compatibility but not used in dual mode
        console.log('📝 No vaccination preselected - entering search mode');
    }

    /**
     * Initialize DOM elements
     */
    initializeElements() {
        // Get form inputs based on HTML structure
        const inputs = document.querySelectorAll('.data-filled');

        if (inputs.length >= 4) {
            this.elements.nameInput = inputs[0];
            this.elements.typeInput = inputs[1];
            this.elements.doseInput = inputs[2];
            this.elements.timeToTakeInput = inputs[3];
        } else {
            throw new Error('Required form inputs not found');
        }

        // Get save button
        this.elements.saveButton = document.querySelector('.frame-9');
        if (!this.elements.saveButton) {
            throw new Error('Save button not found');
        }

        console.log('📋 DOM elements initialized:', Object.keys(this.elements));
    }

    /**
     * Populate form with existing vaccination data
     */
    populateForm() {
        const vaccination = this.dataManager.originalVaccination;

        if (vaccination) {
            this.elements.nameInput.value = vaccination.name || '';
            this.elements.typeInput.value = vaccination.type || '';
            this.elements.doseInput.value = vaccination.dose || '';

            // Handle time format conversion
            if (vaccination.timeToTake) {
                // If it's a full datetime, extract just the time part
                if (vaccination.timeToTake.includes('T') || vaccination.timeToTake.includes(' ')) {
                    const date = new Date(vaccination.timeToTake);
                    const hours = date.getHours().toString().padStart(2, '0');
                    const minutes = date.getMinutes().toString().padStart(2, '0');
                    this.elements.timeToTakeInput.value = `${hours}:${minutes}`;
                } else {
                    // Assume it's already in HH:MM format
                    this.elements.timeToTakeInput.value = vaccination.timeToTake;
                }
            }

            // Store original form data for comparison
            this.originalFormData = {
                name: vaccination.name || '',
                type: vaccination.type || '',
                dose: vaccination.dose || '',
                timeToTake: this.elements.timeToTakeInput.value
            };

            console.log('📝 Form populated with vaccination data:', this.originalFormData);
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Save button click
        this.elements.saveButton.addEventListener('click', () => {
            this.handleSave();
        });

        // Enter key on inputs
        Object.values(this.elements).forEach(element => {
            if (element && element.tagName === 'INPUT') {
                element.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.handleSave();
                    }
                });
            }
        });

        // Back navigation (browser back button or escape key)
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges()) {
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            }
        });

        // Escape key to go back
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.handleBack();
            }
        });

        console.log('🔗 Event listeners setup complete');
    }

    /**
     * Setup real-time validation
     */
    setupRealTimeValidation() {
        // Name input - real-time validation and duplicate check
        if (this.elements.nameInput) {
            this.elements.nameInput.addEventListener('input', (e) => {
                this.handleNameInput(e);
            });

            this.elements.nameInput.addEventListener('blur', (e) => {
                this.validateVaccinationName(e.target.value);
            });
        }

        // Type input - real-time validation
        if (this.elements.typeInput) {
            this.elements.typeInput.addEventListener('input', (e) => {
                this.handleTypeInput(e);
            });
        }

        // Dose input - real-time validation
        if (this.elements.doseInput) {
            this.elements.doseInput.addEventListener('input', (e) => {
                this.handleDoseInput(e);
            });
        }

        // Time input - real-time validation
        if (this.elements.timeToTakeInput) {
            this.elements.timeToTakeInput.addEventListener('input', (e) => {
                this.handleTimeInput(e);
            });
        }

        console.log('✅ Real-time validation setup complete');
    }

    /**
     * Handle vaccination name input
     * @param {Event} event - Input event
     */
    handleNameInput(event) {
        const name = event.target.value.trim();
        this.formData.name = name;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // If in search mode and no vaccination loaded yet, try to search
        if (!this.dataManager.isPreselectedMode && !this.dataManager.originalVaccination && name) {
            // Real-time search (debounced)
            clearTimeout(this.nameSearchTimeout);
            this.nameSearchTimeout = setTimeout(() => {
                this.searchVaccinationByName(name);
            }, 800);
        } else if (this.dataManager.originalVaccination) {
            // Normal editing mode - check for modifications and duplicates
            if (name !== this.originalFormData.name) {
                this.uiManager.highlightFieldModified(event.target);
            }

            // Real-time validation (debounced)
            clearTimeout(this.nameInputTimeout);
            this.nameInputTimeout = setTimeout(() => {
                if (name) {
                    this.validateVaccinationName(name);
                }
            }, 500);
        }
    }

    /**
     * Search for vaccination by name and load it for editing
     * @param {string} name - Vaccination name to search
     */
    searchVaccinationByName(name) {
        console.log(`🔍 Searching for vaccination: ${name}`);

        const found = this.dataManager.loadVaccinationByName(name);

        if (found) {
            // Vaccination found - populate form and enable fields
            this.populateForm();
            this.setFieldsEnabled(true);

            this.uiManager.showNotification(
                `Vaccination "${name}" found! You can now edit it.`,
                'success',
                3000
            );

            // Highlight the name field as found
            this.uiManager.highlightFieldSuccess(this.elements.nameInput);

            console.log(`✅ Vaccination loaded for editing: ${name}`);
        } else {
            // Vaccination not found
            this.uiManager.showFieldError(
                this.elements.nameInput,
                'Vaccination not found. Please check the name.'
            );

            // Keep other fields disabled
            this.setFieldsEnabled(false, true);

            console.log(`❌ Vaccination not found: ${name}`);
        }
    }

    /**
     * Validate vaccination name and show feedback
     * @param {string} name - Vaccination name
     */
    validateVaccinationName(name) {
        if (!name || !name.trim()) {
            return;
        }

        // Check for duplicates (excluding current vaccination)
        const exists = this.dataManager.vaccinationExists(name);

        if (exists) {
            this.uiManager.showFieldError(this.elements.nameInput, 'Vaccination with this name already exists');
            this.uiManager.showNotification('Vaccination name already exists. Please choose a different name.', 'warning', 3000);
        } else {
            // Validate name format
            const errors = this.validationManager.validateName(name);
            if (errors.length > 0) {
                this.uiManager.showFieldError(this.elements.nameInput, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(this.elements.nameInput);
            }
        }
    }

    /**
     * Handle type input
     * @param {Event} event - Input event
     */
    handleTypeInput(event) {
        const value = event.target.value.trim();
        this.formData.type = value;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Highlight if modified
        if (value !== this.originalFormData.type) {
            this.uiManager.highlightFieldModified(event.target);
        }

        // Real-time validation
        if (value) {
            const errors = this.validationManager.validateType(value);
            if (errors.length > 0) {
                this.uiManager.showFieldError(event.target, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(event.target);
            }
        }
    }

    /**
     * Handle dose input
     * @param {Event} event - Input event
     */
    handleDoseInput(event) {
        const value = event.target.value.trim();
        this.formData.dose = value;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Highlight if modified
        if (value !== this.originalFormData.dose) {
            this.uiManager.highlightFieldModified(event.target);
        }

        // Real-time validation
        if (value) {
            const errors = this.validationManager.validateDose(value);
            if (errors.length > 0) {
                this.uiManager.showFieldError(event.target, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(event.target);
            }
        }
    }

    /**
     * Handle time input
     * @param {Event} event - Input event
     */
    handleTimeInput(event) {
        const value = event.target.value.trim();
        this.formData.timeToTake = value;

        // Clear previous errors
        this.uiManager.clearFieldError(event.target);

        // Highlight if modified
        if (value !== this.originalFormData.timeToTake) {
            this.uiManager.highlightFieldModified(event.target);
        }

        // Real-time validation
        if (value) {
            const errors = this.validationManager.validateTimeToTake(value);
            if (errors.length > 0) {
                this.uiManager.showFieldError(event.target, errors[0]);
            } else {
                this.uiManager.highlightFieldSuccess(event.target);
            }
        }
    }

    /**
     * Check if form has unsaved changes
     * @returns {boolean} - True if changes exist
     */
    hasUnsavedChanges() {
        const currentData = this.collectFormData();

        return (
            currentData.name !== this.originalFormData.name ||
            currentData.type !== this.originalFormData.type ||
            currentData.dose !== this.originalFormData.dose ||
            currentData.timeToTake !== this.originalFormData.timeToTake
        );
    }

    /**
     * Collect form data
     * @returns {Object} - Form data object
     */
    collectFormData() {
        return {
            name: this.elements.nameInput.value.trim(),
            type: this.elements.typeInput.value.trim(),
            dose: this.elements.doseInput.value.trim(),
            timeToTake: this.elements.timeToTakeInput.value.trim()
        };
    }

    /**
     * Validate entire form
     * @returns {boolean} - Validation result
     */
    validateForm() {
        const formData = this.collectFormData();
        const errors = this.validationManager.validateFormData(formData);

        // Clear all previous errors
        this.uiManager.clearAllFieldErrors();

        if (errors.length > 0) {
            // Show first error as notification
            this.uiManager.showNotification(errors[0], 'error');

            // Show field-specific errors
            this.showFieldErrors(errors);
            return false;
        }

        // Additional check: verify vaccination name doesn't exist (excluding current)
        const exists = this.dataManager.vaccinationExists(formData.name);
        if (exists) {
            this.uiManager.showFieldError(this.elements.nameInput, 'Vaccination with this name already exists');
            this.uiManager.showNotification('Please enter a unique vaccination name', 'error');
            return false;
        }

        return true;
    }

    /**
     * Show field-specific errors
     * @param {Array} errors - Array of error messages
     */
    showFieldErrors(errors) {
        errors.forEach(error => {
            if (error.toLowerCase().includes('name')) {
                this.uiManager.showFieldError(this.elements.nameInput, error);
            } else if (error.toLowerCase().includes('type')) {
                this.uiManager.showFieldError(this.elements.typeInput, error);
            } else if (error.toLowerCase().includes('dose')) {
                this.uiManager.showFieldError(this.elements.doseInput, error);
            } else if (error.toLowerCase().includes('time')) {
                this.uiManager.showFieldError(this.elements.timeToTakeInput, error);
            }
        });
    }

    /**
     * Handle save button click
     */
    async handleSave() {
        console.log('💾 Save button clicked');

        if (!this.validateForm()) {
            console.log('❌ Form validation failed');
            return;
        }

        // Check if there are any changes
        if (!this.hasUnsavedChanges()) {
            this.uiManager.showNotification('No changes detected. Nothing to update.', 'info');
            return;
        }

        const formData = this.collectFormData();
        console.log('📝 Form data collected:', formData);

        // Show loading state
        this.uiManager.showLoading(this.elements.saveButton);

        try {
            // Update vaccination
            const updatedVaccination = this.dataManager.updateVaccination(formData);

            console.log('✅ Vaccination updated successfully:', updatedVaccination);

            // Show success message with details
            const stats = this.dataManager.getStatistics();
            this.uiManager.showNotification(
                `Vaccination "${updatedVaccination.name}" updated successfully!`,
                'success',
                5000
            );

            // Highlight updated fields
            this.highlightUpdatedFields();

            // Trigger storage events for other pages to update
            this.dataManager.triggerStorageEvents();

            // Update original form data
            this.originalFormData = { ...formData };

            // Navigate back after delay
            setTimeout(() => {
                this.handleBack();
            }, 2000);

        } catch (error) {
            console.error('❌ Error updating vaccination:', error);
            this.uiManager.showNotification(
                `Failed to update vaccination: ${error.message}`,
                'error'
            );
        } finally {
            // Hide loading state
            this.uiManager.hideLoading(this.elements.saveButton);
        }
    }

    /**
     * Highlight updated fields with success styling
     */
    highlightUpdatedFields() {
        const fields = [
            this.elements.nameInput,
            this.elements.typeInput,
            this.elements.doseInput,
            this.elements.timeToTakeInput
        ];

        fields.forEach(field => {
            if (field) {
                this.uiManager.highlightFieldSuccess(field);
            }
        });
    }

    /**
     * Handle back navigation
     */
    handleBack() {
        if (this.hasUnsavedChanges()) {
            const confirmLeave = confirm('You have unsaved changes. Are you sure you want to leave?');
            if (!confirmLeave) {
                return;
            }
            
        }

        // Clear session data
        this.dataManager.clearSession();

        // Navigate back to vaccination page
        // window.location.href = './vaccination.html';
         window.parent.location.reload();
    }

    /**
     * Clear search and reset form
     */
    clearSearch() {
        // Reset data manager state
        this.dataManager.currentVaccinationIndex = null;
        this.dataManager.originalVaccination = null;
        this.dataManager.isPreselectedMode = false;

        // Clear form
        this.clearForm();

        // Reset to search mode
        this.showSearchInstructions();

        console.log('🔄 Search cleared, back to search mode');
        this.uiManager.showNotification('Search cleared. Enter vaccination name to search again.', 'info', 3000);
    }

    /**
     * Check if currently in search mode
     * @returns {boolean} - True if in search mode
     */
    isSearchMode() {
        return !this.dataManager.isPreselectedMode && !this.dataManager.originalVaccination;
    }

    /**
     * Check if vaccination is currently loaded
     * @returns {boolean} - True if vaccination is loaded
     */
    isVaccinationLoaded() {
        return this.dataManager.originalVaccination !== null;
    }

    /**
     * Get current mode description
     * @returns {string} - Mode description
     */
    getCurrentMode() {
        if (this.dataManager.isPreselectedMode) {
            return 'Preselected Mode';
        } else if (this.dataManager.originalVaccination) {
            return 'Search Mode (Vaccination Loaded)';
        } else {
            return 'Search Mode (No Vaccination)';
        }
    }

    /**
     * Clear form to initial state
     */
    clearForm() {
        // Clear form data
        this.formData = {};
        this.originalFormData = {};

        // Clear all input fields
        Object.values(this.elements).forEach(element => {
            if (element && element.tagName === 'INPUT') {
                element.value = '';
                element.placeholder = '';
            }
        });

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        console.log('🔄 Form cleared');
    }

    /**
     * Get vaccination statistics for display
     * @returns {Object} - Statistics object
     */
    getStatistics() {
        return this.dataManager.getStatistics();
    }
}

// ==================== INITIALIZATION ====================

/**
 * Initialize the Update Vaccination system when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing Update Vaccination system...');

    try {
        // Create and initialize the controller
        const updateVaccinationController = new UpdateVaccinationController();
        updateVaccinationController.init();

        // Make it globally accessible for debugging and external access
        window.updateVaccinationController = updateVaccinationController;

        console.log('🎉 Update Vaccination system ready!');
        console.log('Available global methods:');
        console.log('- window.updateVaccinationController.getStatistics()');
        console.log('- window.updateVaccinationController.handleBack()');

    } catch (error) {
        console.error('💥 Failed to initialize Update Vaccination system:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        errorDiv.textContent = 'Failed to initialize the vaccination update form. Please refresh the page.';
        document.body.appendChild(errorDiv);
    }
});

// ==================== UTILITY FUNCTIONS ====================

/**
 * Update existing vaccination (utility function)
 * @param {number} index - Vaccination index
 * @param {Object} vaccinationData - Updated vaccination data
 * @returns {Object|null} - Updated vaccination or null
 */
window.updateExistingVaccination = function(index, vaccinationData) {
    if (!window.updateVaccinationController) {
        console.error('Update Vaccination system not initialized');
        return null;
    }

    try {
        // Set the vaccination index
        window.updateVaccinationController.dataManager.currentVaccinationIndex = index;
        window.updateVaccinationController.dataManager.originalVaccination =
            window.updateVaccinationController.dataManager.vaccinations[index];

        return window.updateVaccinationController.dataManager.updateVaccination(vaccinationData);
    } catch (error) {
        console.error('Error updating vaccination:', error);
        return null;
    }
};

/**
 * Check if vaccination name exists (excluding current) (utility function)
 * @param {string} name - Vaccination name
 * @returns {boolean} - True if exists
 */
window.vaccinationNameExists = function(name) {
    if (!window.updateVaccinationController) {
        console.error('Update Vaccination system not initialized');
        return false;
    }

    return window.updateVaccinationController.dataManager.vaccinationExists(name);
};

/**
 * Get current vaccination being edited (utility function)
 * @returns {Object|null} - Current vaccination or null
 */
window.getCurrentVaccination = function() {
    if (!window.updateVaccinationController) {
        console.error('Update Vaccination system not initialized');
        return null;
    }

    return window.updateVaccinationController.dataManager.originalVaccination;
};

/**
 * Check if form has unsaved changes (utility function)
 * @returns {boolean} - True if changes exist
 */
window.hasUnsavedVaccinationChanges = function() {
    if (!window.updateVaccinationController) {
        console.error('Update Vaccination system not initialized');
        return false;
    }

    return window.updateVaccinationController.hasUnsavedChanges();
};

/**
 * Search for vaccination by name and load it (utility function)
 * @param {string} name - Vaccination name
 * @returns {boolean} - True if found and loaded
 */
window.searchVaccinationByName = function(name) {
    if (!window.updateVaccinationController) {
        console.error('Update Vaccination system not initialized');
        return false;
    }

    return window.updateVaccinationController.searchVaccinationByName(name);
};

/**
 * Clear current search and reset to search mode (utility function)
 */
window.clearVaccinationSearch = function() {
    if (!window.updateVaccinationController) {
        console.error('Update Vaccination system not initialized');
        return;
    }

    window.updateVaccinationController.clearSearch();
};

/**
 * Get current mode information (utility function)
 * @returns {Object} - Mode information
 */
window.getUpdateVaccinationMode = function() {
    if (!window.updateVaccinationController) {
        console.error('Update Vaccination system not initialized');
        return null;
    }

    return {
        mode: window.updateVaccinationController.getCurrentMode(),
        isSearchMode: window.updateVaccinationController.isSearchMode(),
        isVaccinationLoaded: window.updateVaccinationController.isVaccinationLoaded(),
        isPreselected: window.updateVaccinationController.dataManager.isPreselectedMode
    };
};

// ==================== DEBUG FUNCTIONS ====================

/**
 * Debug function to check system status
 */
window.debugUpdateVaccination = function() {
    console.log('=== Update Vaccination Debug Info ===');

    if (!window.updateVaccinationController) {
        console.error('❌ Update Vaccination system not initialized');
        return;
    }

    console.log('✅ System initialized');
    console.log('📊 Vaccinations loaded:', window.updateVaccinationController.dataManager.vaccinations.length);
    console.log('🎯 Current mode:', window.updateVaccinationController.getCurrentMode());
    console.log('🔍 Is preselected mode:', window.updateVaccinationController.dataManager.isPreselectedMode);
    console.log('🔍 Is search mode:', window.updateVaccinationController.isSearchMode());
    console.log('📝 Current vaccination index:', window.updateVaccinationController.dataManager.currentVaccinationIndex);
    console.log('📄 Original vaccination:', window.updateVaccinationController.dataManager.originalVaccination);
    console.log('📝 Form data:', window.updateVaccinationController.formData);
    console.log('📝 Original form data:', window.updateVaccinationController.originalFormData);
    console.log('🔍 Elements:', Object.keys(window.updateVaccinationController.elements));
    console.log('🔄 Has unsaved changes:', window.updateVaccinationController.hasUnsavedChanges());

    // Check form elements
    const elements = window.updateVaccinationController.elements;
    console.log('Form elements status:');
    Object.entries(elements).forEach(([key, element]) => {
        console.log(`  ${key}: ${element ? '✅ Found' : '❌ Missing'}`);
    });

    // Show statistics
    const stats = window.updateVaccinationController.dataManager.getStatistics();
    console.log('📊 Statistics:', stats);
};

/**
 * Test vaccination update
 * @param {Object} newData - New vaccination data to test
 */
window.testVaccinationUpdate = function(newData = {}) {
    console.log('🧪 Testing vaccination update...');

    if (!window.updateVaccinationController) {
        console.error('❌ Update Vaccination system not initialized');
        return;
    }

    const currentVaccination = window.updateVaccinationController.dataManager.originalVaccination;
    if (!currentVaccination) {
        console.error('❌ No vaccination selected for update');
        return;
    }

    try {
        console.log(`✅ Current vaccination: ${currentVaccination.name}`);

        // Merge with default test data
        const testData = {
            name: newData.name || currentVaccination.name + ' (Updated)',
            type: newData.type || 'Test Type',
            dose: newData.dose || '10ml',
            timeToTake: newData.timeToTake || '10:00',
            ...newData
        };

        console.log('📝 Test data:', testData);

        // Update vaccination
        const result = window.updateVaccinationController.dataManager.updateVaccination(testData);

        console.log('✅ Vaccination updated successfully:', result);

        // Show updated statistics
        const stats = window.updateVaccinationController.dataManager.getStatistics();
        console.log('📊 Updated statistics:', stats);

        return result;

    } catch (error) {
        console.error('❌ Test failed:', error);
    }
};

/**
 * Show current vaccination details
 */
window.showCurrentVaccination = function() {
    console.log('💉 CURRENT VACCINATION DETAILS');
    console.log('==============================');

    if (!window.updateVaccinationController) {
        console.error('❌ Update Vaccination system not initialized');
        return;
    }

    const vaccination = window.updateVaccinationController.dataManager.originalVaccination;

    if (!vaccination) {
        console.log('❌ No vaccination selected for update');
        return;
    }

    console.log(`Name: ${vaccination.name}`);
    console.log(`Type: ${vaccination.type}`);
    console.log(`Dose: ${vaccination.dose}`);
    console.log(`Time to take: ${vaccination.timeToTake}`);
    console.log(`Date added: ${vaccination.dateAdded ? new Date(vaccination.dateAdded).toLocaleDateString() : 'Unknown'}`);
    console.log(`Last updated: ${vaccination.updatedAt ? new Date(vaccination.updatedAt).toLocaleDateString() : 'Never'}`);
    console.log(`Index: ${window.updateVaccinationController.dataManager.currentVaccinationIndex}`);

    return vaccination;
};

console.log(`
💉 UPDATE VACCINATION SYSTEM LOADED (DUAL MODE)
===============================================

🎯 DUAL MODE FEATURES:
   • Preselected Mode: Update vaccination selected from vaccination table
   • Search Mode: Enter vaccination name to find and update any vaccination

🔍 DEBUG FUNCTIONS AVAILABLE:
   • debugUpdateVaccination() - Check system status and current mode
   • testVaccinationUpdate(newData) - Test updating vaccination
   • showCurrentVaccination() - Show current vaccination details
   • searchVaccinationByName(name) - Search and load vaccination by name
   • clearVaccinationSearch() - Clear search and reset to search mode
   • getUpdateVaccinationMode() - Get current mode information
   • updateExistingVaccination(index, data) - Update vaccination programmatically
   • vaccinationNameExists(name) - Check if vaccination name exists
   • getCurrentVaccination() - Get current vaccination being edited
   • hasUnsavedVaccinationChanges() - Check for unsaved changes

✅ Ready for vaccination updates in both modes!
`);