<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>Debug Animal System</title>
    <link rel="stylesheet" href="css/animal.css" />
    <style>
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 350px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 1000;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 14px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .debug-panel h3 {
            margin: 0 0 15px 0;
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .debug-panel button {
            display: block;
            width: 100%;
            margin: 8px 0;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 13px;
            transition: background-color 0.2s;
        }
        .debug-panel button:hover {
            background: #0056b3;
        }
        .debug-panel button.danger {
            background: #dc3545;
        }
        .debug-panel button.danger:hover {
            background: #c82333;
        }
        .debug-panel button.success {
            background: #28a745;
        }
        .debug-panel button.success:hover {
            background: #218838;
        }
        .debug-panel .status {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .debug-panel .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 12px;
            color: #856404;
        }
        .main-content {
            margin-right: 380px;
        }
    </style>
</head>
<body>
    <!-- Debug Panel -->
    <div class="debug-panel">
        <h3>🔧 Debug Panel</h3>
        
        <div class="instructions">
            <strong>Instructions:</strong><br>
            1. Open browser console (F12)<br>
            2. Click buttons below to test<br>
            3. Check console for detailed logs<br>
            4. Try adding animals via popup
        </div>
        
        <button onclick="debugAnimalSystem()">🔍 Debug System Status</button>
        <button onclick="addTestAnimal()" class="success">➕ Add Test Animal</button>
        <button onclick="addSampleData()" class="success">📊 Add Sample Data</button>
        <button onclick="testPopup()">🪟 Test Add Animal Popup</button>
        <button onclick="showStats()">📈 Show Statistics</button>
        <button onclick="testTableRefresh()">🔄 Test Table Refresh</button>
        <button onclick="clearAllData()" class="danger">🗑️ Clear All Data</button>
        
        <div class="status" id="statusDisplay">
            System loading...
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="feed">
            <div class="page-container">
                <main class="content-container">
                    <div class="frame-4">
                        <button class="frame-9">
                            <div class="btntext">Add New Animal</div>
                        </button>
                        <button class="frame-9">
                            <div class="btntext">Updating Data</div>
                        </button>
                        <button class="search">
                            <img class="double-left1" src="icons/search.svg">
                            <div class="btntext1">Search</div>
                        </button>
                        <button class="frame-9">
                            <img class="double-left1" src="icons/filter.svg">
                            <div class="btntext">Filter</div>
                        </button>
                        <button class="frame-9">
                            <div class="btntext">Fattening Weight</div>
                        </button>
                        <button class="frame-9">
                            <div class="btntext">Delete Animal</div>
                        </button>
                    </div>

                    <div class="parent1">
                        <div class="frame-5">
                            <div class="text-wrapper-5">Animals</div>
                        </div>
                        <div class="tablecontainer">
                            <table>
                                <thead>
                                    <tr>
                                        <th>
                                            <input type="checkbox" class="select-all-checkbox" title="Select All">
                                            #
                                        </th>
                                        <th>Code</th>
                                        <th>Type</th>
                                        <th>Herd Number</th>
                                        <th>Gender</th>
                                        <th>Weight</th>
                                        <th>Date of Weight</th>
                                        <th>Healthcare</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td colspan="8" style="text-align: center; padding: 20px; color: #666;">
                                            Loading animals...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Include all required scripts -->
    <script src="js/popup.js"></script>
    <script src="js/addNewAnimalPopup.js"></script>
    <script src="js/updateMilchDataPopup.js"></script>
    <script src="js/updateNewbornPopup.js"></script>
    <script src="js/chooseUpdateMilchPopup.js"></script>
    <script src="js/updateFatteningWeightPopup.js"></script>
    <script src="js/deletePopupConfirmation.js"></script>
    <script src="js/animal.js"></script>

    <script>
        // Debug functions
        function updateStatus(message) {
            const statusDiv = document.getElementById('statusDisplay');
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.textContent = `[${timestamp}] ${message}\n` + statusDiv.textContent;
        }

        function testPopup() {
            if (window.animalController && window.animalController.popupManager) {
                window.animalController.popupManager.showAddNewAnimalPopup();
                updateStatus('Add New Animal popup opened');
            } else {
                updateStatus('ERROR: PopupManager not available');
            }
        }

        function showStats() {
            if (window.animalController) {
                const stats = window.animalController.getStatistics();
                updateStatus(`Statistics: Total=${stats.total}, Dairy=${stats.dairy}, Newborn=${stats.newborn}, Fattening=${stats.fattening}`);
                console.log('Full statistics:', stats);
            } else {
                updateStatus('ERROR: AnimalController not available');
            }
        }

        function testTableRefresh() {
            if (window.animalController && window.animalController.tableManager) {
                window.animalController.tableManager.refresh();
                updateStatus('Table refreshed manually');
            } else {
                updateStatus('ERROR: TableManager not available');
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus('Page loaded, waiting for system initialization...');
            
            setTimeout(() => {
                if (window.animalController) {
                    updateStatus('✅ Animal system initialized successfully');
                    showStats();
                } else {
                    updateStatus('❌ Animal system failed to initialize');
                }
            }, 2000);
        });

        // Monitor for changes
        setInterval(() => {
            if (window.animalController) {
                const stats = window.animalController.getStatistics();
                if (stats.total > 0) {
                    document.title = `Debug Animal System (${stats.total} animals)`;
                }
            }
        }, 5000);
    </script>
</body>
</html>
