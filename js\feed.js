document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const makeNewFeedBtn = document.querySelector('.frame-9:nth-child(1)');
    const editExistFeedBtn = document.querySelector('.frame-9:nth-child(2)');
    const searchBtn = document.querySelector('.search');
    const deleteFeedBtn = document.querySelector('.frame-9:nth-child(4)');
    const tableBody = document.querySelector('tbody');

    // Load feeds from localStorage
    loadFeeds();

    // Function to create table row for a feed
    function createFeedTableRow(feed, index) {
        const row = document.createElement('tr');
        
        // Add highlight effect if this is the newly added feed
        const highlightFeedCode = sessionStorage.getItem('highlightFeedCode');
        if (highlightFeedCode && feed.code === highlightFeedCode) {
            row.style.backgroundColor = '#e6ffe6';
            row.style.boxShadow = '0 0 5px rgba(0,128,0,0.5)';
            sessionStorage.removeItem('highlightFeedCode');
        }
        
        row.innerHTML = `
            <td>
                <div>
                    <form>
                        <label class="selectbox">
                            <input class="checkbox" type="checkbox" data-code="${feed.code}">${index + 1}
                        </label>
                    </form>
                </div>
            </td>
            <td>${feed.feedName || 'N/A'}</td>
            <td>${feed.animalType || 'N/A'}</td>
            <td>${feed.proteinPercentage || 'N/A'}%</td>
            <td>${feed.tdnPercentage || 'N/A'}%</td>
            <td><div class="popup" data-code="${feed.code}">View</div></td>
        `;
        
        return row;
    }

    // Function to load feeds from localStorage
    function loadFeeds() {
        // Clear existing table rows
        tableBody.innerHTML = '';

        // Get feeds from localStorage
        const feeds = JSON.parse(localStorage.getItem('feeds') || '[]');

        if (feeds.length === 0) {
            // If no feeds, display a message
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="6" style="text-align: center;">No feeds found</td>
            `;
            tableBody.appendChild(row);
            return;
        }

        // Add each feed to the table
        feeds.forEach((feed, index) => {
            const row = createFeedTableRow(feed, index);
            tableBody.appendChild(row);
        });

        // Add event listeners to checkboxes and view buttons
        addTableEventListeners();
    }

    // Add event listeners to checkboxes and view buttons
    function addTableEventListeners() {
        // Add event listeners to checkboxes
        const checkboxes = document.querySelectorAll('.checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleCheckboxChange);
        });

        // Add event listeners to view buttons
        const viewButtons = document.querySelectorAll('.popup');
        viewButtons.forEach(button => {
            button.addEventListener('click', function() {
                const feedCode = this.getAttribute('data-code');
                viewFeedDetails(feedCode);
            });
        });
    }

    // Handle checkbox selection
    function handleCheckboxChange(e) {
        const checkboxes = document.querySelectorAll('.checkbox');
        const selectedCount = document.querySelectorAll('.checkbox:checked').length;

        // Enable or disable delete button based on selection
        if (selectedCount > 0) {
            deleteFeedBtn.disabled = false;
            deleteFeedBtn.style.opacity = 1;

            // Enable edit button only if exactly one feed is selected
            if (selectedCount === 1) {
                editExistFeedBtn.disabled = false;
                editExistFeedBtn.style.opacity = 1;
            } else {
                editExistFeedBtn.disabled = true;
                editExistFeedBtn.style.opacity = 0.5;
            }
        } else {
            deleteFeedBtn.disabled = true;
            deleteFeedBtn.style.opacity = 0.5;
            editExistFeedBtn.disabled = true;
            editExistFeedBtn.style.opacity = 0.5;
        }
    }

    // Add event listeners to buttons
    makeNewFeedBtn.addEventListener('click', function() {
        // Clear any existing edit feed code
        sessionStorage.removeItem('editFeedCode');
        window.location.href = 'makenewfeed.html';
    });    editExistFeedBtn.addEventListener('click', function() {
        const selectedCheckbox = document.querySelector('.checkbox:checked');
        if (!selectedCheckbox) {
            // Use custom notification instead of alert
            const notification = document.createElement('div');
            notification.className = 'notification error';
            notification.textContent = 'Please select a feed to edit';
            
            const container = document.getElementById('notification-container') || document.createElement('div');
            if (!document.getElementById('notification-container')) {
                container.id = 'notification-container';
                document.body.appendChild(container);
            }
            
            container.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
            return;
        }

        const feedCode = selectedCheckbox.getAttribute('data-code');
        if (!feedCode) {
            console.error('No feed code found on selected checkbox');
            return;
        }

        // Verify the feed exists in localStorage
        const feeds = JSON.parse(localStorage.getItem('feeds') || '[]');
        const feedToEdit = feeds.find(f => f.code === feedCode);
        
        if (feedToEdit) {
            window.location.href = `editfeed.html?id=${feedCode}`;
        }
        
        if (!feedToEdit) {
            const notification = document.createElement('div');
            notification.className = 'notification error';
            notification.textContent = 'Selected feed not found';
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
            return;
        }

        // Store the feed code and redirect
        sessionStorage.setItem('editFeedCode', feedCode);
        window.location.href = 'editfeed.html';
    });

    searchBtn.addEventListener('click', function() {
        const searchTerm = prompt('Enter feed code or animal type to search:');
        if (searchTerm) {
            searchFeeds(searchTerm);
        }
    });

    deleteFeedBtn.addEventListener('click', function() {
        deleteSelectedFeeds();
    });

    // Function to search feeds
    function searchFeeds(searchTerm) {
        const feeds = JSON.parse(localStorage.getItem('feeds') || '[]');
        const filteredFeeds = feeds.filter(feed =>
            feed.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (feed.animalType && feed.animalType.toLowerCase().includes(searchTerm.toLowerCase()))
        );

        // Clear existing table rows
        tableBody.innerHTML = '';

        if (filteredFeeds.length === 0) {
            // If no matching feeds, display a message
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="6" style="text-align: center;">No matching feeds found</td>
            `;
            tableBody.appendChild(row);
            return;
        }

        // Add each filtered feed to the table
        filteredFeeds.forEach((feed, index) => {
            const row = createFeedTableRow(feed, index);
            tableBody.appendChild(row);
        });

        // Re-add event listeners
        addTableEventListeners();
    }

    // Function to view feed details
    function viewFeedDetails(feedCode) {
        const feeds = JSON.parse(localStorage.getItem('feeds') || '[]');
        const feed = feeds.find(f => f.code === feedCode);

        if (!feed) {
            alert('Feed not found');
            return;
        }

        // Create a modal to display feed details
        const modal = document.createElement('div');
        modal.style.position = 'fixed';
        modal.style.top = '50%';
        modal.style.left = '50%';
        modal.style.transform = 'translate(-50%, -50%)';
        modal.style.backgroundColor = 'white';
        modal.style.padding = '20px';
        modal.style.borderRadius = '10px';
        modal.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';
        modal.style.zIndex = '1000';
        modal.style.maxWidth = '80%';
        modal.style.maxHeight = '80%';
        modal.style.overflow = 'auto';

        // Create feed details HTML
        let detailsHTML = `
            <h2 style="margin-bottom: 15px; color: #333;">Feed Details: ${feed.code}</h2>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Animal Type:</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${feed.animalType || 'N/A'}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Weight:</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${feed.weight || 'N/A'}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Gender:</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${feed.gender || 'N/A'}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Growth Rate:</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${feed.growthRate || 'N/A'}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Protein Percentage:</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${feed.proteinPercentage || 'N/A'}%</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">TDN Percentage:</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${feed.tdnPercentage || 'N/A'}%</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Season:</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${feed.season || 'N/A'}</td>
                </tr>
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; font-weight: bold;">Date Created:</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${new Date(feed.dateCreated).toLocaleDateString() || 'N/A'}</td>
                </tr>
            </table>

            <h3 style="margin: 15px 0; color: #333;">Ingredients:</h3>
            <ul style="list-style-type: disc; padding-left: 20px;">
        `;

        // Add ingredients
        if (feed.ingredients && feed.ingredients.length > 0) {
            feed.ingredients.forEach(ingredient => {
                detailsHTML += `<li style="margin-bottom: 5px;">${ingredient}</li>`;
            });
        } else {
            detailsHTML += `<li>No ingredients</li>`;
        }

        detailsHTML += `
            </ul>
            <div style="margin-top: 20px; text-align: center;">
                <button id="closeModalBtn" style="padding: 8px 16px; background-color: #aedf32; border: none; border-radius: 5px; cursor: pointer;">Close</button>
                <button id="editFeedBtn" style="padding: 8px 16px; background-color: #4CAF50; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px; color: white;">Edit</button>
            </div>
        `;

        modal.innerHTML = detailsHTML;
        document.body.appendChild(modal);

        // Add event listener to close button
        document.getElementById('closeModalBtn').addEventListener('click', function() {
            document.body.removeChild(modal);
        });

        // Add event listener to edit button
        document.getElementById('editFeedBtn').addEventListener('click', function() {
            sessionStorage.setItem('editFeedCode', feedCode);
            window.location.href = 'editexistfeed.html';
            document.body.removeChild(modal);
        });
    }

    // Function to delete selected feeds
    function deleteSelectedFeeds() {
        const selectedCheckboxes = document.querySelectorAll('.checkbox:checked');
        if (selectedCheckboxes.length === 0) return;

        if (confirm(`Are you sure you want to delete ${selectedCheckboxes.length} feed(s)?`)) {
            const feeds = JSON.parse(localStorage.getItem('feeds') || '[]');
            const selectedCodes = Array.from(selectedCheckboxes).map(checkbox =>
                checkbox.getAttribute('data-code')
            );

            // Filter out selected feeds
            const updatedFeeds = feeds.filter(feed => !selectedCodes.includes(feed.code));

            // Update localStorage
            localStorage.setItem('feeds', JSON.stringify(updatedFeeds));

            // Reload the table
            loadFeeds();

            alert('Selected feed(s) deleted successfully');
        }
    }

    // Initialize the page
    deleteFeedBtn.disabled = true;
    deleteFeedBtn.style.opacity = 0.5;
    editExistFeedBtn.disabled = true;
    editExistFeedBtn.style.opacity = 0.5;
});