/**
 * report.js
 *
 * This file handles the functionality for the report.html page.
 * It includes functions for generating different types of reports,
 * handling sidebar navigation, and printing reports.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get UI elements
    const sidebarLinks = document.querySelectorAll('.child a');
    const reportContainers = document.querySelectorAll('.category');
    const backButton = document.querySelector('.double-left');
    const printButton = document.getElementById('printButton');

    // Set up event listeners
    setupEventListeners();

    // Highlight the current page in the sidebar
    highlightCurrentPage();

    /**
     * Set up event listeners for the page
     */
    function setupEventListeners() {
        // Add event listeners to sidebar links
        sidebarLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Get the link text
                const linkText = this.textContent.trim().toLowerCase();

                // Skip action if we're already on the Reports page and clicking the Reports link
                if (linkText === 'reports' && window.location.pathname.includes('report.html')) {
                    e.preventDefault();
                    return; // Do nothing when clicking on Reports link while on the Reports page
                }

                // For other links, navigate to the corresponding page
                switch(linkText) {
                    case 'dashboard':
                        e.preventDefault();
                        window.location.href = 'index.html';
                        break;
                    case 'animals':
                        e.preventDefault();
                        window.location.href = 'animal.html';
                        break;
                    case 'dairy':
                        e.preventDefault();
                        window.location.href = 'dairy.html';
                        break;
                    case 'new born':
                        e.preventDefault();
                        window.location.href = 'newborn.html';
                        break;
                    case 'feed':
                        e.preventDefault();
                        window.location.href = 'feed.html';
                        break;
                    case 'ingredient':
                        e.preventDefault();
                        window.location.href = 'ingrediants.html';
                        break;
                    case 'vaccination':
                        e.preventDefault();
                        window.location.href = 'vaccination.html';
                        break;
                    // For Reports, we're already on the page, so do nothing
                }
            });
        });

        // Add event listener to the back button
        if (backButton) {
            backButton.addEventListener('click', function() {
                window.location.href = 'index.html';
            });
        }

        // Add event listener to the print button
        if (printButton) {
            // Add hover effects
            printButton.style.transition = 'all 0.3s ease';
            printButton.style.cursor = 'pointer';

            printButton.addEventListener('mouseover', function() {
                this.style.backgroundColor = '#9bc728';
                this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
            });

            printButton.addEventListener('mouseout', function() {
                this.style.backgroundColor = '#aedf32';
                this.style.boxShadow = 'none';
            });

            // Add click effect
            printButton.addEventListener('mousedown', function() {
                this.style.transform = 'scale(0.95)';
            });

            printButton.addEventListener('mouseup', function() {
                this.style.transform = 'scale(1)';
            });

            // Add click event to print reports
            printButton.addEventListener('click', function() {
                // Visual feedback
                this.style.backgroundColor = '#8ab526';

                // Print reports
                printReports();

                // Reset button style after a short delay
                setTimeout(() => {
                    this.style.backgroundColor = '#aedf32';
                }, 300);
            });
        }
    }


    /**
     * Print the reports
     */
    function printReports() {
        // Create a new window for printing
        const printWindow = window.open('', '_blank');

        // Create the print content
        let printContent = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>SRA Reports</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 20px;
                    }
                    .report-header {
                        text-align: center;
                        margin-bottom: 30px;
                    }
                    .report-title {
                        font-size: 24px;
                        font-weight: bold;
                        margin-bottom: 10px;
                    }
                    .report-date {
                        font-size: 14px;
                        color: #666;
                    }
                    .report-section {
                        margin-bottom: 30px;
                        page-break-inside: avoid;
                    }
                    .section-title {
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 10px;
                        padding-bottom: 5px;
                        border-bottom: 1px solid #ddd;
                    }
                    .section-content {
                        margin-left: 20px;
                    }
                    .statement {
                        margin-bottom: 10px;
                        line-height: 1.5;
                    }
                    @media print {
                        .report-section {
                            page-break-inside: avoid;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="report-header">
                    <div class="report-title">Smart Raising Animals - Reports</div>
                    <div class="report-date">Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</div>
                </div>
        `;

        // Add each category to the print content
        reportContainers.forEach(container => {
            const titleElement = container.querySelector('.text-wrapper-5');
            const statements = container.querySelectorAll('.statement');

            if (titleElement) {
                const title = titleElement.textContent.trim();

                printContent += `
                    <div class="report-section">
                        <div class="section-title">${title}</div>
                        <div class="section-content">
                `;

                // Add each statement
                statements.forEach(statement => {
                    printContent += `
                        <div class="statement">${statement.textContent.trim()}</div>
                    `;
                });

                printContent += `
                        </div>
                    </div>
                `;
            }
        });

        // Close the HTML
        printContent += `
            </body>
            </html>
        `;

        // Write the content to the new window
        printWindow.document.open();
        printWindow.document.write(printContent);
        printWindow.document.close();

        // Wait for the content to load before printing
        printWindow.onload = function() {
            printWindow.print();
            // Close the window after printing (optional)
            // printWindow.close();
        };
    }
});
