/**
 * Delete Popup Confirmation System
 * A comprehensive, reusable delete confirmation popup for all types of items
 *
 * @author: Animal Management System
 * @version: 1.0.0
 * @description: Object-oriented delete confirmation system with support for multiple item types
 */

// ==================== DELETE POPUP MANAGER ====================

/**
 * Main Delete Popup Manager Class
 * Handles all delete confirmation operations with a reusable popup interface
 */
class DeletePopupManager {
    constructor() {
        this.currentDeleteData = null;
        this.isVisible = false;
        this.popupElement = null;
        this.onConfirmCallback = null;
        this.onCancelCallback = null;
    }

    /**
     * Show delete confirmation popup
     * @param {Object} options - Configuration options
     * @param {Array} options.items - Items to delete
     * @param {string} options.itemType - Type of items (animals, feeds, vaccinations, etc.)
     * @param {Function} options.onConfirm - Callback when delete is confirmed
     * @param {Function} options.onCancel - Callback when delete is cancelled
     * @param {string} options.message - Custom message (optional)
     */
    showDeleteConfirmation(options) {
        const {
            items = [],
            itemType = 'items',
            onConfirm = null,
            onCancel = null,
            message = null
        } = options;

        if (items.length === 0) {
            this.showNotification('No items selected for deletion', 'warning');
            return;
        }

        // Store delete data and callbacks
        this.currentDeleteData = {
            items,
            itemType,
            message: message || this.generateDeleteMessage(items, itemType)
        };
        this.onConfirmCallback = onConfirm;
        this.onCancelCallback = onCancel;

        // Create and show popup
        this.createPopup();
        this.showPopup();
    }

    /**
     * Generate appropriate delete message based on items and type
     * @param {Array} items - Items to delete
     * @param {string} itemType - Type of items
     * @returns {string} - Generated message
     */
    generateDeleteMessage(items, itemType) {
        const count = items.length;
        const itemName = this.getItemDisplayName(itemType, count > 1);

        if (count === 1) {
            const itemIdentifier = this.getItemIdentifier(items[0], itemType);
            return `Delete ${itemName} "${itemIdentifier}"?`;
        } else {
            return `Delete ${count} selected ${itemName}?`;
        }
    }

    /**
     * Get display name for item type
     * @param {string} itemType - Item type
     * @param {boolean} plural - Whether to use plural form
     * @returns {string} - Display name
     */
    getItemDisplayName(itemType, plural = false) {
        const displayNames = {
            animals: plural ? 'animals' : 'animal',
            dairy: plural ? 'dairy animals' : 'dairy animal',
            newborn: plural ? 'newborn animals' : 'newborn animal',
            fattening: plural ? 'fattening animals' : 'fattening animal',
            feeds: plural ? 'feeds' : 'feed',
            vaccinations: plural ? 'vaccinations' : 'vaccination',
            ingredients: plural ? 'ingredients' : 'ingredient',
            users: plural ? 'users' : 'user',
            records: plural ? 'records' : 'record'
        };
        return displayNames[itemType] || (plural ? 'items' : 'item');
    }

    /**
     * Get identifier for an item (code, name, etc.)
     * @param {Object} item - Item object
     * @param {string} itemType - Item type
     * @returns {string} - Item identifier
     */
    getItemIdentifier(item, itemType) {
        // Try different common identifier fields
        return item.code || item.name || item.id || item.title || 'Unknown';
    }

    /**
     * Create the popup HTML structure
     */
    createPopup() {
        // Remove existing popup if any
        this.removePopup();

        // Create popup container
        this.popupElement = document.createElement('div');
        this.popupElement.className = 'delete-popup-container';
        this.popupElement.innerHTML = `
            <div class="container">
                <div class="overlay">
                    <div class="delete-popup">
                        <img src="icons/delete.png" alt="Delete">
                        <div class="statement">${this.currentDeleteData.message}</div>
                        <div class="warning-text">This action cannot be undone.</div>
                        <div class="click">
                            <button class="cancel-btn">
                                <div class="txt">Cancel</div>
                            </button>
                            <button class="delete-btn">
                                <div class="txt">Delete</div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add styles
        this.addPopupStyles();

        // Add event listeners
        this.setupEventListeners();

        // Add to document
        document.body.appendChild(this.popupElement);
    }

    /**
     * Add CSS styles for the popup
     */
    addPopupStyles() {
        if (!document.querySelector('#delete-popup-styles')) {
            const style = document.createElement('style');
            style.id = 'delete-popup-styles';
            style.textContent = `
                .delete-popup-container {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 10000;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                .delete-popup-container .container {
                    width: 100%;
                    height: 100%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }

                .delete-popup-container .overlay {
                    position: relative;
                }

                .delete-popup-container .delete-popup {
                    padding: 20px;
                    position: relative;
                    width: 400px;
                    min-height: 280px;
                    background-color: #fff;
                    border-radius: 12px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                    animation: popupSlideIn 0.3s ease-out;
                }

                @keyframes popupSlideIn {
                    from {
                        transform: scale(0.8);
                        opacity: 0;
                    }
                    to {
                        transform: scale(1);
                        opacity: 1;
                    }
                }

                .delete-popup-container .delete-popup img {
                    margin-top: 10px;
                    width: 55px;
                    height: 55px;
                }

                .delete-popup-container .statement {
                    margin-top: 15px;
                    font-size: 24px;
                    font-weight: bold;
                    color: #333;
                    text-align: center;
                    line-height: 1.3;
                }

                .delete-popup-container .warning-text {
                    margin-top: 8px;
                    font-size: 14px;
                    color: #666;
                    text-align: center;
                }

                .delete-popup-container .click {
                    margin-top: 25px;
                    width: 100%;
                    display: flex;
                    flex-direction: row;
                    justify-content: space-around;
                    padding: 0 20px;
                    gap: 15px;
                }

                .delete-popup-container .delete-popup button {
                    flex: 1;
                    height: 45px;
                    border-radius: 8px;
                    border: none;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: bold;
                    transition: all 0.2s ease;
                }

                .delete-popup-container .cancel-btn {
                    background-color: #aedf32;
                    color: white;
                    border: 2px solid #8fb728;
                }

                .delete-popup-container .cancel-btn:hover {
                    background-color: #8fb728;
                    transform: translateY(-1px);
                }

                .delete-popup-container .delete-btn {
                    background-color: #aedf32;
                    color: white;
                    border: 2px solid #8fb728;
                }

                .delete-popup-container .delete-btn:hover {
                    background-color: #8fb728;
                    transform: translateY(-1px);
                }

                .delete-popup-container .delete-btn:active,
                .delete-popup-container .cancel-btn:active {
                    transform: translateY(0);
                }

                .delete-popup-container .txt {
                    color: inherit;
                    font-size: 16px;
                    font-weight: bold;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Setup event listeners for the popup
     */
    setupEventListeners() {
        if (!this.popupElement) return;

        const cancelBtn = this.popupElement.querySelector('.cancel-btn');
        const deleteBtn = this.popupElement.querySelector('.delete-btn');
        const overlay = this.popupElement.querySelector('.container');

        // Cancel button
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => this.handleCancel());
        }

        // Delete button
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => this.handleConfirm());
        }

        // Click outside to cancel
        if (overlay) {
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.handleCancel();
                }
            });
        }

        // Escape key to cancel
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }

    /**
     * Handle keyboard events
     * @param {KeyboardEvent} e - Keyboard event
     */
    handleKeyDown(e) {
        if (!this.isVisible) return;

        if (e.key === 'Escape') {
            this.handleCancel();
        } else if (e.key === 'Enter') {
            this.handleConfirm();
        }
    }

    /**
     * Handle cancel action
     */
    handleCancel() {
        console.log('🚫 Delete cancelled');

        // Call cancel callback if provided
        if (this.onCancelCallback && typeof this.onCancelCallback === 'function') {
            this.onCancelCallback();
        }

        this.hidePopup();
    }

    /**
     * Handle confirm delete action
     */
    async handleConfirm() {
        if (!this.currentDeleteData) {
            console.error('No delete data available');
            return;
        }

        console.log('🗑️ Delete confirmed for:', this.currentDeleteData);

        // Disable buttons to prevent double-click
        this.setButtonsEnabled(false);

        try {
            // Call confirm callback if provided
            if (this.onConfirmCallback && typeof this.onConfirmCallback === 'function') {
                const result = await this.onConfirmCallback(this.currentDeleteData.items, this.currentDeleteData.itemType);

                if (result !== false) {
                    // Show success notification
                    this.showNotification(
                        `Successfully deleted ${this.currentDeleteData.items.length} ${this.getItemDisplayName(this.currentDeleteData.itemType, this.currentDeleteData.items.length > 1)}`,
                        'success'
                    );
                }
            }
        } catch (error) {
            console.error('Error during delete operation:', error);
            this.showNotification(`Failed to delete items: ${error.message}`, 'error');

            // Re-enable buttons on error
            this.setButtonsEnabled(true);
            return;
        }

        this.hidePopup();
    }

    /**
     * Enable or disable popup buttons
     * @param {boolean} enabled - Whether buttons should be enabled
     */
    setButtonsEnabled(enabled) {
        if (!this.popupElement) return;

        const buttons = this.popupElement.querySelectorAll('button');
        buttons.forEach(button => {
            button.disabled = !enabled;
            button.style.opacity = enabled ? '1' : '0.6';
            button.style.cursor = enabled ? 'pointer' : 'not-allowed';
        });
    }

    /**
     * Show the popup
     */
    showPopup() {
        if (this.popupElement) {
            this.popupElement.style.display = 'flex';
            this.isVisible = true;

            // Focus on cancel button by default
            setTimeout(() => {
                const cancelBtn = this.popupElement.querySelector('.cancel-btn');
                if (cancelBtn) {
                    cancelBtn.focus();
                }
            }, 100);
        }
    }

    /**
     * Hide the popup
     */
    hidePopup() {
        if (this.popupElement) {
            this.popupElement.style.display = 'none';
            this.isVisible = false;

            // Remove keyboard event listener
            document.removeEventListener('keydown', this.handleKeyDown.bind(this));

            // Clean up after a short delay to allow animations
            setTimeout(() => {
                this.removePopup();
            }, 300);
        }
    }

    /**
     * Remove popup from DOM
     */
    removePopup() {
        if (this.popupElement && this.popupElement.parentNode) {
            this.popupElement.parentNode.removeChild(this.popupElement);
            this.popupElement = null;
            this.isVisible = false;
            this.currentDeleteData = null;
            this.onConfirmCallback = null;
            this.onCancelCallback = null;
        }
    }

    /**
     * Show notification message
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, warning, info)
     * @param {number} duration - Auto-dismiss duration in milliseconds
     */
    showNotification(message, type = 'info', duration = 4000) {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.delete-notification');
        existingNotifications.forEach(notification => notification.remove());

        const notification = document.createElement('div');
        notification.className = 'delete-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10001;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            animation: slideInRight 0.3s ease-out;
        `;

        // Set colors based on type
        const colors = {
            success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
            error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
            warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
            info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
        };

        const color = colors[type] || colors.info;
        notification.style.backgroundColor = color.bg;
        notification.style.border = `1px solid ${color.border}`;
        notification.style.color = color.text;

        notification.textContent = message;

        // Add close button
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            float: right;
            margin-left: 10px;
            cursor: pointer;
            font-size: 18px;
            line-height: 1;
        `;
        closeBtn.onclick = () => notification.remove();
        notification.appendChild(closeBtn);

        document.body.appendChild(notification);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }

        // Add animation styles if not already present
        if (!document.querySelector('#delete-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'delete-notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
    }
}

// ==================== SPECIALIZED DELETE HANDLERS ====================

/**
 * Animal Delete Handler
 * Specialized handler for deleting animals from all relevant tables
 */
class AnimalDeleteHandler {
    constructor() {
        this.storageKeys = {
            animals: 'animals',
            dairy: 'dairyAnimals',
            newborn: 'newbornAnimals',
            fatteningWeights: 'fatteningWeights',
            milkProduction: 'milkProduction'
        };
    }

    /**
     * Delete animals from all relevant tables
     * @param {Array} animals - Animals to delete
     * @returns {Promise<boolean>} - Success status
     */
    async deleteAnimals(animals) {
        try {
            console.log('🗑️ Deleting animals:', animals.map(a => a.code || a.id));

            // Get all storage data
            const mainAnimals = this.getData('animals');
            const dairyAnimals = this.getData('dairy');
            const newbornAnimals = this.getData('newborn');
            const fatteningWeights = this.getData('fatteningWeights');
            const milkProduction = this.getData('milkProduction');

            // Extract animal IDs and codes for deletion
            const animalIds = animals.map(a => a.id).filter(id => id);
            const animalCodes = animals.map(a => a.code).filter(code => code);

            // Delete from main animals table
            const updatedMainAnimals = mainAnimals.filter(animal =>
                !animalIds.includes(animal.id) && !animalCodes.includes(animal.code)
            );

            // Delete from dairy animals table
            const updatedDairyAnimals = dairyAnimals.filter(animal =>
                !animalIds.includes(animal.id) && !animalCodes.includes(animal.code) && !animalCodes.includes(animal.animalCode)
            );

            // Delete from newborn animals table
            const updatedNewbornAnimals = newbornAnimals.filter(animal =>
                !animalIds.includes(animal.id) && !animalCodes.includes(animal.code) && !animalCodes.includes(animal.animalCode)
            );

            // Delete from fattening weights table
            const updatedFatteningWeights = fatteningWeights.filter(record =>
                !animalCodes.includes(record.animalCode)
            );

            // Delete from milk production table
            const updatedMilkProduction = milkProduction.filter(record =>
                !animalCodes.includes(record.animalCode)
            );

            // Save all updated data
            this.saveData('animals', updatedMainAnimals);
            this.saveData('dairy', updatedDairyAnimals);
            this.saveData('newborn', updatedNewbornAnimals);
            this.saveData('fatteningWeights', updatedFatteningWeights);
            this.saveData('milkProduction', updatedMilkProduction);

            console.log(`✅ Successfully deleted ${animals.length} animals from all tables`);

            // Trigger storage events for other pages to update
            window.dispatchEvent(new StorageEvent('storage', {
                key: 'animals',
                newValue: JSON.stringify(updatedMainAnimals)
            }));

            return true;
        } catch (error) {
            console.error('❌ Error deleting animals:', error);
            throw error;
        }
    }

    /**
     * Get data from localStorage
     * @param {string} key - Storage key
     * @returns {Array} - Data array
     */
    getData(key) {
        try {
            const data = localStorage.getItem(this.storageKeys[key] || key);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`Error getting data for key ${key}:`, error);
            return [];
        }
    }

    /**
     * Save data to localStorage
     * @param {string} key - Storage key
     * @param {Array} data - Data to save
     */
    saveData(key, data) {
        try {
            localStorage.setItem(this.storageKeys[key] || key, JSON.stringify(data));
        } catch (error) {
            console.error(`Error saving data for key ${key}:`, error);
            throw error;
        }
    }
}

/**
 * Generic Item Delete Handler
 * Handler for deleting generic items from a single storage location
 */
class GenericDeleteHandler {
    constructor(storageKey) {
        this.storageKey = storageKey;
    }

    /**
     * Delete items from storage
     * @param {Array} items - Items to delete
     * @returns {Promise<boolean>} - Success status
     */
    async deleteItems(items) {
        try {
            console.log(`🗑️ Deleting ${items.length} items from ${this.storageKey}`);

            const allItems = this.getData();
            const itemIds = items.map(item => item.id).filter(id => id);
            const itemCodes = items.map(item => item.code).filter(code => code);

            // Filter out items to delete
            const updatedItems = allItems.filter(item =>
                !itemIds.includes(item.id) && !itemCodes.includes(item.code)
            );

            this.saveData(updatedItems);

            console.log(`✅ Successfully deleted ${items.length} items from ${this.storageKey}`);

            // Trigger storage event
            window.dispatchEvent(new StorageEvent('storage', {
                key: this.storageKey,
                newValue: JSON.stringify(updatedItems)
            }));

            return true;
        } catch (error) {
            console.error(`❌ Error deleting items from ${this.storageKey}:`, error);
            throw error;
        }
    }

    /**
     * Get data from localStorage
     * @returns {Array} - Data array
     */
    getData() {
        try {
            const data = localStorage.getItem(this.storageKey);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`Error getting data for ${this.storageKey}:`, error);
            return [];
        }
    }

    /**
     * Save data to localStorage
     * @param {Array} data - Data to save
     */
    saveData(data) {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(data));
        } catch (error) {
            console.error(`Error saving data for ${this.storageKey}:`, error);
            throw error;
        }
    }
}

// ==================== GLOBAL DELETE POPUP INSTANCE ====================

// Create global instance
window.deletePopupManager = new DeletePopupManager();

// Create specialized handlers
window.animalDeleteHandler = new AnimalDeleteHandler();

// ==================== UTILITY FUNCTIONS ====================

/**
 * Show delete confirmation for animals
 * @param {Array} animals - Animals to delete
 * @param {Function} onSuccess - Success callback (optional)
 * @param {Function} onCancel - Cancel callback (optional)
 */
window.showDeleteAnimalsConfirmation = function(animals, onSuccess = null, onCancel = null) {
    if (!animals || animals.length === 0) {
        console.warn('No animals provided for deletion');
        return;
    }

    window.deletePopupManager.showDeleteConfirmation({
        items: animals,
        itemType: 'animals',
        onConfirm: async (items) => {
            try {
                await window.animalDeleteHandler.deleteAnimals(items);
                if (onSuccess && typeof onSuccess === 'function') {
                    onSuccess(items);
                }
                return true;
            } catch (error) {
                console.error('Failed to delete animals:', error);
                throw error;
            }
        },
        onCancel: () => {
            if (onCancel && typeof onCancel === 'function') {
                onCancel();
            }
        }
    });
};

/**
 * Show delete confirmation for dairy animals
 * @param {Array} dairyAnimals - Dairy animals to delete
 * @param {Function} onSuccess - Success callback (optional)
 * @param {Function} onCancel - Cancel callback (optional)
 */
window.showDeleteDairyAnimalsConfirmation = function(dairyAnimals, onSuccess = null, onCancel = null) {
    window.deletePopupManager.showDeleteConfirmation({
        items: dairyAnimals,
        itemType: 'dairy',
        onConfirm: async (items) => {
            try {
                await window.animalDeleteHandler.deleteAnimals(items);
                if (onSuccess && typeof onSuccess === 'function') {
                    onSuccess(items);
                }
                return true;
            } catch (error) {
                console.error('Failed to delete dairy animals:', error);
                throw error;
            }
        },
        onCancel: onCancel
    });
};

/**
 * Show delete confirmation for feeds
 * @param {Array} feeds - Feeds to delete
 * @param {Function} onSuccess - Success callback (optional)
 * @param {Function} onCancel - Cancel callback (optional)
 */
window.showDeleteFeedsConfirmation = function(feeds, onSuccess = null, onCancel = null) {
    const feedHandler = new GenericDeleteHandler('feeds');

    window.deletePopupManager.showDeleteConfirmation({
        items: feeds,
        itemType: 'feeds',
        onConfirm: async (items) => {
            try {
                await feedHandler.deleteItems(items);
                if (onSuccess && typeof onSuccess === 'function') {
                    onSuccess(items);
                }
                return true;
            } catch (error) {
                console.error('Failed to delete feeds:', error);
                throw error;
            }
        },
        onCancel: onCancel
    });
};

/**
 * Show delete confirmation for vaccinations
 * @param {Array} vaccinations - Vaccinations to delete
 * @param {Function} onSuccess - Success callback (optional)
 * @param {Function} onCancel - Cancel callback (optional)
 */
window.showDeleteVaccinationsConfirmation = function(vaccinations, onSuccess = null, onCancel = null) {
    const vaccinationHandler = new GenericDeleteHandler('vaccinations');

    window.deletePopupManager.showDeleteConfirmation({
        items: vaccinations,
        itemType: 'vaccinations',
        onConfirm: async (items) => {
            try {
                await vaccinationHandler.deleteItems(items);
                if (onSuccess && typeof onSuccess === 'function') {
                    onSuccess(items);
                }
                return true;
            } catch (error) {
                console.error('Failed to delete vaccinations:', error);
                throw error;
            }
        },
        onCancel: onCancel
    });
};

/**
 * Show delete confirmation for generic items
 * @param {Array} items - Items to delete
 * @param {string} itemType - Type of items
 * @param {string} storageKey - Storage key for the items
 * @param {Function} onSuccess - Success callback (optional)
 * @param {Function} onCancel - Cancel callback (optional)
 */
window.showDeleteItemsConfirmation = function(items, itemType, storageKey, onSuccess = null, onCancel = null) {
    const genericHandler = new GenericDeleteHandler(storageKey);

    window.deletePopupManager.showDeleteConfirmation({
        items: items,
        itemType: itemType,
        onConfirm: async (items) => {
            try {
                await genericHandler.deleteItems(items);
                if (onSuccess && typeof onSuccess === 'function') {
                    onSuccess(items);
                }
                return true;
            } catch (error) {
                console.error(`Failed to delete ${itemType}:`, error);
                throw error;
            }
        },
        onCancel: onCancel
    });
};

/**
 * Helper function to get selected items from checkboxes
 * @param {string} checkboxSelector - CSS selector for checkboxes
 * @param {Array} allItems - Array of all items to match against
 * @returns {Array} - Selected items
 */
window.getSelectedItemsFromCheckboxes = function(checkboxSelector = '.checkbox:checked', allItems = []) {
    const selectedCheckboxes = document.querySelectorAll(checkboxSelector);
    const selectedItems = [];

    selectedCheckboxes.forEach(checkbox => {
        // Try different methods to identify the item
        let itemIdentifier = null;

        // Method 1: data-code attribute
        if (checkbox.hasAttribute('data-code')) {
            itemIdentifier = checkbox.getAttribute('data-code');
        }
        // Method 2: data-id attribute
        else if (checkbox.hasAttribute('data-id')) {
            itemIdentifier = checkbox.getAttribute('data-id');
        }
        // Method 3: next sibling text content (common pattern)
        else if (checkbox.nextSibling && checkbox.nextSibling.textContent) {
            itemIdentifier = checkbox.nextSibling.textContent.trim();
        }
        // Method 4: parent row data
        else {
            const row = checkbox.closest('tr');
            if (row) {
                const cells = row.querySelectorAll('td');
                if (cells.length > 1) {
                    itemIdentifier = cells[1].textContent.trim(); // Usually the code/name is in the second column
                }
            }
        }

        if (itemIdentifier) {
            // Find the corresponding item in allItems array
            const item = allItems.find(item =>
                item.code === itemIdentifier ||
                item.id === itemIdentifier ||
                item.name === itemIdentifier
            );

            if (item) {
                selectedItems.push(item);
            } else {
                // If not found in allItems, create a basic item object
                selectedItems.push({
                    code: itemIdentifier,
                    id: itemIdentifier
                });
            }
        }
    });

    return selectedItems;
};

/**
 * Helper function to delete selected animals from current page
 * This function can be called from any animal page (dairy, newborn, etc.)
 */
window.deleteSelectedAnimalsFromPage = function() {
    // Get all animals from localStorage (you might need to adjust this based on your data structure)
    const allAnimals = JSON.parse(localStorage.getItem('animals') || '[]');
    const selectedAnimals = window.getSelectedItemsFromCheckboxes('.checkbox:checked', allAnimals);

    if (selectedAnimals.length === 0) {
        window.deletePopupManager.showNotification('Please select at least one animal to delete', 'warning');
        return;
    }

    window.showDeleteAnimalsConfirmation(selectedAnimals, () => {
        // Reload the page or refresh the table after successful deletion
        if (typeof loadAnimals === 'function') {
            loadAnimals();
        } else if (typeof loadDairyAnimals === 'function') {
            loadDairyAnimals();
        } else if (typeof loadNewbornAnimals === 'function') {
            loadNewbornAnimals();
        } else {
            // Fallback: reload the page
            window.location.reload();
        }
    });
};

/**
 * Helper function to delete selected feeds from current page
 */
window.deleteSelectedFeedsFromPage = function() {
    const allFeeds = JSON.parse(localStorage.getItem('feeds') || '[]');
    const selectedFeeds = window.getSelectedItemsFromCheckboxes('.checkbox:checked', allFeeds);

    if (selectedFeeds.length === 0) {
        window.deletePopupManager.showNotification('Please select at least one feed to delete', 'warning');
        return;
    }

    window.showDeleteFeedsConfirmation(selectedFeeds, () => {
        // Reload feeds
        if (typeof loadFeeds === 'function') {
            loadFeeds();
        } else {
            window.location.reload();
        }
    });
};

/**
 * Helper function to delete selected vaccinations from current page
 */
window.deleteSelectedVaccinationsFromPage = function() {
    const allVaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');
    const selectedVaccinations = window.getSelectedItemsFromCheckboxes('.checkbox:checked', allVaccinations);

    if (selectedVaccinations.length === 0) {
        window.deletePopupManager.showNotification('Please select at least one vaccination to delete', 'warning');
        return;
    }

    window.showDeleteVaccinationsConfirmation(selectedVaccinations, () => {
        // Reload vaccinations
        if (typeof loadVaccinations === 'function') {
            loadVaccinations();
        } else {
            window.location.reload();
        }
    });
};

// ==================== INITIALIZATION ====================

/**
 * Initialize delete popup system when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🗑️ Delete popup system initialized');

    // Auto-attach delete functionality to common delete buttons
    const deleteButtons = document.querySelectorAll('[class*="delete"], [id*="delete"], [class*="Delete"], [id*="Delete"]');

    deleteButtons.forEach(button => {
        // Skip if already has event listener
        if (button.hasAttribute('data-delete-attached')) {
            return;
        }

        button.setAttribute('data-delete-attached', 'true');

        button.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();

            // Determine what type of items to delete based on page context
            const currentPage = window.location.pathname.toLowerCase();

            if (currentPage.includes('dairy')) {
                window.deleteSelectedAnimalsFromPage();
            } else if (currentPage.includes('newborn')) {
                window.deleteSelectedAnimalsFromPage();
            } else if (currentPage.includes('animal')) {
                window.deleteSelectedAnimalsFromPage();
            } else if (currentPage.includes('feed')) {
                window.deleteSelectedFeedsFromPage();
            } else if (currentPage.includes('vaccination')) {
                window.deleteSelectedVaccinationsFromPage();
            } else {
                // Generic fallback
                console.log('Delete button clicked, but page type not recognized');
                window.deletePopupManager.showNotification('Please select items to delete', 'info');
            }
        });
    });
});

// ==================== DEBUGGING FUNCTIONS ====================

/**
 * Debug function to test delete popup
 */
window.testDeletePopup = function() {
    const testItems = [
        { code: 'TEST001', name: 'Test Item 1' },
        { code: 'TEST002', name: 'Test Item 2' }
    ];

    window.deletePopupManager.showDeleteConfirmation({
        items: testItems,
        itemType: 'test items',
        onConfirm: async (items) => {
            console.log('Test delete confirmed:', items);
            return true;
        },
        onCancel: () => {
            console.log('Test delete cancelled');
        }
    });
};

/**
 * Debug function to show delete popup status
 */
window.debugDeletePopup = function() {
    console.log('=== Delete Popup Debug Info ===');
    console.log('Delete popup manager:', window.deletePopupManager);
    console.log('Animal delete handler:', window.animalDeleteHandler);
    console.log('Is popup visible:', window.deletePopupManager.isVisible);
    console.log('Current delete data:', window.deletePopupManager.currentDeleteData);
    console.log('=== End Debug Info ===');
};

console.log('✅ Delete popup confirmation system loaded successfully');
console.log('Available functions:');
console.log('- window.showDeleteAnimalsConfirmation(animals, onSuccess, onCancel)');
console.log('- window.showDeleteDairyAnimalsConfirmation(dairyAnimals, onSuccess, onCancel)');
console.log('- window.showDeleteFeedsConfirmation(feeds, onSuccess, onCancel)');
console.log('- window.showDeleteVaccinationsConfirmation(vaccinations, onSuccess, onCancel)');
console.log('- window.deleteSelectedAnimalsFromPage()');
console.log('- window.deleteSelectedFeedsFromPage()');
console.log('- window.deleteSelectedVaccinationsFromPage()');
console.log('- window.testDeletePopup() - for testing');
console.log('- window.debugDeletePopup() - for debugging');