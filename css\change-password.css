/* --------------------------------------------------------------------------------- */
.edit-profile {
  position: relative;
  font-weight: 400;
  font-size:23px;
}
.edit-profile1 {
  position: relative;
  font-weight: 400;
  font-size:23px;
  padding:2%;
}

.services-icon1 {
  height: 45px;
  width: 40px;
  position: relative;
  object-fit:contain;
}
.services-parent {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.settings2-inner {
  width:13%;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 17px;
  box-sizing: border-box;
}
.profile-separator {
  align-self: stretch;
  height: 1px;
  position: relative;
  border-top: 1px solid rgba(0, 0, 0, 0.7);
  box-sizing: border-box;
}
.edit-profile-wrapper {
  width:100%;
  height:100%;
  position:relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: var(--padding-3xs);
  box-sizing: border-box;
  /* max-width: 100%; */
}
.current-pass{
  width: 100%;
  border: 0;
  outline: 0;
  font-family: var(--highlights);
  font-size: 16px;
  background-color: transparent;
  position: relative;
  color: var(--color-black);
  text-align: left;
  display: inline-block;
  /* min-width: 115px; */
  padding: 0;
}
.data-filled {
  align-self: stretch;
  height:20%;
  border-radius: 20px;
  background-color: #fff;
  border: 1px solid var(--color-darkslategray-200);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 1%;
}
.new-pass,
.password-input {
  width: 100%;
  border: 0;
  outline: 0;
  font-size:16px;
  background-color: transparent;
  color: var(--color-black);
  text-align: left;
  display: inline-block;
}
.new-pass {
  font-family: var(--highlights);
  position: relative;
  /* min-width: 213px; */
  padding: 0;
}
.password-input {
  font-family: var(--font-rem);
}
.password-input {
  position: relative;
}

.data-filled-parent {
  align-self: stretch;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 10px;
}
.data-filled-group {
  height: 100%;
  width: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  /* max-width: 100%; */
}
.frame-container,
.user-data-fields-wrapper {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  /* max-width: 100%; */
}
.user-data-fields-wrapper {
 height:65%;
 width:60%;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 0 1%;
  font-size:20px;
  color: #000;
}
.frame-container {
  align-self: stretch;
  height: 100%;
  flex-direction: column;
  justify-content: space-around;
  /* padding: var(--padding-5xs) 0 632px; */
}
.double-left-icon {
  height: 40px;
  width: 40px;
  position: relative;
  object-fit: cover;
}
.back-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
}
.save-icon {
  width: 30px;
  position: relative;
  height: 30px;
  object-fit: cover;
}
.save1 {
  position: relative;
  font-size: 19px;
  font-weight: 400;
  font-family: var(--highlights);
  color: var(--color-white);
  text-align: left;
}
.frame-group,
.navigation-buttons,
.save {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.save {
  cursor: pointer;
  border: 0;
  padding: var(--padding-3xs) 0;
  background-color: var(--color-limegreen-100);
  height: 55px;
  width: 290px;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: var(--br-xl);
  justify-content: center;
  box-sizing: border-box;
  gap:10px;
}
.save:hover {
  background-color: #1ab31a;
}
.navigation-buttons {
  align-self: stretch;
  justify-content: space-between;
  padding:0 6%;
 
}

.frame-group {
  flex-direction: column;
  align-items: flex-start;
  width:100%;
   align-self: stretch;
  justify-content: space-between;
  /* min-height: 1049px;
  max-width: 100%; */
}
.profile-separator-parent {
  width:100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;
   max-width: 100%; 
   height:95%;
   text-align: center;
}
.settings21 {
   
   position: absolute;
  width: 75%;
  height: 87%;
  top: 11%;
  left: 21%;
  display: flex;
  align-self: stretch;
  flex-direction: column;
  /* padding: 0 25px 0; */
  box-sizing: border-box;
  max-width: 100%;
  border-radius: 16px;
  background-color:#fff;
  align-items: flex-start;
  justify-content: flex-start;
   gap: 2%;
   padding: 2%;
}
.change-password1 {  
  
   box-sizing: border-box;
  width: 100%;
  height: 100vh;
   position: absolute; 
  background-color: #e3e4e4;
  overflow: hidden;
  flex-direction: row;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  font-family: var(--highlights);
 
}
/* إضافة media queries للتوافق مع مختلف أحجام الشاشات */
@media screen and (max-width: 1350px) {
  .change-password1 {
    flex-wrap: wrap;
  }
}

@media screen and (max-width: 1200px) {
  .frame-container,
  .settings21 {
    padding-top: var(--padding-xl);
    padding-bottom: 75px;
    box-sizing: border-box;
  }
  
  .edit-inner {
    max-width: 100%;
    padding: 4px 20px 0;
  }
  
  .user-data-fields-wrapper {
    width: 100%;
    max-width: 800px;
  }
}

@media screen and (max-width: 1050px) {
  .edit-profile,
  .ingredients {
    font-size: var(--font-size-7xl);
  }
  
  .data-filled-group {
    width: 100%;
    max-width: 570px;
  }
  
  .user-data-fields-wrapper {
    padding: 0 var(--padding-xl);
  }
}

@media screen and (max-width: 800px) {
  .side-bar {
    width: 250px;
  }
  
  .navbar {
    width: 100%;
  }
  
  .frame-group {
    gap: 15px;
  }
  
  .data-filled {
    width: 100%;
  }
  
  .data-filled-group {
    height: auto;
    gap: 20px;
  }
}

@media screen and (max-width: 600px) {
  .change-password1 {
    flex-direction: column;
  }
  
  .side-bar {
    width: 100%;
    border-radius: 0;
    margin-bottom: 10px;
  }
  
  .edit-inner {
    max-width: 100%;
    padding: 0 10px;
  }
  
  .navigation-buttons {
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }
  
  .save {
    width: 100%;
    max-width: 200px;
  }
  
  .content-header-parent {
    padding: 0 5px;
  }
}

@media screen and (max-width: 450px) {
  .edit-profile,
  .ingredients {
    font-size: var(--font-size-lgi);
  }
  
  .services-parent {
    flex-wrap: wrap;
  }
  
  .current-pass,
  .new-pass,
  .password-input {
    font-size: 18px;
  }
  
  .data-filled-group {
    height: auto;
    gap: 15px;
  }
  
  .data-filled {
    padding: var(--padding-5xs) var(--padding-3xs);
    height: 45px;
  }
  
  .frame-container {
    gap: 19px;
  }
  
  .back-left {
    padding-left: 5px;
  }
  
  .save {
    max-width: 150px;
    height: 45px;
  }
  
  .save1 {
    font-size: 18px;
  }
}

@media screen and (max-width: 350px) {
  .data-filled {
    padding: var(--padding-5xs) var(--padding-2xs);
    height: 40px;
  }
  
  .current-pass,
  .new-pass,
  .password-input {
    font-size: 16px;
  }
  
  .edit-profile {
    font-size: 18px;
  }
  
  .save {
    max-width: 120px;
    height: 40px;
  }
  
  .save1 {
    font-size: 16px;
  }
}
/* إعادة تصميم قائمة النافبار المنسدلة */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  z-index: 100;
}

.nav-profile1 {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 140px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 101;
}

.component-13, 
.component-131, 
.component-14 {
  align-self: stretch;
  height: 44px;
  border-radius: var(--br-7xs);
  background-color: var(--color-floralwhite);
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: var(--padding-2xs) var(--padding-3xs);
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.3s;
}

.component-13:hover, 
.component-131:hover, 
.component-14:hover {
  background-color: #f0f0f0;
}

.log-out, 
.log-out1, 
.log-out2 {
  width: auto;
  position: relative;
  font-weight: 500;
  display: inline-block;
}

.popup-overlay a {
  text-decoration: none;
  color: black;
}

/* إضافة مؤشر للعنصر القابل للنقر */
#navProfileContainer {
  cursor: pointer;
}



