/**
 * Vaccination Popup Manager
 * Handles popup functionality for the vaccination page
 */
class VaccinationPopupManager {
    constructor() {
        this.popupContainer = null;
        this.isPopupVisible = false;
    }

    /**
     * Show popup with specified content
     * @param {string} popupType - Type of popup (addnewvaccination, updatevaccination, etc.)
     * @param {Object} options - Popup options
     */
    async showPopup(popupType, options = {}) {
        try {
            console.log(`🔄 Loading ${popupType} popup for vaccination management...`);
            this.hidePopup();

            // Prevent body scrolling and scroll to top
            document.body.classList.add('popup-open');
            window.scrollTo(0, 0);

            this.createPopupContainer();
            await this.loadPopupWithIframe(popupType, options);

            // Show popup with animation
            this.popupContainer.classList.add('visible');
            this.isPopupVisible = true;

            // Ensure popup is at the top of viewport
            this.popupContainer.scrollTop = 0;

            console.log(`✅ Vaccination popup "${popupType}" displayed successfully`);
        } catch (error) {
            console.error(`❌ Failed to show vaccination popup "${popupType}":`, error);
            this.hidePopup();
        }
    }

    async loadPopupWithIframe(popupType, options = {}) {
        const htmlFiles = {
            'addnewvaccination': 'addnewvaccination.html',
            'updatevaccination': 'updatevaccination.html'
        };

        const htmlFile = htmlFiles[popupType];
        if (!htmlFile) {
            throw new Error(`Unknown popup type: ${popupType}`);
        }

        return new Promise((resolve, reject) => {
            const popupBody = this.popupContainer.querySelector('.vaccination-popup-body');
            
            const iframe = document.createElement('iframe');
            iframe.className = 'vaccination-popup-iframe';
            iframe.src = htmlFile;
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.border = 'none';
            iframe.onload = () => resolve();
            iframe.onerror = (e) => reject(new Error(`Failed to load ${htmlFile}`));
            
            popupBody.innerHTML = '';
            popupBody.appendChild(iframe);
        });
    }

    hidePopup() {
        if (this.popupContainer) {
            this.popupContainer.classList.remove('visible');
            setTimeout(() => {
                this.removePopupContainer();
                document.body.classList.remove('popup-open');
                this.isPopupVisible = false;
            }, 300); // Match transition duration
        }
    }

    createPopupContainer() {
        this.removePopupContainer();

        this.popupContainer = document.createElement('div');
        this.popupContainer.className = 'vaccination-popup-overlay';
        this.popupContainer.innerHTML = `
            <div class="vaccination-popup-container">
                <div class="vaccination-popup-body">
                    <!-- Popup content will be loaded here -->
                </div>
            </div>
        `;

        this.addPopupStyles();
        this.setupPopupEventListeners();
        this.addExternalCloseButton();
        document.body.appendChild(this.popupContainer);
    }

    addExternalCloseButton() {
        const externalCloseButton = document.createElement('button');
        externalCloseButton.className = 'external-close-button';
        externalCloseButton.innerHTML = '×';
        externalCloseButton.setAttribute('type', 'button');
        externalCloseButton.addEventListener('click', () => this.hidePopup());
        this.popupContainer.querySelector('.vaccination-popup-container').appendChild(externalCloseButton);
    }

    removePopupContainer() {
        if (this.popupContainer && this.popupContainer.parentNode) {
            this.popupContainer.parentNode.removeChild(this.popupContainer);
        }
        this.popupContainer = null;
    }

    addPopupStyles() {
        // Only add styles if they don't already exist
        if (!document.getElementById('vaccination-popup-styles')) {
            const styleElement = document.createElement('style');
            styleElement.id = 'vaccination-popup-styles';
            styleElement.textContent = `
               .vaccination-popup-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.2);
                    z-index: 1000;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    opacity: 0;
                    visibility: hidden;
                    transition: opacity 0.3s, visibility 0.3s;
                }
                
                .vaccination-popup-overlay.visible {
                    opacity: 1;
                    visibility: visible;
                }
                
                .vaccination-popup-container {
                    // background-color: white;
                    border-radius: 8px;
                    //width: 90%;
                    width:100%;
                    height:100%;
                    left:0%;
                    max-width: 800px;
                    max-height: 90vh;
                    overflow: hidden;
                    position: relative;
                    // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                }
                
                .vaccination-popup-body {
                    width: 100%;
                    height: 100%;
                    max-height: 90vh;
                  //  overflow: auto;
                    overflow: hidden;
                }
                
                .external-close-button {
                    //position: absolute;
                    // top: 10px;
                    // right: 10px;
                    // top: 0px;
                    // left: 0px;
                    // background: none;
                    // border: none;
                    // font-size: 30px;//24
                    // cursor: pointer;
                    // color: #333;
                    // z-index: 1001;
                    position: fixed !important;
                    top: 20px !important;
                    right: 20px !important;
                    width: 50px !important;
                    height: 50px !important;
                    background-color: rgb(174, 223, 50);
                    color: rgb(255, 255, 255) !important;
                    border: 3px solid rgb(255, 255, 255) !important;
                    border-radius: 50% !important;
                    font-size: 30px !important;
                    font-weight: bold !important;
                    cursor: pointer !important;
                    z-index: 999999 !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    font-family: Arial, sans-serif !important;
                    text-align: center !important;
                    padding: 0px !important;
                    margin: 0px !important;
                    line-height: 1 !important;
                    box-shadow: rgba(0, 0, 0, 0.5) 0px 4px 15px;
                    transition: 0.2s !important;
                    transform: scale(1);

                }
                
                body.popup-open {
                    overflow: hidden;
                }
            `;
            document.head.appendChild(styleElement);
        }
    }

    setupPopupEventListeners() {
        // Close when clicking outside the popup
        this.popupContainer.addEventListener('click', (e) => {
            if (e.target === this.popupContainer) {
                this.hidePopup();
            }
        });

        // Close on escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isPopupVisible) {
                this.hidePopup();
            }
        });
    }
}