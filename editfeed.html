<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />

    <link rel="stylesheet" href="css/editfeed.css" />
   

</head>

<body>

    <div class="makenewfeed">
           <div class="parent1">
                        <div class="frame-5">
                            <img class="update" src="icons/feed.png">

                            <div class="text-wrapper-5"> Edit Exist Feed</div>
                        </div>

                        <div class="frame-4">
                              <div class="row">
                                 <div class="frame-7">                                <div class="text-wrapper-6">Feed Name</div>
                                <input class="data-filled" id="feedName" placeholder="feed name"></input>
                            </div>
                              </div>  
                            <div class="row">
                            

                             <div class="frame-7">
                                <div class="text-wrapper-6">Growth rate</div>
                                <input class="data-filled" id="growthRate" placeholder="Growth rate"></input>
                            </div>

                            <div class="frame-7">
                                <div class="text-wrapper-6">Weight</div>
                                <input class="data-filled" id="weight" placeholder="Weight"></input>
                            </div>                            
                            <div class="frame-7">
                                <div class="text-wrapper-6">Gender</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn" id="genderBtn">Select Gender</button>
                                    <div class="dropdown-content" id="genderDropdown">
                                        <a href="#" data-value="male">Male</a>
                                        <a href="#" data-value="female">Female</a>
                                    </div>
                                </div>
                            </div>
                            </div>

                            <div class="row">                            <div class="frame-7">
                                <div class="text-wrapper-6">Animal's Type</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn" id="animalTypeBtn">Select Animal Type</button>
                                    <div class="dropdown-content" id="animalTypeDropdown">
                                        <a href="#" data-value="newborn">Newborn</a>
                                        <a href="#" data-value="dairy">Dairy</a>
                                        <a href="#" data-value="fattening">Fattening</a>
                                    </div>
                                </div>
                            </div>                             <div class="frame-7">
                                <div class="text-wrapper-6">Feed Type</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn" id="feedTypeBtn">Select Feed Type</button>
                                    <div class="dropdown-content" id="feedTypeDropdown">
                                        <a href="#" data-value="tmr">TMR</a>
                                        <a href="#" data-value="cfm">CFM</a>
                                    </div>
                                </div>
                            </div>
                              <div class="frame-7">
                                <div class="text-wrapper-6">Season</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn" id="seasonBtn">Select Season</button>
                                    <div class="dropdown-content" id="seasonDropdown">
                                        <a href="#" data-value="summer">Summer</a>
                                        <a href="#" data-value="winter">Winter</a>
                                    </div>
                                </div>
                            </div>
                            </div>

                           
                                <div class="row">
                            <div class="frame-7">
                                <div class="text-wrapper-6"> Protein percentage</div>
                                <div class="data-filled1"></div><!--it showes the persentage found in feed-->
                            </div>
                            <div class="frame-7">
                                <div class="text-wrapper-6">TDN percentage</div>
                                <div class="data-filled1" ></div><!--it showes the persentage found in feed-->
                            </div>
                        </div>

                        </div>                        <div class="tablecontainer">
                            <div class="row">                            <div class="frame-7">
                              
                                <div class="dropdown">
                                    <button class="data-filled dropbtn" id="ingredientTypeBtn">Select Ingredient Type</button>
                                    <div class="dropdown-content" id="ingredientTypeDropdown">
                                        <a href="#" data-value="concentrates">Concentrates</a>
                                        <a href="#" data-value="roughages">Roughages</a>
                                        <a href="#" data-value="millByProduct">Mill by Product</a>
                                        <a href="#" data-value="oilseedByProduct">Oilseed by Product</a>
                                        <a href="#" data-value="forages">Forages</a>
                                    </div>
                                </div>
                            </div>
                            <div class="frame-7">
                                <div class="text-wrapper-6">Ingredients</div>
                                <div class="dropdown">
                                    <button class="data-filled dropbtn" id="ingredientsDropdown">Select Ingredient</button>
                                    <div class="dropdown-content" id="ingredientsList">
                                        <!-- Will be populated dynamically -->
                                    </div>
                                </div>
                            </div>                            <div class="frame-7">
                                <div class="text-wrapper-6">Price per kg</div>
                                <input class="data-filled" id="pricePerKg" placeholder="price per kg">
                            </div>
                            <div class="frame-7">
                                <div class="text-wrapper-6">Weight (kg)</div>
                                <input class="data-filled" id="ingredientWeight" placeholder="weight">
                            </div>
                            <div class="data-filled11"> 
                                <img class="img" src="icons/addingred.png" id="addIngredientBtn">
                            </div>
                         </div>
                            <table>
                                <thead>
                                    <tr>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                  <!--  <tr>
                                        <td><div class="data-filled">ingrediant</td>
                                        <td><div class="frame-7"><div class="text-wrapper-6">Price per kg</div>
                                            <input class="data-filled" placeholder="price per kg"></input></div></td>
                                        <td><div class="frame-7"><div class="text-wrapper-6"></div><div class="data-filled" >weightit showes the weight of each ingred</div></div></td>
                                        
                                        <td><img class="img" src="icons/delingred.png"></td>
                                    </tr>-->
                                </tbody>
                            </table>
                        </div>

                         <div class="frame-8">
                           <div class="frame-5" ><img class="update" src="icons/ingrediant icon.png">
                            <button class="btntext1">Finish Choose Ingredients</button>
                        </div>
                        <button id="updateIngredientsBtn" class="frame-9" title="Update data at ingredients page">

                            <div class="btntext">Save Feed</div>
                        </button>


                    </div>

                    </div>

    </div>
   <script src="js/editfeed.js"></script>
</body>

</html>