document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const openGmailBtn = document.querySelector('.component');
    const resendLink = document.querySelector('.lastline span');
    
    // Get email from sessionStorage
    const email = sessionStorage.getItem('resetPasswordEmail');
    
    // If no email in session, redirect to forgetpassbyemail.html
    if (!email) {
        alert('Please enter your email first');
        window.location.href = 'forgetpassbyemail.html';
        return;
    }
    
    // Update instruction text with user's email
    updateEmailText(email);
    
    // Handle "Open Gmail" button click
    openGmailBtn.addEventListener('click', function() {
        // Check if email is Gmail
        if (email.toLowerCase().includes('gmail.com')) {
            window.open('https://mail.google.com', '_blank');
        } else if (email.toLowerCase().includes('yahoo.com')) {
            window.open('https://mail.yahoo.com', '_blank');
        } else if (email.toLowerCase().includes('outlook.com') || email.toLowerCase().includes('hotmail.com')) {
            window.open('https://outlook.live.com', '_blank');
        } else {
            // Generic email provider
            window.open('https://mail.google.com', '_blank');
        }
    });
    
    // Handle "send it again" link click
    resendLink.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Show loading animation
        resendLink.textContent = 'sending...';
        resendLink.style.color = '#999';
        resendLink.style.cursor = 'default';
        
        // Simulate resending email
        setTimeout(function() {
            resendLink.textContent = 'send it again';
            resendLink.style.color = '#32cd32';
            resendLink.style.cursor = 'pointer';
            
            // Show success message
            alert('Verification email resent successfully!');
        }, 2000);
    });
    
    // Add "Back to Login" link
    addBackToLoginLink();
    
    // Add "Continue to Reset Password" button (for demo purposes)
    addContinueButton();
    
    // Function to update email text
    function updateEmailText(email) {
        const instructionText = document.querySelector('.instructions');
        if (instructionText) {
            instructionText.textContent = `We have sent you an Email to reset password at ${maskEmail(email)}`;
        }
    }
    
    // Function to mask email for privacy
    function maskEmail(email) {
        const parts = email.split('@');
        if (parts.length !== 2) return email;
        
        const name = parts[0];
        const domain = parts[1];
        
        // Show first 2 characters and last character of the name part
        const maskedName = name.length <= 3 
            ? name 
            : name.substring(0, 2) + '***' + name.substring(name.length - 1);
        
        return maskedName + '@' + domain;
    }
    
    // Function to add "Back to Login" link
    function addBackToLoginLink() {
        const container = document.querySelector('.frame-3');
        
        // Create link element
        const backLink = document.createElement('a');
        backLink.href = 'login.html';
        backLink.textContent = 'Back to Login';
        backLink.style.display = 'block';
        backLink.style.textAlign = 'center';
        backLink.style.marginTop = '20px';
        backLink.style.color = '#32cd32';
        backLink.style.textDecoration = 'none';
        backLink.style.fontFamily = 'Roboto-Medium, Helvetica';
        backLink.style.fontSize = '16px';
        
        // Add hover effect
        backLink.addEventListener('mouseover', function() {
            this.style.textDecoration = 'underline';
        });
        
        backLink.addEventListener('mouseout', function() {
            this.style.textDecoration = 'none';
        });
        
        // Append to container
        container.appendChild(backLink);
    }
    
    // Function to add "Continue to Reset Password" button (for demo purposes)
    function addContinueButton() {
        const container = document.querySelector('.dta-entered');
        
        // Create button element
        const continueButton = document.createElement('button');
        continueButton.className = 'component';
        continueButton.style.marginTop = '20px';
        continueButton.style.backgroundColor = '#4CAF50';
        
        const buttonText = document.createElement('div');
        buttonText.className = 'cancel';
        buttonText.textContent = 'Continue to Reset Password';
        
        continueButton.appendChild(buttonText);
        
        // Add click event
        continueButton.addEventListener('click', function() {
            window.location.href = 'forgetpass.html';
        });
        
        // Append to container
        container.appendChild(continueButton);
    }
    
    // Optional: Add countdown timer for auto-redirect
    addCountdownTimer();
    
    // Function to add countdown timer
    function addCountdownTimer() {
        const container = document.querySelector('.dta-entered');
        
        // Create timer element
        const timerElement = document.createElement('div');
        timerElement.style.textAlign = 'center';
        timerElement.style.marginTop = '15px';
        timerElement.style.color = '#666';
        timerElement.style.fontSize = '14px';
        
        container.appendChild(timerElement);
        
        // Set countdown time (in seconds)
        let timeLeft = 60;
        
        // Update timer every second
        const timerId = setInterval(function() {
            timeLeft--;
            
            if (timeLeft <= 0) {
                clearInterval(timerId);
                // Redirect to password reset page
                window.location.href = 'forgetpass.html';
            } else {
                timerElement.textContent = `Automatically continuing in ${timeLeft} seconds...`;
            }
        }, 1000);
    }
});