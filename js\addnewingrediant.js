function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.getElementById('notification-container').appendChild(notification);
    
    // Remove notification after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'fadeOut 0.5s forwards';
        setTimeout(() => {
            notification.remove();
        }, 500);
    }, 3000);
}

// Function to check if ingredient already exists
function checkDuplicateIngredient(name) {
    const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
    return ingredients.some(ing => ing.name.toLowerCase() === name.toLowerCase());
}

// Function to save ingredient to localStorage
function saveIngredientToStorage(ingredientData) {
    try {
        const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
        ingredients.push(ingredientData);
        localStorage.setItem('ingredients', JSON.stringify(ingredients));
    } catch (error) {
        console.error('Error saving to localStorage:', error);
        throw new Error('Failed to save ingredient data');
    }
}

document.addEventListener('DOMContentLoaded', () => {
    // Get form elements by ID for reliability
    const nameInput = document.getElementById('ingredientName');
    const proteinInput = document.getElementById('ingredientProtein');
    const cfInput = document.getElementById('ingredientCF');
    const tdnInput = document.getElementById('ingredientTDN');
    const meInput = document.getElementById('ingredientME');
    const saveButton = document.getElementById('saveIngredientBtn');
    const ingredientTypeBtn = document.getElementById('ingredientTypeBtn');
    
    // Function to validate numeric input
    function validateNumericInput(value, fieldName) {
        const num = parseFloat(value);
        if (isNaN(num) || num < 0) {
            showNotification(`Please enter a valid positive number for ${fieldName}`, 'error');
            return false;
        }
        return true;
    }

    // Handle ingredient type selection
    document.querySelectorAll('#ingredientTypeDropdown a').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            ingredientTypeBtn.textContent = this.textContent;
            ingredientTypeBtn.classList.remove('error');
            console.log('Selected ingredient type:', this.textContent);
        });
    });

    // Add event listener for save button
    saveButton.addEventListener('click', async () => {
        try {
            // Disable button and show loading state
            saveButton.disabled = true;
            saveButton.innerHTML = `
                <img class="update" src="icons/save.png" style="opacity: 0.7">
                <div class="btntext">Saving...</div>
            `;

            // Get all the values
            const name = nameInput.value.trim();
            const type = ingredientTypeBtn.textContent;
            const proteins = proteinInput.value.trim();
            const crudeFiber = cfInput.value.trim();
            const tdn = tdnInput.value.trim();
            const me = meInput.value.trim();

            // Validate required fields
            if (!name || type === 'Select Ingredient Type' || !proteins || !crudeFiber || !tdn || !me) {
                showNotification('Please fill in all fields', 'error');
                return;
            }

            // Validate numeric inputs
            if (!validateNumericInput(proteins, 'proteins') ||
                !validateNumericInput(crudeFiber, 'crude fiber') ||
                !validateNumericInput(tdn, 'TDN') ||
                !validateNumericInput(me, 'ME')) {
                return;
            }

            // Check for duplicate ingredient
            if (checkDuplicateIngredient(name)) {
                showNotification('An ingredient with this name already exists', 'error');
                nameInput.focus();
                return;
            }

            // Create ingredient object with validated data
            const ingredientData = {
                name: name,
                type: type,
                proteins: parseFloat(proteins),
                crudeFiber: parseFloat(crudeFiber),
                tdn: parseFloat(tdn),
                me: parseFloat(me),
                createdAt: new Date().toISOString()
            };

            // Save to localStorage
            await saveIngredientToStorage(ingredientData);
            
            // Show success message and handle redirect
            showNotification('Ingredient saved successfully!', 'success');
            setTimeout(() => {
                clearForm();
                window.location.href = 'ingrediants.html';
            }, 2000);

        } catch (error) {
            console.error('Error saving ingredient:', error);
            showNotification('Failed to save ingredient. Please try again.', 'error');
        } finally {
            // Reset button state
            saveButton.disabled = false;
            saveButton.innerHTML = `
                <img class="update" src="icons/save.png">
                <div class="btntext">Save Ingredient</div>
            `;
        }
    });

    function clearForm() {
        // Clear all input fields
        nameInput.value = '';
        proteinInput.value = '';
        cfInput.value = '';
        tdnInput.value = '';
        meInput.value = '';
        
        // Reset dropdown to default
        if (ingredientTypeBtn) {
            ingredientTypeBtn.textContent = 'Select Ingredient Type';
        }
    }
});