/**
 * UpdateMilchData popup class
 * This file contains the implementation of the Update Dairy Data popup
 */

class UpdateMilchDataPopup extends Popup {
    constructor() {
        super('choose-update-milch'); // Use the CSS class from updatemilchdata.css
        this.animalData = {};
    }

    // Create the popup content
    createPopupContent() {
        // Create the frame-4 div with proper styling
        const frame4 = document.createElement('div');

        // Apply common styles
        this.applyFrame4Styles(frame4);

        frame4.innerHTML = `
            <div class="frame-5">
                <img class="update" src="icons/update.png">
                <div class="text-wrapper-5">Updating Data</div>
            </div>

            <div class="frame-6">
                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Code</div>
                        <input class="data-filled">
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Type</div>
                        <form class="frame-111">
                            <div class="raddiv">
                                <input type="radio" name="access" id="newborn" class="radio">
                                <label for="newborn">Newborn</label>
                            </div>
                            <div class="raddiv">
                                <input type="radio" name="access" id="dairy" class="radio">
                                <label for="dairy">Dairy</label>
                            </div>
                            <div class="raddiv">
                                <input type="radio" name="access" id="fattening" class="radio">
                                <label for="fattening">Fattening</label>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Date of Birth</div>
                        <input class="data-filled" type="date">
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Type</div>
                        <form class="frame-11">
                            <div class="raddiv">
                                <input type="radio" name="access" id="milch" class="radio">
                                <label for="milch">Dairy</label>
                            </div>
                            <div class="raddiv">
                                <input type="radio" name="access" id="drying" class="radio">
                                <label for="drying">Drying</label>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Made Artificial Insemination</div>
                        <form class="frame-11">
                            <div class="raddiv">
                                <input type="radio" name="access" id="yes" class="radio">
                                <label for="yes">Yes</label>
                            </div>
                            <div class="raddiv">
                                <input type="radio" name="access" id="no" class="radio">
                                <label for="no">No</label>
                            </div>
                        </form>
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Weight</div>
                        <input class="data-filled">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Date of Artificial Insemination</div>
                        <input class="data-filled" type="date">
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Date of weight</div>
                        <input class="data-filled" type="date">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Status of Insemination</div>
                        <form class="frame-12">
                            <div class="raddiv">
                                <input type="radio" name="access" id="pregnant" class="radio">
                                <label for="pregnant">Pregnant</label>
                            </div>
                            <div class="raddiv">
                                <input type="radio" name="access" id="make-insemination-again" class="radio">
                                <label for="make-insemination-again">Make Insemination again</label>
                            </div>
                        </form>
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Herd Number</div>
                        <input class="data-filled">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Health care notes</div>
                        <input class="textarea">
                    </div>

                    <div class="frame-7">
                        <div class="text-wrapper-6">Expected Date of Calfing</div>
                        <input class="data-filled" type="date">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Taken Vaccination</div>
                        <input class="textarea">
                    </div>
                    <div class="frame-7">
                        <button class="frame-79">
                            <img class="update" src="icons/save.png">
                            <div class="btntext">Save Update</div>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add close button (X mark)
        this.addCloseButton(frame4);

        return frame4;
    }

    setupEventListeners() {
        // Get save button
        const saveButton = this.popupContainer.querySelector('.frame-79');
        if (saveButton) {
            saveButton.addEventListener('click', () => this.saveUpdate());
        }

        // Get radio buttons
        const typeRadios = this.popupContainer.querySelectorAll('input[name="access"]');
        typeRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                this.animalData.type = radio.id;

                // If user selects something other than dairy, switch to updatenewborn
                if (radio.id !== 'dairy' && radio.id !== 'milch' && radio.id !== 'drying' &&
                    radio.id !== 'yes' && radio.id !== 'no' && radio.id !== 'pregnant' &&
                    radio.id !== 'make-insemination-again') {
                    this.closePopup();
                    const newbornPopup = new UpdateNewbornPopup();
                    newbornPopup.showPopup();
                }
            });
        });

        // Get input fields
        const inputs = this.popupContainer.querySelectorAll('input.data-filled, input.textarea');
        inputs.forEach(input => {
            input.addEventListener('change', (e) => {
                const label = e.target.closest('.frame-7').querySelector('.text-wrapper-6').textContent;
                const fieldName = this.getLabelFieldName(label);
                this.animalData[fieldName] = e.target.value;
            });
        });
    }

    // Convert label text to camelCase field name
    getLabelFieldName(label) {
        const labelMap = {
            'Code': 'code',
            'Date of Birth': 'dateOfBirth',
            'Weight': 'weight',
            'Date of weight': 'dateOfWeight',
            'Herd Number': 'herdNumber',
            'Health care notes': 'healthcareNotes',
            'Expected Date of Calfing': 'expectedDateOfCalfing',
            'Taken Vaccination': 'takenVaccination',
            'Date of Artificial Insemination': 'dateOfArtificialInsemination'
        };

        return labelMap[label] || label.toLowerCase().replace(/\s(.)/g, ($1) => $1.toUpperCase()).replace(/\s/g, '');
    }

    // Save update data
    saveUpdate() {
        // Validate required fields
        if (!this.animalData.code) {
            alert('Please enter the animal code');
            return;
        }

        // Update animal in table
        AnimalTableManager.updateAnimal(this.animalData);

        alert('Animal data updated successfully!');
        this.closePopup();
    }
}
