/**
 * ChooseUpdateMilch popup class
 * This file contains the implementation of the Choose Update Dairy popup
 */

class ChooseUpdateMilchPopup extends Popup {
    constructor() {
        super('choose-update-milch'); // Use the CSS class from chooseupdatemilch.css
        this.codeInput = null;
    }

    // Create the popup content
    createPopupContent() {
        // Create the frame-4 div with proper styling
        const frame4 = document.createElement('div');

        // Apply common styles
        this.applyFrame4Styles(frame4);

        frame4.innerHTML = `
            <div class="frame-5">
                <img class="update" src="icons/update.png">
                <div class="text-wrapper-5">Updating Data</div>
            </div>

            <div class="frame-6">
                <div class="frame-7">
                    <div class="text-wrapper-6">Code</div>
                    <input class="data-filled">
                </div>

                <div class="frame-8">
                    <img class="double-left" src="icons/doblleft.png">
                    <button class="frame-9">
                        <div class="btntext">Go to update</div>
                    </button>
                </div>
            </div>
        `;

        // Add close button (X mark)
        this.addCloseButton(frame4);

        return frame4;
    }

    setupEventListeners() {
        // Get update button
        const updateButton = this.popupContainer.querySelector('.frame-9');
        if (updateButton) {
            updateButton.addEventListener('click', () => this.goToUpdate());
        }

        // Get code input
        this.codeInput = this.popupContainer.querySelector('.data-filled');
    }

    // Go to update page based on code
    goToUpdate() {
        const code = this.codeInput.value;
        if (!code) {
            alert('Please enter an animal code');
            return;
        }

        // Here you would typically check the animal type in a database
        // For now, we'll just show the dairy update popup
        this.closePopup();
        const updatePopup = new UpdateMilchDataPopup();
        updatePopup.showPopup();
    }
}
