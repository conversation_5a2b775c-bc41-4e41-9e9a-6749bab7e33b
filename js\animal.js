/**
 * Animal Page Controller - Complete Implementation
 * This file provides comprehensive functionality for the animal.html page including
 * button-to-popup integration, search, filter, and table management
 *
 * @author: Animal Management System
 * @version: 1.0.0
 * @description: Object-oriented animal page controller with popup integration
 */

// ==================== BASE CLASSES ====================

/**
 * Base Animal Data Manager for localStorage operations
 * Provides common functionality for animal data management
 */
class BaseAnimalDataManager {
    constructor() {
        this.storageKeys = {
            animals: 'animals',
            dairy: 'dairyAnimals',
            newborn: 'newbornAnimals',
            fatteningWeights: 'fatteningWeights',
            milkProduction: 'milkProduction'
        };
    }

    /**
     * Get data from localStorage with error handling
     * @param {string} key - Storage key
     * @returns {Array} - Array of data or empty array
     */
    getData(key) {
        try {
            const data = localStorage.getItem(this.storageKeys[key] || key);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`Error getting data for key ${key}:`, error);
            return [];
        }
    }

    /**
     * Save data to localStorage with error handling
     * @param {string} key - Storage key
     * @param {Array} data - Data to save
     * @returns {boolean} - Success status
     */
    saveData(key, data) {
        try {
            localStorage.setItem(this.storageKeys[key] || key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error(`Error saving data for key ${key}:`, error);
            return false;
        }
    }

    /**
     * Get all animals with complete data
     * @returns {Array} - Array of all animals
     */
    getAllAnimals() {
        return this.getData('animals');
    }

    /**
     * Search animals by various criteria
     * @param {string} searchTerm - Search term
     * @param {string} searchField - Field to search in (optional)
     * @returns {Array} - Matching animals
     */
    searchAnimals(searchTerm, searchField = null) {
        if (!searchTerm) {
            return this.getAllAnimals();
        }

        const animals = this.getAllAnimals();
        const term = searchTerm.toLowerCase();

        return animals.filter(animal => {
            if (searchField) {
                const fieldValue = animal[searchField];
                return fieldValue && fieldValue.toString().toLowerCase().includes(term);
            } else {
                // Search across multiple fields
                const searchableFields = ['code', 'type', 'herdNumber', 'gender', 'healthcareNotes'];
                return searchableFields.some(field => {
                    const fieldValue = animal[field];
                    return fieldValue && fieldValue.toString().toLowerCase().includes(term);
                });
            }
        });
    }

    /**
     * Filter animals by criteria
     * @param {Object} filters - Filter criteria
     * @returns {Array} - Filtered animals
     */
    filterAnimals(filters) {
        let animals = this.getAllAnimals();

        Object.entries(filters).forEach(([field, value]) => {
            if (value && value !== 'all') {
                animals = animals.filter(animal => {
                    const fieldValue = animal[field];
                    if (field === 'weight') {
                        // Handle weight range filtering
                        const weight = parseFloat(fieldValue);
                        if (value === 'light') return weight < 200;
                        if (value === 'medium') return weight >= 200 && weight < 500;
                        if (value === 'heavy') return weight >= 500;
                    } else {
                        return fieldValue && fieldValue.toString().toLowerCase() === value.toLowerCase();
                    }
                });
            }
        });

        return animals;
    }

    /**
     * Get animal statistics
     * @returns {Object} - Statistics object
     */
    getAnimalStatistics() {
        const animals = this.getAllAnimals();

        const stats = {
            total: animals.length,
            byType: {},
            byGender: {},
            averageWeight: 0
        };

        let totalWeight = 0;
        let weightCount = 0;

        animals.forEach(animal => {
            // Count by type
            const type = animal.type || 'unknown';
            stats.byType[type] = (stats.byType[type] || 0) + 1;

            // Count by gender
            const gender = animal.gender || 'unknown';
            stats.byGender[gender] = (stats.byGender[gender] || 0) + 1;

            // Calculate average weight
            if (animal.weight) {
                totalWeight += parseFloat(animal.weight);
                weightCount++;
            }
        });

        stats.averageWeight = weightCount > 0 ? Math.round((totalWeight / weightCount) * 100) / 100 : 0;

        return stats;
    }
}

/**
 * Popup Manager for handling all popup operations
 * Manages popup creation, display, and integration with existing systems
 */
class AnimalPopupManager {
    constructor() {
        this.currentPopup = null;
        this.popupContainer = null;
        this.isPopupVisible = false;
    }

    /**
     * Show popup with specified content
     * @param {string} popupType - Type of popup (addnewanimal, updatemilchdata, etc.)
     * @param {Object} options - Popup options
     */
    async showPopup(popupType, options = {}) {
        try {
            console.log(`🔄 Loading ${popupType} popup...`);

            // Hide any existing popup
            this.hidePopup();

            // Create popup container
            this.createPopupContainer();

            // Try multiple loading approaches
            let loadSuccess = false;

            // Method 1: Try iframe-based loading (most reliable for local files)
            try {
                await this.loadPopupWithIframe(popupType);
                loadSuccess = true;
                console.log(`✅ Popup "${popupType}" loaded via iframe`);
            } catch (iframeError) {
                console.warn(`⚠️ Iframe loading failed for ${popupType}:`, iframeError);
            }

            // Method 2: Try fetch-based loading (for server environments)
            if (!loadSuccess) {
                try {
                    await this.loadPopupFromHTML(popupType);
                    loadSuccess = true;
                    console.log(`✅ Popup "${popupType}" loaded via fetch`);
                } catch (fetchError) {
                    console.warn(`⚠️ Fetch loading failed for ${popupType}:`, fetchError);
                }
            }

            // Method 3: Fallback to programmatic content creation
            if (!loadSuccess) {
                try {
                    await this.loadPopupProgrammatically(popupType);
                    loadSuccess = true;
                    console.log(`✅ Popup "${popupType}" loaded programmatically`);
                } catch (programmaticError) {
                    console.warn(`⚠️ Programmatic loading failed for ${popupType}:`, programmaticError);
                }
            }

            if (!loadSuccess) {
                throw new Error(`All loading methods failed for ${popupType}`);
            }

            // Show popup with animation
            this.displayPopup();

            // Add close button functionality for frame-4 close button
            this.addFrame4CloseButtonListener();

            console.log(`✅ Popup "${popupType}" displayed successfully`);
        } catch (error) {
            console.error(`❌ Error showing popup "${popupType}":`, error);
            this.showNotification(`Failed to load ${popupType} popup: ${error.message}`, 'error');

            // Clean up failed popup
            this.hidePopup();
        }
    }

    /**
     * Load popup content from HTML file
     * @param {string} popupType - Type of popup
     */
    async loadPopupFromHTML(popupType) {
        const htmlFiles = {
            'addnewanimal': 'addnewanimal.html',
            'updatemilchdata': 'updatemilchdata.html',
            'updateanimal': 'updateanimal.html',
            'updatenewborn': 'updatenewborn.html',
            'updatefatteningweight': 'updatefatteningweight.html'
        };

        const htmlFile = htmlFiles[popupType];
        if (!htmlFile) {
            throw new Error(`Unknown popup type: ${popupType}`);
        }

        try {
            // Fetch the HTML content
            const response = await fetch(htmlFile);
            if (!response.ok) {
                throw new Error(`Failed to fetch ${htmlFile}: ${response.status}`);
            }

            const html = await response.text();

            // Parse the HTML
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // Extract the main content (choose-update-milch container)
            const mainContent = doc.querySelector('.choose-update-milch');
            if (!mainContent) {
                throw new Error(`Could not find .choose-update-milch in ${htmlFile}`);
            }

            // Get the popup body container
            const popupBody = this.popupContainer.querySelector('.animal-popup-body');
            if (!popupBody) {
                throw new Error('Popup body container not found');
            }

            // Insert the content
            popupBody.innerHTML = mainContent.outerHTML;

            // Update popup title
            this.updatePopupTitle(popupType);

            // Load and initialize the corresponding JavaScript
            await this.loadPopupScript(popupType);

            // Setup popup-specific functionality
            this.setupPopupFunctionality(popupType);

        } catch (error) {
            console.error(`Error loading popup content:`, error);
            throw error;
        }
    }

    /**
     * Load popup content using iframe (most reliable for local files)
     * @param {string} popupType - Type of popup
     */
    async loadPopupWithIframe(popupType) {
        const htmlFiles = {
            'addnewanimal': 'addnewanimal.html',
            'updatemilchdata': 'updatemilchdata.html',
            'updateanimal': 'updateanimal.html',
            'updatenewborn': 'updatenewborn.html',
            'updatefatteningweight': 'updatefatteningweight.html'
        };

        const htmlFile = htmlFiles[popupType];
        if (!htmlFile) {
            throw new Error(`Unknown popup type: ${popupType}`);
        }

        return new Promise((resolve, reject) => {
            // Get the popup body container
            const popupBody = this.popupContainer.querySelector('.animal-popup-body');
            if (!popupBody) {
                reject(new Error('Popup body container not found'));
                return;
            }

            // Create iframe
            const iframe = document.createElement('iframe');
            iframe.src = htmlFile;
            iframe.style.cssText = `
                width: 100%;
                height: 100%;
                border: none;
                background: transparent;
                margin: 0;
                padding: 0;
                overflow: hidden;
            `;
            iframe.setAttribute('frameborder', '0');
            iframe.setAttribute('scrolling', 'no');

            // Handle iframe load
            iframe.onload = () => {
                try {
                    // Update popup title
                    this.updatePopupTitle(popupType);

                    // Setup iframe communication
                    this.setupIframeIntegration(iframe, popupType);

                    // Inject CSS to hide gray background and show only white container
                    this.injectIframeStyles(iframe);

                    console.log(`✅ Iframe loaded successfully for ${popupType}`);
                    resolve();
                } catch (error) {
                    console.error(`Error setting up iframe for ${popupType}:`, error);
                    reject(error);
                }
            };

            iframe.onerror = () => {
                reject(new Error(`Failed to load iframe for ${htmlFile}`));
            };

            // Clear popup body and add iframe
            popupBody.innerHTML = '';
            popupBody.appendChild(iframe);

            // Set a timeout to prevent hanging
            setTimeout(() => {
                if (!iframe.contentDocument || iframe.contentDocument.readyState !== 'complete') {
                    reject(new Error(`Iframe loading timeout for ${htmlFile}`));
                }
            }, 5000);
        });
    }

    /**
     * Setup iframe integration for communication between popup and parent
     * @param {HTMLIFrameElement} iframe - The iframe element
     * @param {string} popupType - Type of popup
     */
    setupIframeIntegration(iframe, popupType) {
        try {
            // Add message listener for iframe communication
            const messageHandler = (event) => {
                if (event.data && event.data.type === 'popup-action') {
                    switch (event.data.action) {
                        case 'close':
                            this.hidePopup();
                            break;
                        case 'save-success':
                            this.showNotification('Data saved successfully!', 'success');
                            if (window.animalPageController) {
                                window.animalPageController.refreshAnimalTable();
                            }
                            setTimeout(() => this.hidePopup(), 1500);
                            break;
                        case 'save-error':
                            this.showNotification('Error saving data. Please try again.', 'error');
                            break;
                    }
                }
            };

            window.addEventListener('message', messageHandler);

            // Store the handler for cleanup
            iframe._messageHandler = messageHandler;

            // Try to inject communication script into iframe
            iframe.addEventListener('load', () => {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (iframeDoc) {
                        // Inject communication script
                        const script = iframeDoc.createElement('script');
                        script.textContent = `
                            // Communication functions for iframe
                            window.closePopup = function() {
                                parent.postMessage({type: 'popup-action', action: 'close'}, '*');
                            };

                            window.notifySuccess = function() {
                                parent.postMessage({type: 'popup-action', action: 'save-success'}, '*');
                            };

                            window.notifyError = function() {
                                parent.postMessage({type: 'popup-action', action: 'save-error'}, '*');
                            };

                            // Override existing save functions if they exist
                            if (window.addNewAnimal && window.addNewAnimal.handleSave) {
                                const originalSave = window.addNewAnimal.handleSave;
                                window.addNewAnimal.handleSave = function() {
                                    const result = originalSave.call(this);
                                    if (result !== false) {
                                        window.notifySuccess();
                                    } else {
                                        window.notifyError();
                                    }
                                    return result;
                                };
                            }
                        `;
                        iframeDoc.head.appendChild(script);
                    }
                } catch (crossOriginError) {
                    // Cross-origin restrictions prevent script injection
                    console.warn('Cannot inject script into iframe due to cross-origin restrictions');
                }
            });

        } catch (error) {
            console.warn('Error setting up iframe integration:', error);
        }
    }

    /**
     * Inject CSS into iframe to remove gray background and add functional close button
     * @param {HTMLIFrameElement} iframe - The iframe element
     */
    injectIframeStyles(iframe) {
        try {
            // Multiple injection attempts to handle timing issues
            const injectStyles = () => {
                try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                    if (!iframeDoc) {
                        console.warn('Cannot access iframe document');
                        return false;
                    }

                    // Check if styles already injected
                    if (iframeDoc.querySelector('#popup-enhancement-styles')) {
                        console.log('Styles already injected');
                        return true;
                    }

                    // Get popup type for specific styling
                    const popupType = this.popupContainer.getAttribute('data-popup-type') || 'default';

                    // Create and inject enhancement styles
                    const style = iframeDoc.createElement('style');
                    style.id = 'popup-enhancement-styles';
                    style.textContent = `
                        /* ULTRA AGGRESSIVE gray background removal */
                        *, *::before, *::after {
                            background-color: transparent !important;
                            background: transparent !important;
                            background-image: none !important;
                        }

                        html {
                            margin: 0 !important;
                            padding: 0 !important;
                            background: transparent !important;
                            background-color: transparent !important;
                            background-image: none !important;
                            overflow: hidden !important;
                        }

                        body {
                            margin: 0 !important;
                            padding: 0 !important;
                            background: transparent !important;
                            background-color: transparent !important;
                            background-image: none !important;
                            overflow: hidden !important;
                        }

                        /* FORCE OVERRIDE the original CSS with maximum specificity */
                        html body .choose-update-milch,
                        .choose-update-milch,
                        div.choose-update-milch {
                            background: transparent !important;
                            background-color: transparent !important;
                            background-image: none !important;
                            padding: 0 !important;
                            margin: 0 !important;
                            width: 100% !important;
                            height: 100% !important;
                            display: flex !important;
                            justify-content: center !important;
                            align-items: center !important;
                        }

                        /* Additional override for any nested containers */
                        .choose-update-milch * {
                            background-color: transparent !important;
                            background: transparent !important;
                            background-image: none !important;
                        }

                        /* Make frame-4 fill the entire iframe space with WHITE background */
                        .frame-4 {
                            position: relative !important;
                            width: 95% !important;
                            height: 95% !important;
                            max-width: none !important;
                            max-height: none !important;
                            min-width: none !important;
                            min-height: none !important;
                            top: auto !important;
                            left: auto !important;
                            transform: none !important;
                            margin: 0 !important;
                            padding: 20px !important;
                            box-sizing: border-box !important;
                            background: #ffffff !important;
                            background-color: #ffffff !important;
                            background-image: none !important;
                            border-radius: 16px !important;
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
                            display: flex !important;
                            flex-direction: column !important;
                            overflow-y: auto !important;
                        }

                        /* Ensure input fields and buttons have proper backgrounds */
                        .frame-4 input, .frame-4 textarea, .frame-4 select {
                            background: #ffffff !important;
                            background-color: #ffffff !important;
                            background-image: none !important;
                        }

                        .frame-4 button {
                            background: #28a745 !important;
                            background-color: #28a745 !important;
                            background-image: none !important;
                        }

                        /* Scale form elements proportionally */
                        .frame-4 .frame-5 {
                            flex-shrink: 0 !important;
                            margin-bottom: 20px !important;
                        }

                        .frame-4 .row {
                            flex-shrink: 0 !important;
                            margin-bottom: 15px !important;
                        }

                        .frame-4 .frame-7 {
                            flex: 1 !important;
                            min-width: 0 !important;
                        }

                        .frame-4 .data-filled,
                        .frame-4 .textarea {
                            width: 100% !important;
                            min-height: 40px !important;
                            font-size: 14px !important;
                            padding: 8px 12px !important;
                            box-sizing: border-box !important;
                        }

                        .frame-4 .text-wrapper-6 {
                            font-size: 14px !important;
                            margin-bottom: 8px !important;
                        }

                        .frame-4 .text-wrapper-5 {
                            font-size: 18px !important;
                            font-weight: bold !important;
                        }

                        /* Scale buttons appropriately */
                        .frame-4 .frame-9 {
                            min-height: 45px !important;
                            padding: 10px 20px !important;
                            font-size: 16px !important;
                        }

                        /* Ensure proper spacing for all content */
                        .frame-4 > * {
                            flex-shrink: 0 !important;
                        }

                        /* Universal Close Button for iframe popups - positioned within frame-4 */
                        .frame-4::before {
                            content: "×" !important;
                            position: absolute !important;
                            top: 20px !important;
                            right: 25px !important;
                            width: 40px !important;
                            height: 40px !important;
                            background-color: #f8f9fa !important;
                            color: #495057 !important;
                            border: 2px solid #dee2e6 !important;
                            border-radius: 50% !important;
                            display: flex !important;
                            align-items: center !important;
                            justify-content: center !important;
                            font-size: 28px !important;
                            font-weight: bold !important;
                            cursor: pointer !important;
                            z-index: 10005 !important;
                            transition: all 0.3s ease !important;
                            line-height: 1 !important;
                            user-select: none !important;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
                            font-family: Arial, sans-serif !important;
                        }

                        .frame-4:hover::before {
                            background-color: #e9ecef !important;
                            color: #212529 !important;
                            border-color: #adb5bd !important;
                            transform: scale(1.2) !important;
                            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25) !important;
                        }

                        /* NUCLEAR OPTION: Override the specific gray color from updateanimal.css */
                        .choose-update-milch[style*="#e3e4e4"],
                        .choose-update-milch[style*="rgb(227, 228, 228)"],
                        .choose-update-milch {
                            background-color: transparent !important;
                            background: transparent !important;
                        }

                        /* Ensure proper iframe sizing */
                        html, body {
                            width: 100% !important;
                            height: 100% !important;
                        }
                    `;

                    // Insert styles into head
                    if (iframeDoc.head) {
                        iframeDoc.head.appendChild(style);
                    } else {
                        // If head doesn't exist yet, append to document
                        iframeDoc.appendChild(style);
                    }

                    // Close button functionality removed - only external close button works

                    // AGGRESSIVE background removal for all popups
                    this.aggressiveBackgroundRemoval(iframeDoc);

                    // SPECIAL HANDLING for updateanimal popup - extra gray background removal
                    if (popupType === 'updateanimal') {
                        this.removeUpdateAnimalGrayBackground(iframeDoc);
                    }

                    console.log(`✅ Iframe styles injected successfully for ${popupType}`);
                    return true;

                } catch (error) {
                    console.warn('Error in style injection attempt:', error);
                    return false;
                }
            };

            // Attempt 1: Immediate injection (for already loaded iframes)
            if (injectStyles()) {
                return;
            }

            // Attempt 2: On load event
            iframe.addEventListener('load', () => {
                setTimeout(() => {
                    if (!injectStyles()) {
                        console.warn('Failed to inject styles on load event');
                    }
                }, 100); // Small delay to ensure DOM is ready
            });

            // Attempt 3: Polling for iframe readiness (fallback)
            let attempts = 0;
            const maxAttempts = 20;
            const pollInterval = setInterval(() => {
                attempts++;
                if (injectStyles() || attempts >= maxAttempts) {
                    clearInterval(pollInterval);
                    if (attempts >= maxAttempts) {
                        console.warn('Failed to inject iframe styles after maximum attempts');
                    }
                }
            }, 250);

        } catch (error) {
            console.warn('Error setting up iframe style injection:', error);
        }
    }

    /**
     * Special method to remove gray background from updateanimal popup
     * @param {Document} iframeDoc - The iframe document
     */
    removeUpdateAnimalGrayBackground(iframeDoc) {
        console.log('🎨 Removing gray background from updateanimal popup...');

        const removeGrayBackgrounds = () => {
            try {
                // Target specific elements that might have gray backgrounds in updateanimal.html
                const graySelectors = [
                    'html', 'body', '.container', '.wrapper', '.main', '.content', '.page',
                    '.background', '.bg', '.gray', '.grey',
                    '.choose-update-milch', 'div.choose-update-milch', 'html body .choose-update-milch',
                    '[style*="background-color: #e3e4e4"]', '[style*="background-color: gray"]', '[style*="background-color: grey"]',
                    '[style*="background: #e3e4e4"]', '[style*="background: gray"]', '[style*="background: grey"]',
                    '[style*="rgb(128"]', '[style*="rgb(169"]', '[style*="rgb(192"]',
                    '[style*="rgb(211"]', '[style*="rgb(220"]', '[style*="rgb(245"]',
                    '[style*="rgb(227, 228, 228)"]' // This is #e3e4e4 in RGB
                ];

                graySelectors.forEach(selector => {
                    try {
                        const elements = iframeDoc.querySelectorAll(selector);
                        elements.forEach(element => {
                            element.style.background = 'transparent';
                            element.style.backgroundColor = 'transparent';
                            element.style.backgroundImage = 'none';
                        });
                    } catch (e) {
                        // Ignore invalid selectors
                    }
                });

                // Force all elements to have transparent background except frame-4
                const allElements = iframeDoc.querySelectorAll('*:not(.frame-4)');
                allElements.forEach(element => {
                    if (!element.classList.contains('frame-4')) {
                        element.style.background = 'transparent';
                        element.style.backgroundColor = 'transparent';
                        element.style.backgroundImage = 'none';
                    }
                });

                // Ensure frame-4 has white background
                const frame4Elements = iframeDoc.querySelectorAll('.frame-4');
                frame4Elements.forEach(frame4 => {
                    frame4.style.background = '#ffffff';
                    frame4.style.backgroundColor = '#ffffff';
                    frame4.style.backgroundImage = 'none';
                });

                console.log('✅ UpdateAnimal gray background removal completed');
            } catch (error) {
                console.warn('Error in updateanimal background removal:', error);
            }
        };

        // Multiple attempts with delays
        removeGrayBackgrounds(); // Immediate
        setTimeout(removeGrayBackgrounds, 100);
        setTimeout(removeGrayBackgrounds, 300);
        setTimeout(removeGrayBackgrounds, 500);
        setTimeout(removeGrayBackgrounds, 1000);
        setTimeout(removeGrayBackgrounds, 2000);
    }

    // Iframe close button functionality removed - only external close button works

    /**
     * Load popup content programmatically (fallback method)
     * @param {string} popupType - Type of popup
     */
    async loadPopupProgrammatically(popupType) {
        const popupBody = this.popupContainer.querySelector('.animal-popup-body');
        if (!popupBody) {
            throw new Error('Popup body container not found');
        }

        // Update popup title
        this.updatePopupTitle(popupType);

        // Create content based on popup type
        let content;
        switch (popupType) {
            case 'addnewanimal':
                content = this.createAddNewAnimalContent();
                break;
            case 'updatemilchdata':
                content = this.createUpdateMilchDataContent();
                break;
            case 'updateanimal':
                content = this.createUpdateAnimalContent();
                break;
            case 'updatenewborn':
                content = this.createUpdateNewbornContent();
                break;
            case 'updatefatteningweight':
                content = this.createUpdateFatteningWeightContent();
                break;
            default:
                throw new Error(`Unknown popup type: ${popupType}`);
        }

        // Clear popup body and add content
        popupBody.innerHTML = '';
        popupBody.appendChild(content);

        // Setup event listeners for the programmatically created content
        this.setupProgrammaticEventListeners(popupType);

        console.log(`✅ Programmatic content created for ${popupType}`);
    }

    /**
     * Create Add New Animal content programmatically
     * @returns {HTMLElement} - The content element
     */
    createAddNewAnimalContent() {
        const container = document.createElement('div');
        container.className = 'choose-update-milch';
        container.innerHTML = `
            <div class="frame-4">
                <div class="frame-5">
                    <img class="update" src="icons/add.png" alt="Add">
                    <div class="text-wrapper-5">Add New Animal</div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Animal Code</div>
                        <input class="data-filled" type="text" id="animalCode" placeholder="Enter animal code">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Animal Type</div>
                        <select class="data-filled" id="animalType">
                            <option value="">Select Type</option>
                            <option value="dairy">Dairy</option>
                            <option value="newborn">Newborn</option>
                            <option value="fattening">Fattening</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Gender</div>
                        <select class="data-filled" id="gender">
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Herd Number</div>
                        <input class="data-filled" type="text" id="herdNumber" placeholder="Enter herd number">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Weight (kg)</div>
                        <input class="data-filled" type="number" id="weight" placeholder="Enter weight">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Date of Birth</div>
                        <input class="data-filled" type="date" id="dateOfBirth">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Healthcare Notes</div>
                        <input class="textarea" type="text" id="healthcareNotes" placeholder="Enter healthcare notes">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Taken Vaccination</div>
                        <input class="textarea" type="text" id="takenVaccination" placeholder="Enter vaccination details">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-8">
                        <button class="frame-9" id="saveAnimalBtn" title="Save animal data to the database">
                            <img class="update" src="icons/save.png" alt="Save">
                            <div class="btntext">Save Animal</div>
                        </button>
                    </div>
                </div>
            </div>
        `;
        return container;
    }

    /**
     * Create Update Milch Data content programmatically
     * @returns {HTMLElement} - The content element
     */
    createUpdateMilchDataContent() {
        const container = document.createElement('div');
        container.className = 'choose-update-milch';
        container.innerHTML = `
            <div class="frame-4">
                <div class="frame-5">
                    <img class="update" src="icons/milk-can.png" alt="Dairy">
                    <div class="text-wrapper-5">Update Dairy Data</div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Animal Code</div>
                        <input class="data-filled" type="text" id="animalCode" placeholder="Enter animal code">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Date</div>
                        <input class="data-filled" type="date" id="date">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Milk Production (L)</div>
                        <input class="data-filled" type="number" id="milkProduction" placeholder="Enter milk production">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Quality Grade</div>
                        <select class="data-filled" id="qualityGrade">
                            <option value="">Select Grade</option>
                            <option value="A">Grade A</option>
                            <option value="B">Grade B</option>
                            <option value="C">Grade C</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="frame-8">
                        <button class="frame-9" id="updateMilchBtn" title="Update dairy data">
                            <img class="update" src="icons/save.png" alt="Update">
                            <div class="btntext">Update Data</div>
                        </button>
                    </div>
                </div>
            </div>
        `;
        return container;
    }

    /**
     * Create Update Animal content programmatically
     * @returns {HTMLElement} - The content element
     */
    createUpdateAnimalContent() {
        const container = document.createElement('div');
        container.className = 'choose-update-milch';
        container.innerHTML = `
            <div class="frame-4">
                <div class="frame-5">
                    <img class="update" src="icons/cow-1.png" alt="Animal">
                    <div class="text-wrapper-5">Update Animal Data</div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Animal Code</div>
                        <input class="data-filled" type="text" id="animalCode" placeholder="Enter animal code">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Animal Type</div>
                        <select class="data-filled" id="animalType">
                            <option value="">Select Type</option>
                            <option value="dairy">Dairy</option>
                            <option value="newborn">Newborn</option>
                            <option value="fattening">Fattening</option>
                        </select>
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Gender</div>
                        <select class="data-filled" id="gender">
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Weight (kg)</div>
                        <input class="data-filled" type="number" id="weight" placeholder="Enter weight">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Herd Number</div>
                        <input class="data-filled" type="text" id="herdNumber" placeholder="Enter herd number">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Date of Birth</div>
                        <input class="data-filled" type="date" id="dateOfBirth">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-8">
                        <button class="frame-9" id="updateAnimalBtn" title="Update animal data">
                            <img class="update" src="icons/save.png" alt="Update">
                            <div class="btntext">Update Animal</div>
                        </button>
                    </div>
                </div>
            </div>
        `;
        return container;
    }

    /**
     * Create Update Newborn content programmatically
     * @returns {HTMLElement} - The content element
     */
    createUpdateNewbornContent() {
        const container = document.createElement('div');
        container.className = 'choose-update-milch';
        container.innerHTML = `
            <div class="frame-4">
                <div class="frame-5">
                    <img class="update" src="icons/cow-1.png" alt="Newborn">
                    <div class="text-wrapper-5">Update Newborn Data</div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Animal Code</div>
                        <input class="data-filled" type="text" id="animalCode" placeholder="Enter animal code">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Current Weight (kg)</div>
                        <input class="data-filled" type="number" id="currentWeight" placeholder="Enter current weight">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Health Status</div>
                        <select class="data-filled" id="healthStatus">
                            <option value="">Select Status</option>
                            <option value="healthy">Healthy</option>
                            <option value="sick">Sick</option>
                            <option value="recovering">Recovering</option>
                        </select>
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Date</div>
                        <input class="data-filled" type="date" id="date">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-8">
                        <button class="frame-9" id="updateNewbornBtn" title="Update newborn data">
                            <img class="update" src="icons/save.png" alt="Update">
                            <div class="btntext">Update Data</div>
                        </button>
                    </div>
                </div>
            </div>
        `;
        return container;
    }

    /**
     * Create Update Fattening Weight content programmatically
     * @returns {HTMLElement} - The content element
     */
    createUpdateFatteningWeightContent() {
        const container = document.createElement('div');
        container.className = 'choose-update-milch';
        container.innerHTML = `
            <div class="frame-4">
                <div class="frame-5">
                    <img class="update" src="icons/scale.png" alt="Fattening">
                    <div class="text-wrapper-5">Update Fattening Weight</div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Animal Code</div>
                        <input class="data-filled" type="text" id="animalCode" placeholder="Enter animal code">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Current Weight (kg)</div>
                        <input class="data-filled" type="number" id="currentWeight" placeholder="Enter current weight">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-7">
                        <div class="text-wrapper-6">Date of Weight</div>
                        <input class="data-filled" type="date" id="dateOfWeight">
                    </div>
                    <div class="frame-7">
                        <div class="text-wrapper-6">Feed Efficiency</div>
                        <input class="data-filled" type="number" id="feedEfficiency" placeholder="Enter feed efficiency" step="0.01">
                    </div>
                </div>

                <div class="row">
                    <div class="frame-8">
                        <button class="frame-9" id="updateFatteningBtn" title="Update fattening weight">
                            <img class="update" src="icons/save.png" alt="Update">
                            <div class="btntext">Update Weight</div>
                        </button>
                    </div>
                </div>
            </div>
        `;
        return container;
    }

    /**
     * Setup event listeners for programmatically created content
     * @param {string} popupType - Type of popup
     */
    setupProgrammaticEventListeners(popupType) {
        switch (popupType) {
            case 'addnewanimal':
                this.setupAddNewAnimalListeners();
                break;
            case 'updatemilchdata':
                this.setupUpdateMilchDataListeners();
                break;
            case 'updateanimal':
                this.setupUpdateAnimalListeners();
                break;
            case 'updatenewborn':
                this.setupUpdateNewbornListeners();
                break;
            case 'updatefatteningweight':
                this.setupUpdateFatteningWeightListeners();
                break;
        }
    }

    /**
     * Setup event listeners for Add New Animal form
     */
    setupAddNewAnimalListeners() {
        const saveBtn = this.popupContainer.querySelector('#saveAnimalBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.handleAddNewAnimalSave();
            });
        }
    }

    /**
     * Handle Add New Animal save
     */
    handleAddNewAnimalSave() {
        try {
            // Collect form data
            const formData = {
                code: this.popupContainer.querySelector('#animalCode')?.value || '',
                type: this.popupContainer.querySelector('#animalType')?.value || '',
                gender: this.popupContainer.querySelector('#gender')?.value || '',
                herdNumber: this.popupContainer.querySelector('#herdNumber')?.value || '',
                weight: this.popupContainer.querySelector('#weight')?.value || '',
                dateOfBirth: this.popupContainer.querySelector('#dateOfBirth')?.value || '',
                healthcareNotes: this.popupContainer.querySelector('#healthcareNotes')?.value || '',
                takenVaccination: this.popupContainer.querySelector('#takenVaccination')?.value || ''
            };

            // Validate required fields
            if (!formData.code || !formData.type || !formData.gender) {
                this.showNotification('Please fill in all required fields (Code, Type, Gender)', 'error');
                return;
            }

            // Add ID and timestamp
            formData.id = 'animal_' + Date.now();
            formData.dateOfWeight = new Date().toISOString().split('T')[0];

            // Save to localStorage
            const animals = JSON.parse(localStorage.getItem('animals') || '[]');
            animals.push(formData);
            localStorage.setItem('animals', JSON.stringify(animals));

            // Save to type-specific table if needed
            if (formData.type === 'dairy') {
                const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
                dairyAnimals.push(formData);
                localStorage.setItem('dairyAnimals', JSON.stringify(dairyAnimals));
            } else if (formData.type === 'newborn') {
                const newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
                newbornAnimals.push(formData);
                localStorage.setItem('newbornAnimals', JSON.stringify(newbornAnimals));
            }

            // Show success message
            this.showNotification('Animal added successfully!', 'success');

            // Refresh table
            if (window.animalPageController) {
                window.animalPageController.refreshAnimalTable();
            }

            // Close popup after delay
            setTimeout(() => this.hidePopup(), 1500);

        } catch (error) {
            console.error('Error saving animal:', error);
            this.showNotification('Error saving animal. Please try again.', 'error');
        }
    }

    /**
     * Setup event listeners for other popup types (simplified for now)
     */
    setupUpdateMilchDataListeners() {
        const updateBtn = this.popupContainer.querySelector('#updateMilchBtn');
        if (updateBtn) {
            updateBtn.addEventListener('click', () => {
                this.showNotification('Update Dairy Data functionality coming soon!', 'info');
                setTimeout(() => this.hidePopup(), 2000);
            });
        }
    }

    /**
     * Setup event listeners for Update Animal form
     */
    setupUpdateAnimalListeners() {
        const updateBtn = this.popupContainer.querySelector('#updateAnimalBtn');
        if (updateBtn) {
            updateBtn.addEventListener('click', () => {
                this.handleUpdateAnimalSave();
            });
        }
    }

    /**
     * Handle Update Animal save
     */
    handleUpdateAnimalSave() {
        try {
            // Collect form data
            const formData = {
                code: this.popupContainer.querySelector('#animalCode')?.value || '',
                type: this.popupContainer.querySelector('#animalType')?.value || '',
                gender: this.popupContainer.querySelector('#gender')?.value || '',
                weight: this.popupContainer.querySelector('#weight')?.value || '',
                herdNumber: this.popupContainer.querySelector('#herdNumber')?.value || '',
                dateOfBirth: this.popupContainer.querySelector('#dateOfBirth')?.value || ''
            };

            // Validate required fields
            if (!formData.code) {
                this.showNotification('Please enter an animal code', 'error');
                return;
            }

            // Find and update existing animal
            const animals = JSON.parse(localStorage.getItem('animals') || '[]');
            const animalIndex = animals.findIndex(animal => animal.code === formData.code);

            if (animalIndex === -1) {
                this.showNotification('Animal not found. Please check the animal code.', 'error');
                return;
            }

            // Update animal data
            animals[animalIndex] = { ...animals[animalIndex], ...formData };
            localStorage.setItem('animals', JSON.stringify(animals));

            // Update type-specific table if needed
            if (formData.type === 'dairy') {
                const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
                const dairyIndex = dairyAnimals.findIndex(animal => animal.code === formData.code);
                if (dairyIndex !== -1) {
                    dairyAnimals[dairyIndex] = { ...dairyAnimals[dairyIndex], ...formData };
                    localStorage.setItem('dairyAnimals', JSON.stringify(dairyAnimals));
                }
            } else if (formData.type === 'newborn') {
                const newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
                const newbornIndex = newbornAnimals.findIndex(animal => animal.code === formData.code);
                if (newbornIndex !== -1) {
                    newbornAnimals[newbornIndex] = { ...newbornAnimals[newbornIndex], ...formData };
                    localStorage.setItem('newbornAnimals', JSON.stringify(newbornAnimals));
                }
            }

            // Show success message
            this.showNotification('Animal updated successfully!', 'success');

            // Refresh table
            if (window.animalPageController) {
                window.animalPageController.refreshAnimalTable();
            }

            // Close popup after delay
            setTimeout(() => this.hidePopup(), 1500);

        } catch (error) {
            console.error('Error updating animal:', error);
            this.showNotification('Error updating animal. Please try again.', 'error');
        }
    }

    setupUpdateNewbornListeners() {
        const updateBtn = this.popupContainer.querySelector('#updateNewbornBtn');
        if (updateBtn) {
            updateBtn.addEventListener('click', () => {
                this.showNotification('Update Newborn Data functionality coming soon!', 'info');
                setTimeout(() => this.hidePopup(), 2000);
            });
        }
    }

    setupUpdateFatteningWeightListeners() {
        const updateBtn = this.popupContainer.querySelector('#updateFatteningBtn');
        if (updateBtn) {
            updateBtn.addEventListener('click', () => {
                this.showNotification('Update Fattening Weight functionality coming soon!', 'info');
                setTimeout(() => this.hidePopup(), 2000);
            });
        }
    }

    /**
     * Update popup title based on popup type
     * @param {string} popupType - Type of popup
     */
    updatePopupTitle(popupType) {
        const titles = {
            'addnewanimal': 'Add New Animal',
            'updatemilchdata': 'Update Dairy Data',
            'updateanimal': 'Update Animal Data',
            'updatenewborn': 'Update Newborn Data',
            'updatefatteningweight': 'Update Fattening Weight'
        };

        const titleElement = this.popupContainer.querySelector('.animal-popup-title');
        if (titleElement) {
            titleElement.textContent = titles[popupType] || 'Animal Management';
        }

        // Add popup type attribute for specific styling
        const popupBody = this.popupContainer.querySelector('.animal-popup-body');
        if (popupBody) {
            popupBody.setAttribute('data-popup-type', popupType);
        }
    }

    /**
     * Load the JavaScript file for the popup
     * @param {string} popupType - Type of popup
     */
    async loadPopupScript(popupType) {
        const scriptFiles = {
            'addnewanimal': 'js/addnewanimal.js',
            'updatemilchdata': 'js/updatemilchdata.js',
            'updateanimal': 'js/updateanimal.js',
            'updatenewborn': 'js/updatenewborn.js',
            'updatefatteningweight': 'js/updatefatteningweight.js'
        };

        const scriptFile = scriptFiles[popupType];
        if (!scriptFile) {
            console.warn(`No script file defined for ${popupType}`);
            return;
        }

        // Check if script is already loaded
        if (document.querySelector(`script[src="${scriptFile}"]`)) {
            console.log(`Script ${scriptFile} already loaded`);
            return;
        }

        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = scriptFile;
            script.onload = () => {
                console.log(`✅ Loaded script: ${scriptFile}`);
                resolve();
            };
            script.onerror = () => {
                console.warn(`⚠️ Failed to load script: ${scriptFile}`);
                resolve(); // Don't reject, just continue without the script
            };
            document.head.appendChild(script);
        });
    }

    /**
     * Setup popup-specific functionality
     * @param {string} popupType - Type of popup
     */
    setupPopupFunctionality(popupType) {
        // Add close button functionality to any existing close buttons in the content
        const closeButtons = this.popupContainer.querySelectorAll('button');
        closeButtons.forEach(button => {
            const buttonText = button.textContent.toLowerCase();
            if (buttonText.includes('cancel') || buttonText.includes('close')) {
                button.addEventListener('click', () => {
                    this.hidePopup();
                });
            }
        });

        // Override any form submissions to handle success/error in popup context
        const forms = this.popupContainer.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                // Let the original form handler run, but add our own success callback
                setTimeout(() => {
                    // Check if the operation was successful (you may need to adjust this logic)
                    // For now, we'll close the popup after a short delay
                    setTimeout(() => {
                        if (window.animalPageController) {
                            window.animalPageController.refreshAnimalTable();
                            this.hidePopup();
                        }
                    }, 1000);
                }, 100);
            });
        });

        // Setup save button functionality
        const saveButtons = this.popupContainer.querySelectorAll('button');
        saveButtons.forEach(button => {
            const buttonText = button.textContent.toLowerCase();
            if (buttonText.includes('save')) {
                const originalClick = button.onclick;
                button.addEventListener('click', () => {
                    // Let the original save function run
                    if (originalClick) {
                        originalClick();
                    }

                    // After save, refresh table and close popup
                    setTimeout(() => {
                        if (window.animalPageController) {
                            window.animalPageController.refreshAnimalTable();
                            this.showNotification('Data saved successfully!', 'success');
                            setTimeout(() => this.hidePopup(), 1500);
                        }
                    }, 500);
                });
            }
        });
    }

    /**
     * Create popup container with proper styling
     */
    createPopupContainer() {
        // Remove existing popup container
        this.removePopupContainer();

        this.popupContainer = document.createElement('div');
        this.popupContainer.className = 'animal-popup-overlay';
        this.popupContainer.innerHTML = `
            <div class="animal-popup-container">
                <div class="animal-popup-header">
                    <span class="animal-popup-title">Animal Management</span>
                </div>
                <div class="animal-popup-body">
                    <!-- Popup content will be loaded here -->
                </div>
            </div>
        `;

        // Add styles
        this.addPopupStyles();

        // Add event listeners
        this.setupPopupEventListeners();

        // Add EXTERNAL close button (outside iframe, always works)
        this.addExternalCloseButton();

        // Add to document
        document.body.appendChild(this.popupContainer);
    }

    /**
     * Add external close button that's guaranteed to work
     */
    addExternalCloseButton() {
        // Create close button element
        const externalCloseButton = document.createElement('button');
        externalCloseButton.className = 'external-close-button';
        externalCloseButton.innerHTML = '×';
        externalCloseButton.setAttribute('type', 'button');
        externalCloseButton.setAttribute('aria-label', 'Close popup');
        externalCloseButton.setAttribute('title', 'Close popup');

        // Style the external close button - GREEN to match page color scheme
        externalCloseButton.style.cssText = `
            position: fixed !important;
            top: 20px !important;
            right: 20px !important;
            width: 50px !important;
            height: 50px !important;
            background-color: #aedf32 !important;
            color: #ffffff !important;
            border: 3px solid #ffffff !important;
            border-radius: 50% !important;
            font-size: 30px !important;
            font-weight: bold !important;
            cursor: pointer !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-family: Arial, sans-serif !important;
            text-align: center !important;
            padding: 0 !important;
            margin: 0 !important;
            line-height: 1 !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5) !important;
            transition: all 0.2s ease !important;
        `;

        // Add hover effects - darker green on hover
        externalCloseButton.addEventListener('mouseenter', () => {
            externalCloseButton.style.backgroundColor = '#8bc220';
            externalCloseButton.style.transform = 'scale(1.1)';
            externalCloseButton.style.boxShadow = '0 6px 20px rgba(0, 0, 0, 0.7)';
        });

        externalCloseButton.addEventListener('mouseleave', () => {
            externalCloseButton.style.backgroundColor = '#aedf32';
            externalCloseButton.style.transform = 'scale(1)';
            externalCloseButton.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.5)';
        });

        // Add click handler - GUARANTEED TO WORK
        externalCloseButton.addEventListener('click', (e) => {
            console.log('🔴 EXTERNAL CLOSE BUTTON CLICKED - FORCE CLOSING POPUP!');
            e.preventDefault();
            e.stopPropagation();

            try {
                // Force close immediately
                this.hidePopup();
                console.log('✅ EXTERNAL CLOSE BUTTON - POPUP CLOSED SUCCESSFULLY!');
            } catch (error) {
                console.error('❌ External close button error:', error);

                // Ultimate fallback - direct DOM manipulation
                if (this.popupContainer) {
                    this.popupContainer.style.display = 'none';
                    this.isPopupVisible = false;
                }

                // Remove the button itself
                if (externalCloseButton.parentNode) {
                    externalCloseButton.parentNode.removeChild(externalCloseButton);
                }
            }
        });

        // Add to document body (not inside popup container)
        document.body.appendChild(externalCloseButton);

        // Store reference for cleanup
        this.externalCloseButton = externalCloseButton;

        console.log('✅ EXTERNAL close button added - GREEN X in top-right corner');
    }

    /**
     * Add CSS styles for popup
     */
    addPopupStyles() {
        if (!document.querySelector('#animal-popup-styles')) {
            const style = document.createElement('style');
            style.id = 'animal-popup-styles';
            style.textContent = `
                .animal-popup-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: transparent;
                    z-index: 10000;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    pointer-events: none;
                }

                .animal-popup-overlay.visible {
                    opacity: 1;
                    pointer-events: auto;
                }

                .animal-popup-container {
                    background: transparent;
                    border-radius: 0;
                    box-shadow: none;
                    width: auto;
                    height: auto;
                    overflow: visible;
                    transform: scale(0.95);
                    transition: transform 0.3s ease;
                    display: flex;
                    flex-direction: column;
                    position: relative;
                    pointer-events: auto;
                }

                .animal-popup-overlay.visible .animal-popup-container {
                    transform: scale(1);
                }

                .animal-popup-header {
                    background-color: transparent;
                    color: #0b291a;
                    padding: 5px 10px;
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    flex-shrink: 0;
                    position: absolute;
                    top: -30px;
                    right: 0;
                    z-index: 10001;
                    height: 30px;
                    width: auto;
                }

                .animal-popup-title {
                    display: none;
                }

                .animal-popup-close {
                    font-size: 20px;
                    cursor: pointer;
                    padding: 3px 6px;
                    border-radius: 50%;
                    transition: background-color 0.2s ease;
                    line-height: 1;
                    user-select: none;
                    background-color: #0b291a;
                    color: white;
                    width: 24px;
                    height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .animal-popup-close:hover {
                    background-color: #1a4d2e;
                }

                .animal-popup-body {
                    position: relative;
                    width: 100vw;
                    height: 100vh;
                    overflow: visible;
                    padding: 0;
                    margin: 0;
                    background: none !important;
                    background-color: transparent !important;
                    background-image: none !important;
                }

                /* COMPLETE GRAY BACKGROUND ELIMINATION - Override all gray backgrounds */
                .animal-popup-body *[style*="#e3e4e4"],
                .animal-popup-body *[style*="rgb(227, 228, 228)"],
                .animal-popup-body *[style*="background-color: #e3e4e4"],
                .animal-popup-body *[style*="background: #e3e4e4"],
                .animal-popup-body .choose-update-milch,
                .animal-popup-body div[style*="#e3e4e4"],
                .animal-popup-body *,
                .animal-popup-body *:before,
                .animal-popup-body *:after {
                    background: transparent !important;
                    background-color: transparent !important;
                    background-image: none !important;
                }

                /* Exception: Keep white background only for frame-4 */
                .animal-popup-body .frame-4,
                .animal-popup-body .choose-update-milch .frame-4 {
                    background: #fff !important;
                    background-color: #fff !important;
                    background-image: none !important;
                }

                /* Keep form element backgrounds */
                .animal-popup-body .data-filled,
                .animal-popup-body input,
                .animal-popup-body textarea,
                .animal-popup-body select {
                    background: #f1f1f1 !important;
                    background-color: #f1f1f1 !important;
                }

                /* Keep button backgrounds */
                .animal-popup-body .frame-9,
                .animal-popup-body button {
                    background: #aedf32 !important;
                    background-color: #aedf32 !important;
                }

                /* Iframe dimensions increased by 16% (5% + 6% + 5% additional) */
                .animal-popup-body iframe {
                    width: 93.492vw !important;
                    height: 105.1785vh !important;
                    max-width: 1402.38px !important;
                    max-height: 934.92px !important;
                    min-width: 934.92px !important;
                    min-height: 701.19px !important;
                    border: none !important;
                    outline: none !important;
                    box-shadow: none !important;
                    background: transparent !important;
                    margin: 0 !important;
                    padding: 0 !important;
                }

                /* Completely hide and disable gray container */
                .animal-popup-body .choose-update-milch {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    position: absolute !important;
                    top: -9999px !important;
                    left: -9999px !important;
                    width: 0 !important;
                    height: 0 !important;
                    background: none !important;
                    background-color: transparent !important;
                    border: none !important;
                    outline: none !important;
                    box-shadow: none !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    overflow: hidden !important;
                    pointer-events: none !important;
                }

                /* Frame-4 positioned directly in popup body - bypassing gray container */
                .animal-popup-body .frame-4,
                .animal-popup-body .choose-update-milch .frame-4 {
                    position: fixed !important;
                    top: 50% !important;
                    left: 50% !important;
                    transform: translate(-50%, -50%) !important;
                    background-color: #fff !important;
                    border-radius: 16px !important;
                    display: flex !important;
                    flex-direction: column !important;
                    align-items: flex-start !important;
                    border: none !important;
                    outline: none !important;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
                    padding: 0 !important;
                    margin: 0 !important;
                    z-index: 10002 !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    pointer-events: auto !important;
                }

                /* UNIVERSAL Close button for ALL popups - Enhanced design */
                .animal-popup-body .frame-4::before,
                .animal-popup-body .choose-update-milch .frame-4::before {
                    content: "×" !important;
                    position: absolute !important;
                    top: 20px !important;
                    right: 25px !important;
                    width: 40px !important;
                    height: 40px !important;
                    background-color: #f8f9fa !important;
                    color: #495057 !important;
                    border: 2px solid #dee2e6 !important;
                    border-radius: 50% !important;
                    display: flex !important;
                    align-items: center !important;
                    justify-content: center !important;
                    font-size: 28px !important;
                    font-weight: bold !important;
                    cursor: pointer !important;
                    z-index: 10005 !important;
                    transition: all 0.3s ease !important;
                    line-height: 1 !important;
                    user-select: none !important;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
                    font-family: Arial, sans-serif !important;
                }

                .animal-popup-body .frame-4:hover::before,
                .animal-popup-body .choose-update-milch .frame-4:hover::before {
                    background-color: #e9ecef !important;
                    color: #212529 !important;
                    border-color: #adb5bd !important;
                    transform: scale(1.2) !important;
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25) !important;
                }

                /* Universal close button applies to all popups including Update Fattening Weight */

                /* Add New Animal - ENLARGED by 31% (15% + 5% + 6% + 5% increase) for better visibility */
                .animal-popup-body[data-popup-type="addnewanimal"] .frame-4,
                .animal-popup-body[data-popup-type="addnewanimal"] .choose-update-milch .frame-4 {
                    width: 93.492vw !important;
                    height: 105.1785vh !important;
                    max-width: 1402.38px !important;
                    max-height: 1051.785px !important;
                    min-width: 818.055px !important;
                    min-height: 701.19px !important;
                }

                /* Update Animal - ENLARGED by 31% (15% + 5% + 6% + 5% increase) for better visibility */
                .animal-popup-body[data-popup-type="updateanimal"] .frame-4,
                .animal-popup-body[data-popup-type="updateanimal"] .choose-update-milch .frame-4 {
                    width: 99.33525vw !important;
                    height: 111.02175vh !important;
                    max-width: 1519.245px !important;
                    max-height: 1168.65px !important;
                    min-width: 876.4875px !important;
                    min-height: 759.6225px !important;
                }

                /* Update Dairy Data - ENLARGED by 31% (15% + 5% + 6% + 5% increase) for better visibility */
                .animal-popup-body[data-popup-type="updatemilchdata"] .frame-4,
                .animal-popup-body[data-popup-type="updatemilchdata"] .choose-update-milch .frame-4 {
                    width: 99.33525vw !important;
                    height: 111.02175vh !important;
                    max-width: 1519.245px !important;
                    max-height: 1168.65px !important;
                    min-width: 876.4875px !important;
                    min-height: 759.6225px !important;
                }

                /* Update Fattening Weight - ENLARGED by 31% (15% + 5% + 6% + 5% increase) for consistency */
                .animal-popup-body[data-popup-type="updatefatteningweight"] .frame-4,
                .animal-popup-body[data-popup-type="updatefatteningweight"] .choose-update-milch .frame-4 {
                    width: 99.33525vw !important;
                    height: 107.5158vh !important;
                    max-width: 1519.245px !important;
                    max-height: 1110.2175px !important;
                    min-width: 876.4875px !important;
                    min-height: 701.19px !important;
                }

                /* Update Newborn - ENLARGED by 31% (15% + 5% + 6% + 5% increase) for better visibility */
                .animal-popup-body[data-popup-type="updatenewborn"] .frame-4,
                .animal-popup-body[data-popup-type="updatenewborn"] .choose-update-milch .frame-4 {
                    width: 99.33525vw !important;
                    height: 111.02175vh !important;
                    max-width: 1519.245px !important;
                    max-height: 1168.65px !important;
                    min-width: 876.4875px !important;
                    min-height: 759.6225px !important;
                }

                /* Preserve all original styling for form elements */
                .animal-popup-body .frame-5 {
                    display: flex !important;
                    width: 10% !important;
                    height: 15% !important;
                    align-items: center !important;
                    justify-content: flex-start !important;
                    gap: 15px !important;
                    padding: 10px !important;
                    position: relative !important;
                    left: 1% !important;
                }

                .animal-popup-body .frame-6 {
                    display: flex !important;
                    flex-direction: column !important;
                    width: 100% !important;
                    height: 90% !important;
                    align-items: center !important;
                    position: absolute !important;
                    top: 10% !important;
                }

                .animal-popup-body .row {
                    width: 95% !important;
                    padding-top: 5% !important;
                    display: flex !important;
                    justify-content: space-between !important;
                }

                .animal-popup-body .frame-7 {
                    display: flex !important;
                    align-items: center !important;
                    flex-direction: row !important;
                    width: 100% !important;
                }

                .animal-popup-body .text-wrapper-5 {
                    position: relative !important;
                    width: fit-content !important;
                    font-weight: bold !important;
                    color: #0b291a !important;
                    font-size: 20px !important;
                    white-space: nowrap !important;
                }

                .animal-popup-body .text-wrapper-6 {
                    position: relative !important;
                    width: 10% !important;
                    font-family: "Roboto-Medium", Helvetica !important;
                    color: #000000 !important;
                    font-size: 18px !important;
                    line-height: 18px !important;
                    white-space: nowrap !important;
                    left: 5% !important;
                }

                .animal-popup-body .data-filled {
                    position: relative !important;
                    width: 45% !important;
                    height: 55px !important;
                    background-color: #f1f1f1 !important;
                    border-radius: 12px !important;
                    border: hidden !important;
                    left: 40% !important;
                }

                .animal-popup-body .frame-9 {
                    display: flex !important;
                    position: relative !important;
                    width: 48% !important;
                    height: 55px !important;
                    align-items: center !important;
                    justify-content: center !important;
                    border: hidden !important;
                    background-color: #aedf32 !important;
                    border-radius: 12px !important;
                    left: 51.4% !important;
                }

                .animal-popup-body .btntext {
                    font-size: 20px !important;
                    font-weight: 400 !important;
                    color: #ffffff !important;
                }

                .animal-popup-body .update {
                    position: relative !important;
                    width: 40px !important;
                    height: 40px !important;
                }

                /* Remove all borders and outlines */
                .animal-popup-body *,
                .animal-popup-body *:before,
                .animal-popup-body *:after {
                    border: none !important;
                    outline: none !important;
                    box-shadow: none !important;
                }

                /* Responsive design for ALL ENLARGED popups */
                @media screen and (max-width: 1200px) {
                    /* Large tablets - maintain enlarged sizes increased by 16% (5% + 6% + 5% additional) */
                    .animal-popup-body[data-popup-type="addnewanimal"] .frame-4 {
                        width: 87.64875vw !important;
                        height: 99.33525vh !important;
                        max-width: 1051.785px !important;
                        max-height: 818.055px !important;
                    }

                    .animal-popup-body[data-popup-type="updateanimal"] .frame-4,
                    .animal-popup-body[data-popup-type="updatemilchdata"] .frame-4,
                    .animal-popup-body[data-popup-type="updatenewborn"] .frame-4 {
                        width: 93.492vw !important;
                        height: 105.1785vh !important;
                        max-width: 1168.65px !important;
                        max-height: 934.92px !important;
                    }

                    .animal-popup-body[data-popup-type="updatefatteningweight"] .frame-4 {
                        width: 93.492vw !important;
                        height: 101.67255vh !important;
                        max-width: 1168.65px !important;
                        max-height: 876.4875px !important;
                    }
                }

                @media screen and (max-width: 900px) {
                    /* Tablets - maintain enlarged sizes increased by 16% (5% + 6% + 5% additional) */
                    .animal-popup-body[data-popup-type="addnewanimal"] .frame-4 {
                        width: 105.1785vw !important;
                        height: 93.492vh !important;
                        max-width: 818.055px !important;
                        max-height: 701.19px !important;
                        min-width: 584.325px !important;
                        min-height: 525.8925px !important;
                    }

                    .animal-popup-body[data-popup-type="updateanimal"] .frame-4,
                    .animal-popup-body[data-popup-type="updatemilchdata"] .frame-4,
                    .animal-popup-body[data-popup-type="updatenewborn"] .frame-4 {
                        width: 111.02175vw !important;
                        height: 99.33525vh !important;
                        max-width: 876.4875px !important;
                        max-height: 759.6225px !important;
                        min-width: 642.7575px !important;
                        min-height: 584.325px !important;
                    }

                    .animal-popup-body[data-popup-type="updatefatteningweight"] .frame-4 {
                        width: 111.02175vw !important;
                        height: 95.8293vh !important;
                        max-width: 876.4875px !important;
                        max-height: 724.563px !important;
                        min-width: 642.7575px !important;
                        min-height: 560.952px !important;
                    }
                }

                @media screen and (max-width: 600px) {
                    /* Mobile - maximum screen usage for ALL popups increased by 16% (5% + 6% + 5% additional) */
                    .animal-popup-body[data-popup-type="addnewanimal"] .frame-4 {
                        width: 114.5277vw !important;
                        height: 105.1785vh !important;
                        max-width: 701.19px !important;
                        max-height: 818.055px !important;
                        min-width: 467.46px !important;
                        min-height: 584.325px !important;
                    }

                    .animal-popup-body[data-popup-type="updateanimal"] .frame-4,
                    .animal-popup-body[data-popup-type="updatemilchdata"] .frame-4,
                    .animal-popup-body[data-popup-type="updatenewborn"] .frame-4 {
                        width: 114.5277vw !important;
                        height: 111.02175vh !important;
                        max-width: 701.19px !important;
                        max-height: 876.4875px !important;
                        min-width: 467.46px !important;
                        min-height: 642.7575px !important;
                    }

                    .animal-popup-body[data-popup-type="updatefatteningweight"] .frame-4 {
                        width: 114.5277vw !important;
                        height: 107.5158vh !important;
                        max-width: 701.19px !important;
                        max-height: 841.428px !important;
                        min-width: 467.46px !important;
                        min-height: 607.698px !important;
                    }

                    /* Smaller text and elements for mobile */
                    .animal-popup-body .text-wrapper-5 {
                        font-size: 16px !important;
                    }

                    .animal-popup-body .text-wrapper-6 {
                        font-size: 14px !important;
                        width: 15% !important;
                    }

                    .animal-popup-body .data-filled {
                        width: 50% !important;
                        height: 45px !important;
                        left: 35% !important;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        // Add delete button styling to maintain original appearance
        if (!document.querySelector('#delete-button-styles')) {
            const deleteStyle = document.createElement('style');
            deleteStyle.id = 'delete-button-styles';
            deleteStyle.textContent = `
                /* Ensure delete button maintains original styling */
                .frame-4 button.frame-9:has(.btntext:contains("Delete")) {
                    background-color: #aedf32 !important;
                }

                /* Alternative selector for delete button */
                .frame-4 button.frame-9 {
                    background-color: #aedf32;
                    border: none;
                    border-radius: 14px;
                    color: white;
                    cursor: pointer;
                    transition: background-color 0.2s ease;
                }

                .frame-4 button.frame-9:hover {
                    background-color: #8fb728;
                }

                .frame-4 .btntext {
                    color: #ffffff;
                    font-size: 20px;
                    font-weight: 400;
                }
            `;
            document.head.appendChild(deleteStyle);
        }
    }

    /**
     * Setup event listeners for popup
     */
    setupPopupEventListeners() {
        if (!this.popupContainer) return;

        const overlay = this.popupContainer;

        // Click outside to close
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                this.hidePopup();
            }
        });

        // Escape key to close
        document.addEventListener('keydown', this.handleKeyDown.bind(this));
    }

    /**
     * Handle keyboard events
     * @param {KeyboardEvent} e - Keyboard event
     */
    handleKeyDown(e) {
        if (!this.isPopupVisible) return;

        if (e.key === 'Escape') {
            this.hidePopup();
        }
    }



    /**
     * Get popup title based on type
     * @param {string} popupType - Type of popup
     * @returns {string} - Popup title
     */
    getPopupTitle(popupType) {
        const titles = {
            addnewanimal: 'Add New Animal',
            updatemilchdata: 'Update Dairy Data',
            updatenewborn: 'Update Newborn Data',
            updatefatteningweight: 'Update Fattening Weight'
        };
        return titles[popupType] || 'Animal Management';
    }





    /**
     * Load script dynamically
     * @param {string} src - Script source path
     * @returns {Promise} - Promise that resolves when script is loaded
     */
    loadScript(src) {
        return new Promise((resolve, reject) => {
            // Check if script is already loaded
            if (document.querySelector(`script[src="${src}"]`)) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    /**
     * Display popup with animation
     */
    displayPopup() {
        if (this.popupContainer) {
            this.isPopupVisible = true;
            this.popupContainer.style.display = 'flex';

            // Trigger animation
            setTimeout(() => {
                this.popupContainer.classList.add('visible');
            }, 10);
        }
    }

    /**
     * Add UNIVERSAL close button functionality for ALL popups (iframe content approach)
     */
    addFrame4CloseButtonListener() {
        // Close button is now injected into iframe content via CSS
        // The addIframeCloseButtonListener method handles the actual click detection

        // For iframe-based popups, the close button listener is added via CSS injection
        const iframe = this.popupContainer.querySelector('iframe');
        if (iframe) {
            console.log('✅ Close button will be added to iframe content via CSS injection');
        } else {
            // For non-iframe popups (programmatic content), add direct listeners
            const frame4Elements = this.popupContainer.querySelectorAll('.frame-4');

            frame4Elements.forEach(frame4 => {
                frame4.addEventListener('click', (e) => {
                    // Get the frame-4 dimensions and position
                    const rect = frame4.getBoundingClientRect();
                    const clickX = e.clientX;
                    const clickY = e.clientY;

                    // Get popup type for logging
                    const popupType = this.popupContainer.getAttribute('data-popup-type') || 'unknown';

                    // Close button dimensions: top: 20px, right: 25px with 40px width and height
                    const closeButtonLeft = rect.right - 65; // 25px from right + 40px width
                    const closeButtonRight = rect.right - 25;
                    const closeButtonTop = rect.top + 20;
                    const closeButtonBottom = rect.top + 60; // 20px from top + 40px height

                    // Check if click is within close button area
                    if (clickX >= closeButtonLeft && clickX <= closeButtonRight &&
                        clickY >= closeButtonTop && clickY <= closeButtonBottom) {
                        console.log(`🔴 Direct close button clicked for "${popupType}" popup`);
                        this.hidePopup();
                        e.stopPropagation(); // Prevent event bubbling
                    }
                });
            });

            console.log('✅ Direct close button listeners added for non-iframe content');
        }
    }

    /**
     * Hide popup with animation
     */
    hidePopup() {
        if (this.popupContainer && this.isPopupVisible) {
            this.popupContainer.classList.remove('visible');

            setTimeout(() => {
                this.removePopupContainer();
            }, 300);

            this.isPopupVisible = false;

            // Remove keyboard event listener
            document.removeEventListener('keydown', this.handleKeyDown.bind(this));

            // Remove external close button
            this.removeExternalCloseButton();
        }
    }

    /**
     * Remove external close button
     */
    removeExternalCloseButton() {
        if (this.externalCloseButton && this.externalCloseButton.parentNode) {
            this.externalCloseButton.parentNode.removeChild(this.externalCloseButton);
            this.externalCloseButton = null;
            console.log('✅ External close button removed');
        }
    }

    /**
     * Remove popup container from DOM
     */
    removePopupContainer() {
        if (this.popupContainer && this.popupContainer.parentNode) {
            this.popupContainer.parentNode.removeChild(this.popupContainer);
            this.popupContainer = null;
            this.currentPopup = null;
        }
    }

    /**
     * Show notification message
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, warning, info)
     * @param {number} duration - Auto-dismiss duration in milliseconds
     */
    showNotification(message, type = 'info', duration = 4000) {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.animal-notification');
        existingNotifications.forEach(notification => notification.remove());

        const notification = document.createElement('div');
        notification.className = 'animal-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10001;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            animation: slideInRight 0.3s ease-out;
        `;

        // Set colors based on type
        const colors = {
            success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
            error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
            warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
            info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
        };

        const color = colors[type] || colors.info;
        notification.style.backgroundColor = color.bg;
        notification.style.border = `1px solid ${color.border}`;
        notification.style.color = color.text;

        notification.textContent = message;

        // Add close button
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            float: right;
            margin-left: 10px;
            cursor: pointer;
            font-size: 18px;
            line-height: 1;
        `;
        closeBtn.onclick = () => notification.remove();
        notification.appendChild(closeBtn);

        document.body.appendChild(notification);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }

        // Add animation styles if not already present
        if (!document.querySelector('#animal-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'animal-notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);
        }
    }
}

// ==================== DROPDOWN MANAGERS ====================

/**
 * Update Button Dropdown Manager
 * Handles the dropdown menu for the Update Data button
 */
class UpdateDropdownManager {
    constructor(animalPageController) {
        this.animalPageController = animalPageController;
        this.dropdownContainer = null;
        this.isVisible = false;
    }

    /**
     * Show update dropdown menu
     * @param {HTMLElement} buttonElement - Update button element
     */
    showDropdown(buttonElement) {
        // Hide any existing dropdown
        this.hideDropdown();

        // Create dropdown
        this.createDropdown(buttonElement);
        this.showDropdownWithAnimation();
    }

    /**
     * Create dropdown menu
     * @param {HTMLElement} buttonElement - Update button element
     */
    createDropdown(buttonElement) {
        this.dropdownContainer = document.createElement('div');
        this.dropdownContainer.className = 'update-dropdown-container';

        const buttonRect = buttonElement.getBoundingClientRect();

        this.dropdownContainer.innerHTML = `
            <div class="update-dropdown-menu">
                <div class="update-dropdown-item" data-popup="updatemilchdata">
                    <img src="icons/milk-can.png" alt="Dairy">
                    <span>Dairy</span>
                </div>
                <div class="update-dropdown-item" data-popup="updateanimal">
                    <img src="icons/cow-1.png" alt="Animal">
                    <span>Animal</span>
                </div>
            </div>
        `;

        // Position dropdown
        this.dropdownContainer.style.cssText = `
            position: fixed;
            top: ${buttonRect.bottom + 5}px;
            left: ${buttonRect.left}px;
            z-index: 9999;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        `;

        // Add styles
        this.addDropdownStyles();

        // Add event listeners
        this.setupDropdownEventListeners();

        // Add to document
        document.body.appendChild(this.dropdownContainer);
    }

    /**
     * Add CSS styles for dropdown
     */
    addDropdownStyles() {
        if (!document.querySelector('#update-dropdown-styles')) {
            const style = document.createElement('style');
            style.id = 'update-dropdown-styles';
            style.textContent = `
                .update-dropdown-menu {
                    background: #aedf32;
                    border-radius: 14px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    border: 2px solid #8fb728;
                    overflow: hidden;
                    min-width: 220px;
                }

                .update-dropdown-item {
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                    color: #ffffff;
                    font-weight: 500;
                }

                .update-dropdown-item:last-child {
                    border-bottom: none;
                }

                .update-dropdown-item:hover {
                    background-color: #8fb728;
                    transform: translateX(2px);
                }

                .update-dropdown-item img {
                    width: 20px;
                    height: 20px;
                    margin-right: 12px;
                    filter: brightness(0) invert(1);
                }

                .update-dropdown-item span {
                    font-size: 16px;
                    color: #ffffff;
                    font-weight: 500;
                }

                .update-dropdown-container {
                    z-index: 10000;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Setup event listeners for dropdown
     */
    setupDropdownEventListeners() {
        if (!this.dropdownContainer) return;

        // Click on dropdown items
        const items = this.dropdownContainer.querySelectorAll('.update-dropdown-item');
        items.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();

                const popupType = item.getAttribute('data-popup');
                console.log(`🔄 Update dropdown item clicked: ${popupType}`);

                // Verify popup type is valid
                const validPopupTypes = ['updatemilchdata', 'updateanimal'];
                if (!validPopupTypes.includes(popupType)) {
                    console.error(`❌ Invalid popup type: ${popupType}`);
                    return;
                }

                // Show the specific popup
                this.animalPageController.popupManager.showPopup(popupType)
                    .then(() => {
                        console.log(`✅ Successfully showed ${popupType} popup`);
                    })
                    .catch(error => {
                        console.error(`❌ Failed to show ${popupType} popup:`, error);
                    });

                this.hideDropdown();
            });
        });

        // Click outside to close
        document.addEventListener('click', this.handleOutsideClick.bind(this));
    }

    /**
     * Handle click outside dropdown
     * @param {Event} e - Click event
     */
    handleOutsideClick(e) {
        if (this.isVisible && this.dropdownContainer && !this.dropdownContainer.contains(e.target)) {
            this.hideDropdown();
        }
    }

    /**
     * Show dropdown with animation
     */
    showDropdownWithAnimation() {
        if (this.dropdownContainer) {
            this.isVisible = true;
            setTimeout(() => {
                this.dropdownContainer.style.opacity = '1';
                this.dropdownContainer.style.transform = 'translateY(0)';
            }, 10);
        }
    }

    /**
     * Hide dropdown
     */
    hideDropdown() {
        if (this.dropdownContainer && this.isVisible) {
            this.dropdownContainer.style.opacity = '0';
            this.dropdownContainer.style.transform = 'translateY(-10px)';

            setTimeout(() => {
                if (this.dropdownContainer && this.dropdownContainer.parentNode) {
                    this.dropdownContainer.parentNode.removeChild(this.dropdownContainer);
                    this.dropdownContainer = null;
                }
            }, 300);

            this.isVisible = false;

            // Remove event listener
            document.removeEventListener('click', this.handleOutsideClick.bind(this));
        }
    }
}

/**
 * Filter Dropdown Manager
 * Handles the dropdown menu for the Filter button
 */
class FilterDropdownManager {
    constructor(animalPageController) {
        this.animalPageController = animalPageController;
        this.dropdownContainer = null;
        this.isVisible = false;
        this.currentFilters = {};
    }

    /**
     * Show filter dropdown menu
     * @param {HTMLElement} buttonElement - Filter button element
     */
    showDropdown(buttonElement) {
        // Hide any existing dropdown
        this.hideDropdown();

        // Create dropdown
        this.createDropdown(buttonElement);
        this.showDropdownWithAnimation();
    }

    /**
     * Create filter dropdown menu
     * @param {HTMLElement} buttonElement - Filter button element
     */
    createDropdown(buttonElement) {
        this.dropdownContainer = document.createElement('div');
        this.dropdownContainer.className = 'filter-dropdown-container';

        const buttonRect = buttonElement.getBoundingClientRect();

        this.dropdownContainer.innerHTML = `
            <div class="filter-dropdown-menu">
                <div class="filter-section">
                    <label>Type:</label>
                    <select class="filter-select" data-field="type">
                        <option value="all">All Types</option>
                        <option value="dairy">Dairy</option>
                        <option value="newborn">Newborn</option>
                        <option value="fattening">Fattening</option>
                    </select>
                </div>
                <div class="filter-section">
                    <label>Gender:</label>
                    <select class="filter-select" data-field="gender">
                        <option value="all">All Genders</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                    </select>
                </div>
                <div class="filter-section">
                    <label>Weight:</label>
                    <select class="filter-select" data-field="weight">
                        <option value="all">All Weights</option>
                        <option value="light">Light (&lt; 200kg)</option>
                        <option value="medium">Medium (200-500kg)</option>
                        <option value="heavy">Heavy (&gt; 500kg)</option>
                    </select>
                </div>
                <div class="filter-actions">
                    <button class="filter-apply-btn">Apply Filters</button>
                    <button class="filter-clear-btn">Clear All</button>
                </div>
            </div>
        `;

        // Position dropdown
        this.dropdownContainer.style.cssText = `
            position: fixed;
            top: ${buttonRect.bottom + 5}px;
            left: ${buttonRect.left}px;
            z-index: 9999;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        `;

        // Add styles
        this.addFilterDropdownStyles();

        // Add event listeners
        this.setupFilterDropdownEventListeners();

        // Set current filter values
        this.setCurrentFilterValues();

        // Add to document
        document.body.appendChild(this.dropdownContainer);
    }

    /**
     * Add CSS styles for filter dropdown
     */
    addFilterDropdownStyles() {
        if (!document.querySelector('#filter-dropdown-styles')) {
            const style = document.createElement('style');
            style.id = 'filter-dropdown-styles';
            style.textContent = `
                .filter-dropdown-menu {
                    background: #aedf32;
                    border-radius: 14px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    border: 2px solid #8fb728;
                    padding: 16px;
                    min-width: 280px;
                }

                .filter-section {
                    margin-bottom: 14px;
                }

                .filter-section label {
                    display: block;
                    font-size: 14px;
                    font-weight: bold;
                    color: #ffffff;
                    margin-bottom: 6px;
                }

                .filter-select {
                    width: 100%;
                    padding: 8px 12px;
                    border: 2px solid #a9c99a;
                    border-radius: 8px;
                    font-size: 14px;
                    background: #ffffff;
                    color: #2d5016;
                    font-weight: 500;
                    transition: border-color 0.2s ease;
                }

                .filter-select:focus {
                    outline: none;
                    border-color: #8fb728;
                    box-shadow: 0 0 0 2px rgba(174, 223, 50, 0.2);
                }

                .filter-actions {
                    display: flex;
                    gap: 10px;
                    margin-top: 18px;
                }

                .filter-apply-btn, .filter-clear-btn {
                    flex: 1;
                    padding: 10px 14px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }

                .filter-apply-btn {
                    background: #aedf32;
                    color: #ffffff;
                    border: 2px solid #8fb728;
                }

                .filter-apply-btn:hover {
                    background: #8fb728;
                    transform: translateY(-1px);
                }

                .filter-clear-btn {
                    background: #ffffff;
                    color: #8fb728;
                    border: 2px solid #8fb728;
                }

                .filter-clear-btn:hover {
                    background: #8fb728;
                    color: #ffffff;
                    transform: translateY(-1px);
                }

                .filter-dropdown-container {
                    z-index: 10000;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Setup event listeners for filter dropdown
     */
    setupFilterDropdownEventListeners() {
        if (!this.dropdownContainer) return;

        // Apply filters button
        const applyBtn = this.dropdownContainer.querySelector('.filter-apply-btn');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.applyFilters();
                this.hideDropdown();
            });
        }

        // Clear filters button
        const clearBtn = this.dropdownContainer.querySelector('.filter-clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearFilters();
                this.hideDropdown();
            });
        }

        // Click outside to close
        document.addEventListener('click', this.handleFilterOutsideClick.bind(this));
    }

    /**
     * Set current filter values in dropdown
     */
    setCurrentFilterValues() {
        if (!this.dropdownContainer) return;

        const selects = this.dropdownContainer.querySelectorAll('.filter-select');
        selects.forEach(select => {
            const field = select.getAttribute('data-field');
            if (this.currentFilters[field]) {
                select.value = this.currentFilters[field];
            }
        });
    }

    /**
     * Apply filters
     */
    applyFilters() {
        const selects = this.dropdownContainer.querySelectorAll('.filter-select');
        const filters = {};

        selects.forEach(select => {
            const field = select.getAttribute('data-field');
            const value = select.value;
            if (value !== 'all') {
                filters[field] = value;
            }
        });

        this.currentFilters = filters;
        this.animalPageController.applyFilters(filters);
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        this.currentFilters = {};
        this.animalPageController.clearFilters();
    }

    /**
     * Handle click outside filter dropdown
     * @param {Event} e - Click event
     */
    handleFilterOutsideClick(e) {
        if (this.isVisible && this.dropdownContainer && !this.dropdownContainer.contains(e.target)) {
            this.hideDropdown();
        }
    }

    /**
     * Show dropdown with animation
     */
    showDropdownWithAnimation() {
        if (this.dropdownContainer) {
            this.isVisible = true;
            setTimeout(() => {
                this.dropdownContainer.style.opacity = '1';
                this.dropdownContainer.style.transform = 'translateY(0)';
            }, 10);
        }
    }

    /**
     * Hide dropdown
     */
    hideDropdown() {
        if (this.dropdownContainer && this.isVisible) {
            this.dropdownContainer.style.opacity = '0';
            this.dropdownContainer.style.transform = 'translateY(-10px)';

            setTimeout(() => {
                if (this.dropdownContainer && this.dropdownContainer.parentNode) {
                    this.dropdownContainer.parentNode.removeChild(this.dropdownContainer);
                    this.dropdownContainer = null;
                }
            }, 300);

            this.isVisible = false;

            // Remove event listener
            document.removeEventListener('click', this.handleFilterOutsideClick.bind(this));
        }
    }
}

/**
 * Search Dropdown Manager
 * Handles the dropdown menu for the Search button with inline input fields
 */
class SearchDropdownManager {
    constructor(animalPageController) {
        this.animalPageController = animalPageController;
        this.dropdownContainer = null;
        this.isVisible = false;
        this.currentSearchField = null;
    }

    /**
     * Show search dropdown menu
     * @param {HTMLElement} buttonElement - Search button element
     */
    showDropdown(buttonElement) {
        // Hide any existing dropdown
        this.hideDropdown();

        // Add active class to search container
        const searchContainer = buttonElement.closest('.search-dropdown-container');
        if (searchContainer) {
            searchContainer.classList.add('active');
        }

        // Create dropdown
        this.createDropdown(buttonElement);
        this.showDropdownWithAnimation();
    }

    /**
     * Create search dropdown menu
     * @param {HTMLElement} buttonElement - Search button element
     */
    createDropdown(buttonElement) {
        this.dropdownContainer = document.createElement('div');
        this.dropdownContainer.className = 'search-dropdown-container-dynamic';

        const buttonRect = buttonElement.getBoundingClientRect();

        this.dropdownContainer.innerHTML = `
            <div class="search-dropdown-menu-dynamic">
                <div class="search-option-dynamic" data-search="code">
                    <span class="search-icon-dynamic">🏷️</span>
                    <span>By Code</span>
                </div>
                <div class="search-input-container-dynamic" id="codeSearchContainerDynamic" style="display: none;">
                    <input type="text" class="search-input-dynamic" id="codeSearchInputDynamic" placeholder="Enter animal code...">
                    <div class="search-buttons-dynamic">
                        <button class="search-btn-small-dynamic" data-search-type="code">Search</button>
                        <button class="search-btn-cancel-dynamic">Cancel</button>
                    </div>
                </div>

                <div class="search-option-dynamic" data-search="type">
                    <span class="search-icon-dynamic">🐄</span>
                    <span>By Type</span>
                </div>
                <div class="search-input-container-dynamic" id="typeSearchContainerDynamic" style="display: none;">
                    <input type="text" class="search-input-dynamic" id="typeSearchInputDynamic" placeholder="Enter type (Dairy, Newborn, Fattening)...">
                    <div class="search-buttons-dynamic">
                        <button class="search-btn-small-dynamic" data-search-type="type">Search</button>
                        <button class="search-btn-cancel-dynamic">Cancel</button>
                    </div>
                </div>

                <div class="search-option-dynamic" data-search="herdNumber">
                    <span class="search-icon-dynamic">🏠</span>
                    <span>By Herd Number</span>
                </div>
                <div class="search-input-container-dynamic" id="herdNumberSearchContainerDynamic" style="display: none;">
                    <input type="text" class="search-input-dynamic" id="herdNumberSearchInputDynamic" placeholder="Enter herd number...">
                    <div class="search-buttons-dynamic">
                        <button class="search-btn-small-dynamic" data-search-type="herdNumber">Search</button>
                        <button class="search-btn-cancel-dynamic">Cancel</button>
                    </div>
                </div>

                <div class="search-option-dynamic" data-search="clear">
                    <span class="search-icon-dynamic">🔄</span>
                    <span>Clear Search</span>
                </div>
            </div>
        `;

        // Position dropdown
        this.dropdownContainer.style.cssText = `
            position: fixed;
            top: ${buttonRect.bottom + 5}px;
            left: ${buttonRect.left}px;
            z-index: 9999;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        `;

        // Add styles
        this.addSearchDropdownStyles();

        // Add event listeners
        this.setupSearchDropdownEventListeners();

        // Add to document
        document.body.appendChild(this.dropdownContainer);
    }

    /**
     * Add CSS styles for search dropdown (matching vaccination page style)
     */
    addSearchDropdownStyles() {
        if (!document.querySelector('#search-dropdown-styles-animal')) {
            const style = document.createElement('style');
            style.id = 'search-dropdown-styles-animal';
            style.textContent = `
                .search-dropdown-menu-dynamic {
                    background-color: #c1d8b9;
                    border: 2px solid #a8c49a;
                    border-radius: 14px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    padding: 0;
                    min-width: 280px;
                    overflow: hidden;
                }

                .search-option-dynamic {
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    gap: 10px;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                    color: #a9a9a9;
                }

                .search-option-dynamic:last-child {
                    border-bottom: none;
                }

                .search-option-dynamic:hover {
                    background-color: #a8c49a;
                    color: #ffffff;
                }

                .search-option-dynamic.active {
                    background-color: #a8c49a;
                    color: #ffffff;
                    font-weight: bold;
                }

                .search-icon-dynamic {
                    font-size: 18px;
                    display: inline-block;
                    transition: all 0.3s ease;
                }

                .search-option-dynamic span {
                    font-size: 16px;
                    font-weight: 500;
                    color: inherit;
                }

                /* Search input containers */
                .search-input-container-dynamic {
                    padding: 12px 16px;
                    background-color: #f8f9fa;
                    border-top: 1px solid #e0e0e0;
                    border-bottom: 1px solid #e0e0e0;
                }

                .search-input-dynamic {
                    width: 100%;
                    padding: 8px 12px;
                    border: 2px solid #c1d8b9;
                    border-radius: 8px;
                    font-size: 14px;
                    margin-bottom: 8px;
                    outline: none;
                    transition: border-color 0.3s ease;
                }

                .search-input-dynamic:focus {
                    border-color: #a8c49a;
                    box-shadow: 0 0 0 2px rgba(193, 216, 185, 0.2);
                }

                .search-input-dynamic::placeholder {
                    color: #999;
                    font-style: italic;
                }

                .search-buttons-dynamic {
                    display: flex;
                    gap: 8px;
                    justify-content: flex-end;
                }

                .search-btn-small-dynamic {
                    padding: 6px 12px;
                    background-color: #c1d8b9;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .search-btn-small-dynamic:hover {
                    background-color: #a8c49a;
                    transform: translateY(-1px);
                }

                .search-btn-cancel-dynamic {
                    padding: 6px 12px;
                    background-color: #e0e0e0;
                    color: #666;
                    border: none;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .search-btn-cancel-dynamic:hover {
                    background-color: #d0d0d0;
                    transform: translateY(-1px);
                }

                /* Expanded dropdown when input is shown */
                .search-dropdown-menu-dynamic.expanded {
                    min-height: auto;
                    max-height: 400px;
                }

                .search-dropdown-container-dynamic {
                    z-index: 10000;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Setup event listeners for search dropdown
     */
    setupSearchDropdownEventListeners() {
        if (!this.dropdownContainer) return;

        // Search option clicks
        const searchOptions = this.dropdownContainer.querySelectorAll('.search-option-dynamic');
        searchOptions.forEach(option => {
            option.addEventListener('click', () => {
                const searchType = option.getAttribute('data-search');
                this.selectSearchOption(option, searchType);
            });
        });

        // Search buttons
        const searchButtons = this.dropdownContainer.querySelectorAll('.search-btn-small-dynamic');
        searchButtons.forEach(button => {
            button.addEventListener('click', () => {
                const searchType = button.getAttribute('data-search-type');
                this.performSearch(searchType);
            });
        });

        // Cancel buttons
        const cancelButtons = this.dropdownContainer.querySelectorAll('.search-btn-cancel-dynamic');
        cancelButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.cancelSearch();
            });
        });

        // Enter key in input fields
        const inputFields = this.dropdownContainer.querySelectorAll('.search-input-dynamic');
        inputFields.forEach(input => {
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    const searchType = input.id.replace('SearchInputDynamic', '').replace('code', 'code').replace('type', 'type').replace('herdNumber', 'herdNumber');
                    this.performSearch(searchType);
                }
            });
        });

        // Click outside to close
        document.addEventListener('click', this.handleSearchOutsideClick.bind(this));
    }

    /**
     * Select search option and show input field
     * @param {HTMLElement} optionElement - Selected option element
     * @param {string} searchType - Type of search
     */
    selectSearchOption(optionElement, searchType) {
        // Remove active class from all options
        this.dropdownContainer.querySelectorAll('.search-option-dynamic').forEach(opt => {
            opt.classList.remove('active');
        });

        // Hide all input containers
        this.dropdownContainer.querySelectorAll('.search-input-container-dynamic').forEach(container => {
            container.style.display = 'none';
        });

        // Add active class to selected option
        optionElement.classList.add('active');

        // Execute search based on type
        if (searchType === 'clear') {
            this.animalPageController.clearSearch();
            this.hideDropdown();
        } else {
            // Show the corresponding input container
            const inputContainer = this.dropdownContainer.querySelector(`#${searchType}SearchContainerDynamic`);
            if (inputContainer) {
                inputContainer.style.display = 'block';
                this.dropdownContainer.querySelector('.search-dropdown-menu-dynamic').classList.add('expanded');

                // Focus on the input field
                const inputField = this.dropdownContainer.querySelector(`#${searchType}SearchInputDynamic`);
                if (inputField) {
                    setTimeout(() => inputField.focus(), 100);
                }
            }
        }
    }

    /**
     * Perform search with input value
     * @param {string} searchType - Type of search
     */
    performSearch(searchType) {
        const inputField = this.dropdownContainer.querySelector(`#${searchType}SearchInputDynamic`);
        if (inputField) {
            const searchTerm = inputField.value.trim();
            if (searchTerm) {
                console.log(`🔍 Animal search: ${searchType} = "${searchTerm}"`);
                this.animalPageController.performFieldSearch(searchTerm, searchType);
                this.hideDropdown();
                // Clear the input field
                inputField.value = '';
            } else {
                // Show error or highlight empty field
                inputField.style.borderColor = '#ff6b6b';
                setTimeout(() => {
                    inputField.style.borderColor = '#c1d8b9';
                }, 1000);
            }
        }
    }

    /**
     * Cancel search and close input
     */
    cancelSearch() {
        // Clear all input fields
        this.dropdownContainer.querySelectorAll('.search-input-dynamic').forEach(input => {
            input.value = '';
        });

        // Hide all input containers
        this.dropdownContainer.querySelectorAll('.search-input-container-dynamic').forEach(container => {
            container.style.display = 'none';
        });

        // Remove active class from all options
        this.dropdownContainer.querySelectorAll('.search-option-dynamic').forEach(opt => {
            opt.classList.remove('active');
        });

        // Remove expanded class
        this.dropdownContainer.querySelector('.search-dropdown-menu-dynamic').classList.remove('expanded');
    }

    /**
     * Handle click outside search dropdown
     * @param {Event} e - Click event
     */
    handleSearchOutsideClick(e) {
        if (this.isVisible && this.dropdownContainer && !this.dropdownContainer.contains(e.target)) {
            this.hideDropdown();
        }
    }

    /**
     * Show dropdown with animation
     */
    showDropdownWithAnimation() {
        if (this.dropdownContainer) {
            this.isVisible = true;
            setTimeout(() => {
                this.dropdownContainer.style.opacity = '1';
                this.dropdownContainer.style.transform = 'translateY(0)';
            }, 10);
        }
    }

    /**
     * Hide dropdown
     */
    hideDropdown() {
        if (this.dropdownContainer && this.isVisible) {
            this.dropdownContainer.style.opacity = '0';
            this.dropdownContainer.style.transform = 'translateY(-10px)';

            // Remove active class from search container
            const searchContainer = document.querySelector('.search-dropdown-container.active');
            if (searchContainer) {
                searchContainer.classList.remove('active');
            }

            setTimeout(() => {
                if (this.dropdownContainer && this.dropdownContainer.parentNode) {
                    this.dropdownContainer.parentNode.removeChild(this.dropdownContainer);
                    this.dropdownContainer = null;
                }
            }, 300);

            this.isVisible = false;

            // Remove event listener
            document.removeEventListener('click', this.handleSearchOutsideClick.bind(this));
        }
    }
}

// ==================== MAIN CONTROLLER ====================

/**
 * Main Animal Page Controller
 * Orchestrates all components and manages the animal page functionality
 */
class AnimalPageController extends BaseAnimalDataManager {
    constructor() {
        super();
        this.popupManager = new AnimalPopupManager();
        this.updateDropdownManager = new UpdateDropdownManager(this);
        this.filterDropdownManager = new FilterDropdownManager(this);
        this.searchDropdownManager = new SearchDropdownManager(this);
        this.currentSearchTerm = '';
        this.currentSearchField = null;
        this.currentFilters = {};
        this.isInitialized = false;
    }

    /**
     * Initialize the animal page controller
     */
    init() {
        try {
            console.log('🚀 Initializing Animal Page Controller...');

            this.setupPageElements();
            this.setupEventListeners();
            this.loadAnimalTable();
            this.setupStorageEventListeners();

            this.isInitialized = true;
            console.log('✅ Animal Page Controller initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize Animal Page Controller:', error);
            this.showNotification('Failed to initialize the animal page. Please refresh the page.', 'error');
        }
    }

    /**
     * Setup page elements
     */
    setupPageElements() {
        // Get all buttons in the frame-4 container
        const allButtons = document.querySelectorAll('.frame-4 button');

        this.elements = {
            // Buttons - identify by .btntext content (matching actual HTML structure)
            addNewAnimalBtn: Array.from(allButtons).find(btn => {
                const btntext = btn.querySelector('.btntext');
                return btntext && btntext.textContent.trim() === 'Add New Animal';
            }),
            updateDataBtn: Array.from(allButtons).find(btn => {
                const btntext = btn.querySelector('.btntext');
                return btntext && btntext.textContent.trim() === 'Updating Data';
            }),
            searchBtn: Array.from(allButtons).find(btn => {
                return btn.classList.contains('search') ||
                       (btn.querySelector('.btntext1') && btn.querySelector('.btntext1').textContent.trim() === 'Search');
            }),
            filterBtn: Array.from(allButtons).find(btn => {
                const btntext = btn.querySelector('.btntext');
                return btntext && btntext.textContent.trim() === 'Filter';
            }),
            fatteningWeightBtn: Array.from(allButtons).find(btn => {
                const btntext = btn.querySelector('.btntext');
                return btntext && btntext.textContent.trim() === 'Fattening Weight';
            }),
            deleteBtn: Array.from(allButtons).find(btn => {
                const btntext = btn.querySelector('.btntext');
                return btntext && btntext.textContent.trim() === 'Delete Animal';
            }),

            // Search input - not needed for current implementation
            searchInput: null,

            // Animal table
            animalTable: document.querySelector('table'),
            animalTableBody: document.querySelector('tbody')
        };

        // Validate that elements exist
        const missingElements = [];
        Object.entries(this.elements).forEach(([key, element]) => {
            if (!element) {
                missingElements.push(key);
                console.warn(`Element not found: ${key}`);
            }
        });

        if (missingElements.length > 0) {
            console.warn(`Some page elements not found: ${missingElements.join(', ')}`);
        }

        console.log('📋 Page elements setup complete');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Add New Animal button
        if (this.elements.addNewAnimalBtn) {
            this.elements.addNewAnimalBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleAddNewAnimal();
            });
        }

        // Update Data button (shows dropdown)
        if (this.elements.updateDataBtn) {
            this.elements.updateDataBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleUpdateData(this.elements.updateDataBtn);
            });
        }

        // Search button (shows dropdown)
        if (this.elements.searchBtn) {
            this.elements.searchBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleSearchDropdown(this.elements.searchBtn);
            });
        }

        // Filter button (shows dropdown)
        if (this.elements.filterBtn) {
            this.elements.filterBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleFilter(this.elements.filterBtn);
            });
        }

        // Fattening Weight button
        if (this.elements.fatteningWeightBtn) {
            this.elements.fatteningWeightBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFatteningWeight();
            });
        }

        // Delete button
        if (this.elements.deleteBtn) {
            this.elements.deleteBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleDelete();
            });
        }

        // Search input - real-time search (if available)
        if (this.elements.searchInput) {
            this.elements.searchInput.addEventListener('input', (e) => {
                this.handleRealTimeSearch(e.target.value);
            });

            this.elements.searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleSearch();
                }
            });
        }

        console.log('🔗 Event listeners setup complete');
    }

    /**
     * Setup storage event listeners to refresh table when data changes
     */
    setupStorageEventListeners() {
        window.addEventListener('storage', (e) => {
            if (e.key === 'animals' || e.key === 'dairyAnimals' || e.key === 'newbornAnimals') {
                console.log('📊 Storage updated, refreshing animal table');
                this.refreshAnimalTable();
            }
        });
    }

    /**
     * Handle Add New Animal button click
     */
    handleAddNewAnimal() {
        console.log('➕ Add New Animal button clicked');
        this.popupManager.showPopup('addnewanimal');
    }

    /**
     * Handle Update Data button click (show dropdown)
     * @param {HTMLElement} buttonElement - Button element
     */
    handleUpdateData(buttonElement) {
        console.log('🔄 Update Data button clicked');
        this.updateDropdownManager.showDropdown(buttonElement);
    }

    /**
     * Handle Search button click (show dropdown)
     * @param {HTMLElement} buttonElement - Button element
     */
    handleSearchDropdown(buttonElement) {
        console.log('🔍 Search button clicked - showing dropdown');
        this.searchDropdownManager.showDropdown(buttonElement);
    }

    /**
     * Handle Search button click (legacy method for fallback)
     */
    handleSearch() {
        // Show a simple prompt for search term
        const searchTerm = prompt('Enter search term (animal code, type, herd number, etc.):');

        if (searchTerm !== null) { // User didn't cancel
            console.log(`🔍 Search button clicked with term: "${searchTerm}"`);

            this.currentSearchTerm = searchTerm.trim();
            this.currentSearchField = null;
            this.refreshAnimalTable();

            if (this.currentSearchTerm) {
                this.showNotification(`Searching for: "${this.currentSearchTerm}"`, 'info', 2000);
            } else {
                this.showNotification('Showing all animals', 'info', 2000);
            }
        }
    }

    /**
     * Perform field-specific search
     * @param {string} searchTerm - Search term
     * @param {string} searchField - Field to search in
     */
    performFieldSearch(searchTerm, searchField) {
        console.log(`🔍 Performing field search: ${searchField} = "${searchTerm}"`);

        this.currentSearchTerm = searchTerm;
        this.currentSearchField = searchField;
        this.refreshAnimalTable();

        const fieldName = searchField.charAt(0).toUpperCase() + searchField.slice(1);
        this.showNotification(`Searching ${fieldName}: "${searchTerm}"`, 'info', 2000);
    }

    /**
     * Clear search and show all animals
     */
    clearSearch() {
        console.log('🔄 Clearing search');

        this.currentSearchTerm = '';
        this.currentSearchField = null;
        this.refreshAnimalTable();

        this.showNotification('Showing all animals', 'info', 2000);
    }

    /**
     * Handle real-time search as user types
     * @param {string} searchTerm - Search term
     */
    handleRealTimeSearch(searchTerm) {
        // Debounce the search
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        this.searchTimeout = setTimeout(() => {
            this.currentSearchTerm = searchTerm;
            this.refreshAnimalTable();
        }, 300);
    }

    /**
     * Handle Filter button click (show dropdown)
     * @param {HTMLElement} buttonElement - Button element
     */
    handleFilter(buttonElement) {
        console.log('🔽 Filter button clicked');
        this.filterDropdownManager.showDropdown(buttonElement);
    }

    /**
     * Handle Fattening Weight button click
     */
    handleFatteningWeight() {
        console.log('⚖️ Fattening Weight button clicked');
        this.popupManager.showPopup('updatefatteningweight');
    }

    /**
     * Handle Delete button click
     */
    handleDelete() {
        console.log('🗑️ Delete button clicked');

        // Get selected animals from checkboxes
        const selectedAnimals = this.getSelectedAnimals();

        if (selectedAnimals.length === 0) {
            this.showNotification('Please select animals to delete', 'warning');
            return;
        }

        console.log(`Found ${selectedAnimals.length} selected animals:`, selectedAnimals);

        // Use the delete confirmation system
        if (window.showDeleteAnimalsConfirmation) {
            window.showDeleteAnimalsConfirmation(
                selectedAnimals,
                (deletedItems) => {
                    console.log(`✅ Successfully deleted ${deletedItems.length} animals`);

                    // Refresh the animal table
                    this.refreshAnimalTable();

                    // Clear all checkboxes
                    this.clearAllCheckboxes();

                    // Show success notification
                    this.showNotification(`Successfully deleted ${deletedItems.length} animal(s)`, 'success');

                    // Trigger storage events for other components
                    window.dispatchEvent(new Event('animalDataChanged'));
                },
                () => {
                    console.log('🚫 Animal deletion cancelled');
                }
            );
        } else if (window.deleteSelectedAnimalsFromPage) {
            // Fallback to page-level delete function
            window.deleteSelectedAnimalsFromPage();
        } else {
            // Final fallback: simple confirmation
            if (confirm(`Are you sure you want to delete ${selectedAnimals.length} selected animal(s)? This action cannot be undone.`)) {
                this.deleteSelectedAnimals(selectedAnimals);
            }
        }
    }

    /**
     * Get selected animals from checkboxes
     * @returns {Array} - Array of selected animals
     */
    getSelectedAnimals() {
        const checkboxes = document.querySelectorAll('.checkbox:checked');
        const allAnimals = this.getAllAnimals();
        const selectedAnimals = [];

        checkboxes.forEach(checkbox => {
            const animalCode = checkbox.getAttribute('data-code');
            const animalId = checkbox.getAttribute('data-id');

            // Find the animal by code or id
            const animal = allAnimals.find(a =>
                a.code === animalCode ||
                a.id === animalId ||
                a.id === animalCode
            );

            if (animal) {
                selectedAnimals.push(animal);
            }
        });

        return selectedAnimals;
    }

    /**
     * Clear all checkboxes
     */
    clearAllCheckboxes() {
        const checkboxes = document.querySelectorAll('.checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        console.log('✅ All checkboxes cleared');
    }

    /**
     * Delete selected animals (fallback method)
     * @param {Array} animals - Animals to delete
     */
    deleteSelectedAnimals(animals) {
        try {
            let deletedCount = 0;

            animals.forEach(animal => {
                // Remove from main animals table
                const allAnimals = this.getAllAnimals();
                const updatedAnimals = allAnimals.filter(a => a.id !== animal.id && a.code !== animal.code);
                this.saveData('animals', updatedAnimals);

                // Remove from type-specific tables
                if (animal.type === 'dairy') {
                    const dairyAnimals = this.getData('dairy');
                    const updatedDairy = dairyAnimals.filter(a => a.id !== animal.id && a.code !== animal.code);
                    this.saveData('dairy', updatedDairy);
                } else if (animal.type === 'newborn') {
                    const newbornAnimals = this.getData('newborn');
                    const updatedNewborn = newbornAnimals.filter(a => a.id !== animal.id && a.code !== animal.code);
                    this.saveData('newborn', updatedNewborn);
                }

                deletedCount++;
            });

            // Refresh the table
            this.refreshAnimalTable();

            // Show success message
            this.showNotification(`Successfully deleted ${deletedCount} animal(s)`, 'success');

            console.log(`✅ Deleted ${deletedCount} animals successfully`);

        } catch (error) {
            console.error('❌ Error deleting animals:', error);
            this.showNotification('Error deleting animals. Please try again.', 'error');
        }
    }



    /**
     * Apply filters to animal table
     * @param {Object} filters - Filter criteria
     */
    applyFilters(filters) {
        console.log('🔽 Applying filters:', filters);
        this.currentFilters = filters;
        this.refreshAnimalTable();

        const filterCount = Object.keys(filters).length;
        if (filterCount > 0) {
            this.showNotification(`Applied ${filterCount} filter(s)`, 'success', 2000);
        }
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        console.log('🔄 Clearing all filters');
        this.currentFilters = {};
        this.refreshAnimalTable();
        this.showNotification('All filters cleared', 'info', 2000);
    }

    /**
     * Load and display animal table
     */
    loadAnimalTable() {
        console.log('📊 Loading animal table...');
        this.refreshAnimalTable();
    }

    /**
     * Refresh animal table with current search and filter criteria
     */
    refreshAnimalTable() {
        try {
            // Get animals based on current search and filters
            let animals = this.getAllAnimals();

            // Apply search filter
            if (this.currentSearchTerm) {
                if (this.currentSearchField) {
                    // Field-specific search
                    animals = this.searchAnimalsByField(this.currentSearchTerm, this.currentSearchField);
                } else {
                    // General search
                    animals = this.searchAnimals(this.currentSearchTerm);
                }
            }

            // Apply additional filters
            if (Object.keys(this.currentFilters).length > 0) {
                animals = this.filterAnimals(this.currentFilters);
            }

            // Update table display
            this.updateAnimalTableDisplay(animals);

            console.log(`📊 Animal table refreshed with ${animals.length} animals`);
        } catch (error) {
            console.error('❌ Error refreshing animal table:', error);
            this.showNotification('Error loading animal data', 'error');
        }
    }

    /**
     * Search animals by specific field
     * @param {string} searchTerm - Search term
     * @param {string} searchField - Field to search in
     * @returns {Array} - Matching animals
     */
    searchAnimalsByField(searchTerm, searchField) {
        if (!searchTerm) {
            return this.getAllAnimals();
        }

        const animals = this.getAllAnimals();
        const term = searchTerm.toLowerCase();

        return animals.filter(animal => {
            let fieldValue;

            switch (searchField) {
                case 'code':
                    fieldValue = animal.code;
                    break;
                case 'type':
                    fieldValue = animal.type;
                    break;
                case 'herdNumber':
                    fieldValue = animal.herdNumber;
                    break;
                case 'gender':
                    fieldValue = animal.gender;
                    break;
                case 'weight':
                    fieldValue = animal.weight;
                    break;
                case 'healthcareNotes':
                    fieldValue = animal.healthcareNotes;
                    break;
                default:
                    // Fallback to general search
                    return this.searchAnimals(searchTerm).includes(animal);
            }

            return fieldValue && fieldValue.toString().toLowerCase().includes(term);
        });
    }

    /**
     * Update animal table display
     * @param {Array} animals - Animals to display
     */
    updateAnimalTableDisplay(animals) {
        if (!this.elements.animalTableBody) {
            console.warn('Animal table body not found');
            return;
        }

        // Clear existing table content
        this.elements.animalTableBody.innerHTML = '';

        if (animals.length === 0) {
            // Show empty state
            this.showEmptyTableState();
            return;
        }

        // Create table rows
        animals.forEach((animal) => {
            const row = this.createAnimalTableRow(animal);
            this.elements.animalTableBody.appendChild(row);
        });

        // Update table statistics
        this.updateTableStatistics(animals);
    }

    /**
     * Create animal table row
     * @param {Object} animal - Animal data
     * @returns {HTMLElement} - Table row element
     */
    createAnimalTableRow(animal) {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <input type="checkbox" class="checkbox" data-code="${animal.code}" data-id="${animal.id || animal.code}">
                ${animal.code || 'N/A'}
            </td>
            <td>${animal.type || 'N/A'}</td>
            <td>${animal.herdNumber || 'N/A'}</td>
            <td>${animal.gender || 'N/A'}</td>
            <td>${animal.weight ? animal.weight + 'kg' : 'N/A'}</td>
            <td>${animal.dateOfWeight || animal.dateOfBirth || 'N/A'}</td>
            <td>${animal.healthcareNotes || 'N/A'}</td>
        `;

        // Add hover effect
        row.addEventListener('mouseenter', () => {
            row.style.backgroundColor = '#f8f9fa';
        });

        row.addEventListener('mouseleave', () => {
            row.style.backgroundColor = '';
        });

        // Add click handler for row selection
        row.addEventListener('click', (e) => {
            if (e.target.type !== 'checkbox') {
                const checkbox = row.querySelector('.checkbox');
                if (checkbox) {
                    checkbox.checked = !checkbox.checked;
                }
            }
        });

        return row;
    }

    /**
     * Show empty table state
     */
    showEmptyTableState() {
        const emptyRow = document.createElement('tr');
        emptyRow.className = 'empty-table-state';
        emptyRow.innerHTML = `
            <td colspan="7" style="text-align: center; padding: 40px; color: #666;">
                <div style="font-size: 48px; margin-bottom: 16px;">🐄</div>
                <div style="font-size: 18px; font-weight: bold; margin-bottom: 8px;">No animals found</div>
                <div style="font-size: 14px;">
                    ${this.currentSearchTerm ? `No animals match "${this.currentSearchTerm}"` :
                      Object.keys(this.currentFilters).length > 0 ? 'No animals match the current filters' :
                      'No animals have been added yet'}
                </div>
                ${!this.currentSearchTerm && Object.keys(this.currentFilters).length === 0 ?
                  '<button style="margin-top: 16px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;" onclick="window.animalPageController.handleAddNewAnimal()">Add First Animal</button>' :
                  ''}
            </td>
        `;
        this.elements.animalTableBody.appendChild(emptyRow);
    }

    /**
     * Update table statistics
     * @param {Array} animals - Current animals being displayed
     */
    updateTableStatistics(animals) {
        const stats = this.getAnimalStatistics();

        // Update any statistics display elements if they exist
        const statsElement = document.querySelector('.animal-stats');
        if (statsElement) {
            statsElement.innerHTML = `
                <div>Total: ${animals.length} animals</div>
                <div>Dairy: ${stats.byType.dairy || 0}</div>
                <div>Newborn: ${stats.byType.newborn || 0}</div>
                <div>Fattening: ${stats.byType.fattening || 0}</div>
            `;
        }
    }

    /**
     * Show notification message
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, warning, info)
     * @param {number} duration - Auto-dismiss duration in milliseconds
     */
    showNotification(message, type = 'info', duration = 4000) {
        // Use the popup manager's notification system
        this.popupManager.showNotification(message, type, duration);
    }

    /**
     * Get current page statistics
     * @returns {Object} - Page statistics
     */
    getPageStatistics() {
        const stats = this.getAnimalStatistics();
        return {
            ...stats,
            currentSearch: this.currentSearchTerm,
            activeFilters: Object.keys(this.currentFilters).length,
            isFiltered: this.currentSearchTerm || Object.keys(this.currentFilters).length > 0
        };
    }
}

// ==================== INITIALIZATION ====================

/**
 * Initialize the Animal Page Controller when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing Animal Page Controller...');

    try {
        // Create and initialize the controller
        const animalPageController = new AnimalPageController();
        animalPageController.init();

        // Make it globally accessible for debugging and external access
        window.animalPageController = animalPageController;

        console.log('🎉 Animal Page Controller ready!');
        console.log('Available global methods:');
        console.log('- window.animalPageController.refreshAnimalTable()');
        console.log('- window.animalPageController.getPageStatistics()');
        console.log('- window.animalPageController.searchAnimals(term)');
        console.log('- window.animalPageController.filterAnimals(filters)');

    } catch (error) {
        console.error('💥 Failed to initialize Animal Page Controller:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        errorDiv.textContent = 'Failed to initialize the animal page. Please refresh the page.';
        document.body.appendChild(errorDiv);
    }
});

// ==================== UTILITY FUNCTIONS ====================

/**
 * Refresh animal table (utility function)
 */
window.refreshAnimalTable = function() {
    if (window.animalPageController) {
        window.animalPageController.refreshAnimalTable();
    } else {
        console.error('Animal Page Controller not initialized');
    }
};

/**
 * Search animals (utility function)
 * @param {string} searchTerm - Search term
 * @returns {Array} - Matching animals
 */
window.searchAnimals = function(searchTerm) {
    if (window.animalPageController) {
        return window.animalPageController.searchAnimals(searchTerm);
    } else {
        console.error('Animal Page Controller not initialized');
        return [];
    }
};

/**
 * Filter animals (utility function)
 * @param {Object} filters - Filter criteria
 * @returns {Array} - Filtered animals
 */
window.filterAnimals = function(filters) {
    if (window.animalPageController) {
        return window.animalPageController.filterAnimals(filters);
    } else {
        console.error('Animal Page Controller not initialized');
        return [];
    }
};

/**
 * Get all animals (utility function)
 * @returns {Array} - All animals
 */
window.getAllAnimals = function() {
    if (window.animalPageController) {
        return window.animalPageController.getAllAnimals();
    } else {
        console.error('Animal Page Controller not initialized');
        return [];
    }
};

/**
 * Get animal statistics (utility function)
 * @returns {Object} - Animal statistics
 */
window.getAnimalStatistics = function() {
    if (window.animalPageController) {
        return window.animalPageController.getAnimalStatistics();
    } else {
        console.error('Animal Page Controller not initialized');
        return {};
    }
};

/**
 * Show popup (utility function)
 * @param {string} popupType - Type of popup to show
 * @param {Object} options - Popup options
 */
window.showAnimalPopup = function(popupType, options = {}) {
    if (window.animalPageController && window.animalPageController.popupManager) {
        window.animalPageController.popupManager.showPopup(popupType, options);
    } else {
        console.error('Animal Page Controller or Popup Manager not initialized');
    }
};

/**
 * Close popup (utility function) - Can be called from iframe content
 * This is the main method that iframe popups should use to close themselves
 */
window.closeAnimalPopup = function() {
    console.log('🔴 Global close popup called');
    if (window.animalPageController && window.animalPageController.popupManager) {
        window.animalPageController.popupManager.hidePopup();
        console.log('✅ Popup closed via global method');
    } else {
        console.error('Animal Page Controller or Popup Manager not initialized');
    }
};

/**
 * Listen for postMessage from iframe to close popup
 * This solves cross-origin restrictions with file:// URLs
 */
window.addEventListener('message', function(event) {
    console.log('📨 PostMessage received:', event.data);

    if (event.data && event.data.action === 'closePopup') {
        console.log('🔴 PostMessage close popup request received');

        try {
            // Try to close via popup manager
            if (window.animalPageController && window.animalPageController.popupManager) {
                window.animalPageController.popupManager.hidePopup();
                console.log('✅ PostMessage: Popup closed via manager');
            } else {
                // Fallback: Direct DOM manipulation
                const popupOverlay = document.querySelector('.animal-popup-overlay');
                if (popupOverlay) {
                    popupOverlay.style.display = 'none';
                    console.log('✅ PostMessage: Popup closed via direct DOM');
                } else {
                    console.warn('⚠️ PostMessage: No popup overlay found');
                }
            }
        } catch (error) {
            console.error('❌ PostMessage close error:', error);
        }
    }
});

/**
 * Apply filters programmatically (utility function)
 * @param {Object} filters - Filter criteria
 */
window.applyAnimalFilters = function(filters) {
    if (window.animalPageController) {
        window.animalPageController.applyFilters(filters);
    } else {
        console.error('Animal Page Controller not initialized');
    }
};

/**
 * Clear all filters (utility function)
 */
window.clearAnimalFilters = function() {
    if (window.animalPageController) {
        window.animalPageController.clearFilters();
    } else {
        console.error('Animal Page Controller not initialized');
    }
};

/**
 * Get selected animals from checkboxes (utility function)
 * @returns {Array} - Selected animals
 */
window.getSelectedAnimals = function() {
    if (window.animalPageController) {
        const allAnimals = window.animalPageController.getAllAnimals();
        return window.getSelectedItemsFromCheckboxes('.checkbox:checked', allAnimals);
    } else {
        console.error('Animal Page Controller not initialized');
        return [];
    }
};

// ==================== DEBUGGING FUNCTIONS ====================

/**
 * Debug function to check system status
 */
window.debugAnimalPage = function() {
    console.log('=== Animal Page Debug Info ===');

    if (!window.animalPageController) {
        console.error('❌ Animal Page Controller not initialized');
        return;
    }

    console.log('✅ System initialized');
    console.log('📊 Page Statistics:', window.animalPageController.getPageStatistics());
    console.log('🔍 Current search term:', window.animalPageController.currentSearchTerm);
    console.log('🔽 Current filters:', window.animalPageController.currentFilters);

    // Check page elements
    const elements = window.animalPageController.elements;
    console.log('📋 Page elements:');
    Object.entries(elements).forEach(([key, element]) => {
        if (element) {
            console.log(`  ✅ ${key}: Found`);
        } else {
            console.log(`  ❌ ${key}: Missing`);
        }
    });

    // Check popup manager
    if (window.animalPageController.popupManager) {
        console.log('✅ Popup Manager: Available');
        console.log('  Current popup visible:', window.animalPageController.popupManager.isPopupVisible);
    } else {
        console.log('❌ Popup Manager: Not available');
    }

    console.log('=== End Debug Info ===');
};

/**
 * Test function to create sample animals
 */
window.createSampleAnimals = function() {
    if (!window.animalPageController) {
        console.error('Animal Page Controller not initialized');
        return;
    }

    const sampleAnimals = [
        {
            id: 'sample1',
            code: 'DAIRY001',
            type: 'dairy',
            gender: 'female',
            herdNumber: 'H001',
            weight: '450',
            dateOfBirth: '2022-03-15',
            healthcareNotes: 'Healthy dairy cow'
        },
        {
            id: 'sample2',
            code: 'NEWBORN001',
            type: 'newborn',
            gender: 'male',
            herdNumber: 'H002',
            weight: '45',
            dateOfBirth: '2024-01-10',
            healthcareNotes: 'Young calf'
        },
        {
            id: 'sample3',
            code: 'FATTENING001',
            type: 'fattening',
            gender: 'male',
            herdNumber: 'H003',
            weight: '320',
            dateOfBirth: '2023-06-20',
            healthcareNotes: 'Growing well'
        }
    ];

    // Save to localStorage
    const existingAnimals = JSON.parse(localStorage.getItem('animals') || '[]');
    const updatedAnimals = [...existingAnimals, ...sampleAnimals];
    localStorage.setItem('animals', JSON.stringify(updatedAnimals));

    // Refresh table
    window.animalPageController.refreshAnimalTable();

    console.log('✅ Sample animals created and table refreshed');
    window.animalPageController.showNotification('Sample animals created successfully!', 'success');
};

/**
 * Test function to clear all animals
 */
window.clearAllAnimals = function() {
    if (confirm('Are you sure you want to clear all animals? This cannot be undone.')) {
        localStorage.removeItem('animals');
        localStorage.removeItem('dairyAnimals');
        localStorage.removeItem('newbornAnimals');
        localStorage.removeItem('fatteningWeights');
        localStorage.removeItem('milkProduction');

        if (window.animalPageController) {
            window.animalPageController.refreshAnimalTable();
            window.animalPageController.showNotification('All animals cleared', 'info');
        }

        console.log('🗑️ All animals cleared from storage');
    }
};

/**
 * Test function to show popup
 * @param {string} popupType - Type of popup to test
 */
window.testPopup = function(popupType = 'addnewanimal') {
    if (window.animalPageController && window.animalPageController.popupManager) {
        window.animalPageController.popupManager.showPopup(popupType);
        console.log(`🧪 Testing ${popupType} popup`);
    } else {
        console.error('Animal Page Controller or Popup Manager not initialized');
    }
};

// ==================== GLOBAL FUNCTIONS FOR HTML ONCLICK HANDLERS ====================

/**
 * Global function for animal search (called from HTML onclick)
 * @param {string} searchType - Type of search
 */
window.performAnimalSearch = function(searchType) {
    console.log(`🔍 performAnimalSearch called with type: ${searchType}`);

    if (window.animalPageController && window.animalPageController.searchDropdownManager) {
        window.animalPageController.searchDropdownManager.performSearch(searchType);
    } else {
        console.error('❌ Animal Page Controller or Search Dropdown Manager not available');
    }
};

/**
 * Global function to cancel animal search (called from HTML onclick)
 */
window.cancelAnimalSearch = function() {
    console.log('🔄 cancelAnimalSearch called');

    if (window.animalPageController && window.animalPageController.searchDropdownManager) {
        window.animalPageController.searchDropdownManager.cancelSearch();
    } else {
        console.error('❌ Animal Page Controller or Search Dropdown Manager not available');
    }
};

console.log('✅ Animal page controller system loaded successfully');
console.log('Available functions:');
console.log('- window.refreshAnimalTable()');
console.log('- window.searchAnimals(term)');
console.log('- window.filterAnimals(filters)');
console.log('- window.getAllAnimals()');
console.log('- window.getAnimalStatistics()');
console.log('- window.showAnimalPopup(type, options)');
console.log('- window.performAnimalSearch(type) - for search dropdown');
console.log('- window.cancelAnimalSearch() - for search dropdown');
console.log('- window.debugAnimalPage() - for debugging');
console.log('- window.createSampleAnimals() - for testing');
console.log('- window.testPopup(type) - for testing popups');