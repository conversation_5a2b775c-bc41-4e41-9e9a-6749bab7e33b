import { Injectable } from '@angular/core';
import { ApiService } from './api.service';
import { Observable } from 'rxjs';
import { Animal } from '../models/animal.model';

@Injectable({
  providedIn: 'root'
})
export class AnimalService {
  constructor(private apiService: ApiService) { }

  // Get all animals
  getAllAnimals(): Observable<Animal[]> {
    return this.apiService.get<Animal[]>('/api/animals');
  }

  // Get animal by ID
  getAnimalById(id: string): Observable<Animal> {
    return this.apiService.get<Animal>(`/api/animals/${id}`);
  }

  // Create new animal
  createAnimal(animalData: Partial<Animal>): Observable<Animal> {
    return this.apiService.post<Animal>('/api/animals', animalData);
  }

  // Update animal
  updateAnimal(id: string, animalData: Partial<Animal>): Observable<Animal> {
    return this.apiService.put<Animal>(`/api/animals/${id}`, animalData);
  }

  // Delete animal
  deleteAnimal(id: string): Observable<any> {
    return this.apiService.delete<any>(`/api/animals/${id}`);
  }

  // Search animals
  searchAnimals(searchTerm: string): Observable<Animal[]> {
    return this.apiService.get<Animal[]>(`/api/animals/search?term=${searchTerm}`);
  }

  // Filter animals by type
  getAnimalsByType(type: string): Observable<Animal[]> {
    return this.apiService.get<Animal[]>(`/api/animals/type/${type}`);
  }
}

