/**
 * AnimalTableManager class
 * This file contains the implementation of the Animal Table Manager
 * for handling table operations
 */

class AnimalTableManager {
    // Add a new animal to the table
    static addAnimal(animalData) {
        const tableBody = document.querySelector('table tbody');
        if (!tableBody) return;
        
        const row = document.createElement('tr');
        
        // Create checkbox cell
        const checkboxCell = document.createElement('td');
        checkboxCell.innerHTML = `
            <div>
                <form>
                    <label class="selectbox">
                        <input class="checkbox" type="checkbox">${animalData.code}
                    </label>
                </form>
            </div>
        `;
        row.appendChild(checkboxCell);
        
        // Add other cells
        row.appendChild(this.createCell(animalData.type));
        row.appendChild(this.createCell(animalData.herdNumber));
        row.appendChild(this.createCell(animalData.gender));
        row.appendChild(this.createCell(animalData.weight));
        row.appendChild(this.createCell(animalData.dateOfWeight));
        row.appendChild(this.createCell(animalData.healthcareNotes));
        
        tableBody.appendChild(row);
    }
    
    // Create a table cell
    static createCell(content) {
        const cell = document.createElement('td');
        cell.textContent = content || '';
        return cell;
    }
    
    // Update an animal in the table
    static updateAnimal(animalData) {
        const rows = document.querySelectorAll('table tbody tr');
        for (const row of rows) {
            const codeCell = row.querySelector('td:first-child');
            if (codeCell && codeCell.textContent.includes(animalData.code)) {
                // Update cells
                if (animalData.type) row.cells[1].textContent = animalData.type;
                if (animalData.herdNumber) row.cells[2].textContent = animalData.herdNumber;
                if (animalData.gender) row.cells[3].textContent = animalData.gender;
                if (animalData.weight) row.cells[4].textContent = animalData.weight;
                if (animalData.dateOfWeight) row.cells[5].textContent = animalData.dateOfWeight;
                if (animalData.healthcareNotes) row.cells[6].textContent = animalData.healthcareNotes;
                break;
            }
        }
    }
    
    // Update animal weight
    static updateAnimalWeight(weightData) {
        const rows = document.querySelectorAll('table tbody tr');
        for (const row of rows) {
            const codeCell = row.querySelector('td:first-child');
            if (codeCell && codeCell.textContent.includes(weightData.code)) {
                // Update weight and date
                row.cells[4].textContent = weightData.weight;
                row.cells[5].textContent = weightData.dateOfWeight;
                if (weightData.healthcareNote) row.cells[6].textContent = weightData.healthcareNote;
                break;
            }
        }
    }
    
    // Delete an animal from the table
    static deleteAnimal(animalCode) {
        const rows = document.querySelectorAll('table tbody tr');
        for (const row of rows) {
            const codeCell = row.querySelector('td:first-child');
            if (codeCell && codeCell.textContent.includes(animalCode)) {
                row.remove();
                break;
            }
        }
    }
    
    // Get selected animal codes
    static getSelectedAnimalCodes() {
        const selectedCodes = [];
        const checkboxes = document.querySelectorAll('table tbody input.checkbox:checked');
        checkboxes.forEach(checkbox => {
            const code = checkbox.parentNode.textContent.trim();
            selectedCodes.push(code);
        });
        return selectedCodes;
    }
}
