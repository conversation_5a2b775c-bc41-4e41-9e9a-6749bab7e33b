document.addEventListener('DOMContentLoaded', function() {
    // Get elements
    const addNewVaccinationBtn = document.querySelector('.frame-9:nth-child(1)');
    const updateVaccinationBtn = document.querySelector('.frame-9:nth-child(2)');
    const searchBtn = document.getElementById('searchButton');
    const searchDropdown = document.getElementById('searchDropdown');
    const searchContainer = document.querySelector('.search-dropdown-container');
    const filterBtn = document.getElementById('filterButton');
    const filterDropdown = document.getElementById('filterDropdown');
    const filterContainer = document.querySelector('.filter-dropdown-container');
    // Find delete button by text content to avoid nth-child issues
    const deleteVaccinationBtn = Array.from(document.querySelectorAll('.frame-9')).find(btn =>
        btn.textContent.includes('Delete Vaccination')
    );
    const tableBody = document.querySelector('tbody');
    

    let currentFilter = 'all'; // Track current filter state

    // Initialize popup manager
    const popupManager = new VaccinationPopupManager();
    window.vaccinationPopupManager = popupManager;

    // Load vaccinations when page loads
    loadVaccinations();

    // Add event listeners to buttons
    addNewVaccinationBtn.addEventListener('click', function() {
        // Replace navigation with popup
        popupManager.showPopup('addnewvaccination');
    });

    updateVaccinationBtn.addEventListener('click', function() {
        const selectedCheckbox = document.querySelector('.checkbox:checked');
        if (!selectedCheckbox) {
            alert('Please select a vaccination to update');
            return;
        }

        // Get the index of the selected vaccination
        const vaccinationIndex = selectedCheckbox.value;

        // Store the selected vaccination index in sessionStorage
        sessionStorage.setItem('updateVaccinationId', vaccinationIndex);

        // Show the update vaccination popup instead of navigating
        popupManager.showPopup('updatevaccination');
    });

    // Search dropdown functionality
    searchBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        toggleSearchDropdown();
    });

    // Search option selection
    const searchOptions = document.querySelectorAll('.search-option');
    searchOptions.forEach(option => {
        option.addEventListener('click', function() {
            const searchType = this.getAttribute('data-search');
            selectSearchOption(this, searchType);
        });
    });

    // Close search dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!searchContainer.contains(e.target)) {
            closeSearchDropdown();
        }
    });

    // Close search dropdown on escape key and handle enter key for search inputs
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeSearchDropdown();
        } else if (e.key === 'Enter' && e.target.classList.contains('search-input')) {
            // Handle Enter key in search input fields
            const inputId = e.target.id;
            const searchType = inputId.replace('SearchInput', '');
            window.performSearch(searchType);
        }
    });

    // Filter dropdown functionality
    filterBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        toggleFilterDropdown();
    });

    // Filter option selection
    const filterOptions = document.querySelectorAll('.filter-option');
    filterOptions.forEach(option => {
        option.addEventListener('click', function() {
            const filterType = this.getAttribute('data-filter');
            selectFilterOption(this, filterType);
        });
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(e) {
        if (!filterContainer.contains(e.target)) {
            closeFilterDropdown();
        }
    });

    // Close dropdown on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeFilterDropdown();
        }
    });

    deleteVaccinationBtn.addEventListener('click', function() {
        const selectedCheckboxes = document.querySelectorAll('.checkbox:checked');
        if (selectedCheckboxes.length === 0) {
            alert('Please select at least one vaccination to delete');
            return;
        }

        if (confirm(`Are you sure you want to delete ${selectedCheckboxes.length} vaccination(s)?`)) {
            deleteSelectedVaccinations(selectedCheckboxes);
        }
    });

    // Search dropdown functions
    function toggleSearchDropdown() {
        const isOpen = searchDropdown.classList.contains('show');
        if (isOpen) {
            closeSearchDropdown();
        } else {
            openSearchDropdown();
        }
    }

    function openSearchDropdown() {
        searchDropdown.classList.add('show');
        searchContainer.classList.add('active');
    }

    function closeSearchDropdown() {
        searchDropdown.classList.remove('show');
        searchDropdown.classList.remove('expanded');
        searchContainer.classList.remove('active');

        // Hide all input containers
        document.querySelectorAll('.search-input-container').forEach(container => {
            container.style.display = 'none';
        });

        // Remove active class from all options
        document.querySelectorAll('.search-option').forEach(opt => {
            opt.classList.remove('active');
        });
    }

    function selectSearchOption(optionElement, searchType) {
        // Remove active class from all options
        document.querySelectorAll('.search-option').forEach(opt => {
            opt.classList.remove('active');
        });

        // Hide all input containers
        document.querySelectorAll('.search-input-container').forEach(container => {
            container.style.display = 'none';
        });

        // Add active class to selected option
        optionElement.classList.add('active');

        // Execute search based on type
        if (searchType === 'clear') {
            loadVaccinations();
            closeSearchDropdown();
        } else {
            // Show the corresponding input container
            const inputContainer = document.getElementById(`${searchType}SearchContainer`);
            if (inputContainer) {
                inputContainer.style.display = 'block';
                searchDropdown.classList.add('expanded');

                // Focus on the input field
                const inputField = document.getElementById(`${searchType}SearchInput`);
                if (inputField) {
                    setTimeout(() => inputField.focus(), 100);
                }
            }
        }
    }

    // Global functions for inline onclick handlers
    window.performSearch = function(searchType) {
        console.log(`🔍 performSearch called with type: ${searchType}`);
        const inputField = document.getElementById(`${searchType}SearchInput`);
        console.log(`🔍 Input field found:`, inputField);

        if (inputField) {
            const searchTerm = inputField.value.trim();
            console.log(`🔍 Search term: "${searchTerm}"`);

            if (searchTerm) {
                console.log(`🔍 Calling searchVaccinationsByField with term: "${searchTerm}", field: "${searchType}"`);
                searchVaccinationsByField(searchTerm, searchType);
                closeSearchDropdown();
                // Clear the input field
                inputField.value = '';
            } else {
                console.log(`🔍 Empty search term, highlighting field`);
                // Show error or highlight empty field
                inputField.style.borderColor = '#ff6b6b';
                setTimeout(() => {
                    inputField.style.borderColor = '#c1d8b9';
                }, 1000);
            }
        } else {
            console.error(`❌ Input field not found for search type: ${searchType}`);
        }
    };

    window.cancelSearch = function() {
        // Clear all input fields
        document.querySelectorAll('.search-input').forEach(input => {
            input.value = '';
        });

        // Hide all input containers
        document.querySelectorAll('.search-input-container').forEach(container => {
            container.style.display = 'none';
        });

        // Remove active class from all options
        document.querySelectorAll('.search-option').forEach(opt => {
            opt.classList.remove('active');
        });

        closeSearchDropdown();
    };

    // Filter dropdown functions
    function toggleFilterDropdown() {
        const isOpen = filterDropdown.classList.contains('show');
        if (isOpen) {
            closeFilterDropdown();
        } else {
            openFilterDropdown();
        }
    }

    function openFilterDropdown() {
        filterDropdown.classList.add('show');
        filterContainer.classList.add('active');
    }

    function closeFilterDropdown() {
        filterDropdown.classList.remove('show');
        filterContainer.classList.remove('active');
    }

    function selectFilterOption(optionElement, filterType) {
        // Remove active class from all options
        document.querySelectorAll('.filter-option').forEach(opt => {
            opt.classList.remove('active');
        });

        // Add active class to selected option
        optionElement.classList.add('active');

        // Update current filter
        currentFilter = filterType;

        // Apply filter
        if (filterType === 'type') {
            loadVaccinationsGroupedByType();
        } else {
            loadVaccinations();
        }

        // Close dropdown
        closeFilterDropdown();
    }

    // Function to load vaccinations
    function loadVaccinations() {
        // Get vaccinations from localStorage
        const vaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');

        // Clear existing table rows
        tableBody.innerHTML = '';

        // Add vaccinations to table
        if (vaccinations.length === 0) {
            // If no vaccinations, add a message row
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `
                <td colspan="4" style="text-align: center;">No vaccinations found</td>
            `;
            tableBody.appendChild(emptyRow);
        } else {
            vaccinations.forEach((vaccination, index) => {
                addVaccinationToTable(vaccination, index);
            });
        }

        // Add event listeners to checkboxes
        addCheckboxEventListeners();
    }

    // Function to load vaccinations grouped by type
    function loadVaccinationsGroupedByType() {
        // Get vaccinations from localStorage
        const vaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');

        // Clear existing table rows
        tableBody.innerHTML = '';

        if (vaccinations.length === 0) {
            // If no vaccinations, add a message row
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `
                <td colspan="4" style="text-align: center;">No vaccinations found</td>
            `;
            tableBody.appendChild(emptyRow);
            return;
        }

        // Group vaccinations by type
        const groupedVaccinations = {};
        vaccinations.forEach((vaccination, index) => {
            const type = vaccination.type || 'Unknown';
            if (!groupedVaccinations[type]) {
                groupedVaccinations[type] = [];
            }
            groupedVaccinations[type].push({ vaccination, index });
        });

        // Sort types alphabetically
        const sortedTypes = Object.keys(groupedVaccinations).sort();

        // Add grouped vaccinations to table
        sortedTypes.forEach(type => {
            // Add type header
            const headerRow = document.createElement('tr');
            headerRow.className = 'type-group-header';
            headerRow.innerHTML = `
                <td colspan="4" style="text-align: left; font-weight: bold; background-color: #f8f9fa; color: #0b291a; padding: 10px 8px; border-bottom: 2px solid #aedf32;">
                    📋 ${type} Vaccinations (${groupedVaccinations[type].length})
                </td>
            `;
            tableBody.appendChild(headerRow);

            // Add vaccinations for this type
            groupedVaccinations[type].forEach(({ vaccination, index }) => {
                const row = addVaccinationToTable(vaccination, index, true);
                row.classList.add('type-group-row');
            });
        });

        // Add event listeners to checkboxes
        addCheckboxEventListeners();
    }

    // Function to add a vaccination to the table
    function addVaccinationToTable(vaccination, index, isGrouped = false) {
        const row = document.createElement('tr');

        // Format the time to take
        let formattedTime = vaccination.timeToTake || '';
        if (vaccination.timeToTake) {
            // Check if it's already in HH:MM format
            if (vaccination.timeToTake.match(/^\d{1,2}:\d{2}$/)) {
                // It's already in HH:MM format, use as is
                formattedTime = vaccination.timeToTake;
            } else {
                // Try to parse as date and extract time
                try {
                    const date = new Date(vaccination.timeToTake);
                    if (!isNaN(date.getTime())) {
                        const hours = date.getHours().toString().padStart(2, '0');
                        const minutes = date.getMinutes().toString().padStart(2, '0');
                        formattedTime = `${hours}:${minutes}`;
                    } else {
                        formattedTime = vaccination.timeToTake;
                    }
                } catch (error) {
                    formattedTime = vaccination.timeToTake;
                }
            }
        }

        row.innerHTML = `
            <td>
                <div>
                    <form>
                        <label class="selectbox">
                            <input class="checkbox" type="checkbox" value="${index}">
                            ${vaccination.name}
                        </label>
                    </form>
                </div>
            </td>
            <td>${vaccination.type || ''}</td>
            <td>${vaccination.dose || ''}</td>
            <td>${formattedTime || ''}</td>
        `;

        tableBody.appendChild(row);
        return row;
    }

    // Function to search vaccinations
    function searchVaccinations(searchTerm) {
        // Get vaccinations from localStorage
        const vaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');

        // Filter vaccinations by search term (case-insensitive)
        const filteredVaccinations = vaccinations.filter(vaccination => {
            return vaccination.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                   (vaccination.type && vaccination.type.toLowerCase().includes(searchTerm.toLowerCase()));
        });

        // Clear existing table rows
        tableBody.innerHTML = '';

        // Add filtered vaccinations to table
        if (filteredVaccinations.length === 0) {
            // If no matching vaccinations, add a message row
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `
                <td colspan="4" style="text-align: center;">No vaccinations found matching "${searchTerm}"</td>
            `;
            tableBody.appendChild(emptyRow);
        } else {
            // Find the original indices of the filtered vaccinations
            filteredVaccinations.forEach((vaccination) => {
                const originalIndex = vaccinations.findIndex(v =>
                    v.name === vaccination.name &&
                    v.type === vaccination.type &&
                    v.dose === vaccination.dose &&
                    v.timeToTake === vaccination.timeToTake
                );
                addVaccinationToTable(vaccination, originalIndex);
            });
        }

        // Add event listeners to checkboxes
        addCheckboxEventListeners();
    }

    // Function to search vaccinations by specific field
    function searchVaccinationsByField(searchTerm, searchField) {
        console.log(`🔍 searchVaccinationsByField called with term: "${searchTerm}", field: "${searchField}"`);

        // Get vaccinations from localStorage
        const vaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');
        console.log(`🔍 Total vaccinations in storage:`, vaccinations.length);
        console.log(`🔍 Vaccinations data:`, vaccinations);

        // Filter vaccinations by specific field (case-insensitive)
        const filteredVaccinations = vaccinations.filter(vaccination => {
            let matches = false;
            switch (searchField) {
                case 'name':
                    matches = vaccination.name && vaccination.name.toLowerCase().includes(searchTerm.toLowerCase());
                    console.log(`🔍 Checking name "${vaccination.name}" against "${searchTerm}": ${matches}`);
                    return matches;
                case 'type':
                    matches = vaccination.type && vaccination.type.toLowerCase().includes(searchTerm.toLowerCase());
                    console.log(`🔍 Checking type "${vaccination.type}" against "${searchTerm}": ${matches}`);
                    return matches;
                case 'dose':
                    matches = vaccination.dose && vaccination.dose.toLowerCase().includes(searchTerm.toLowerCase());
                    console.log(`🔍 Checking dose "${vaccination.dose}" against "${searchTerm}": ${matches}`);
                    return matches;
                default:
                    // Fallback to general search
                    matches = vaccination.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           (vaccination.type && vaccination.type.toLowerCase().includes(searchTerm.toLowerCase())) ||
                           (vaccination.dose && vaccination.dose.toLowerCase().includes(searchTerm.toLowerCase()));
                    console.log(`🔍 General search for "${vaccination.name}": ${matches}`);
                    return matches;
            }
        });

        // Clear existing table rows
        tableBody.innerHTML = '';

        // Add filtered vaccinations to table
        if (filteredVaccinations.length === 0) {
            // If no matching vaccinations, add a message row
            const emptyRow = document.createElement('tr');
            const fieldName = searchField.charAt(0).toUpperCase() + searchField.slice(1);
            emptyRow.innerHTML = `
                <td colspan="4" style="text-align: center;">No vaccinations found with ${fieldName}: "${searchTerm}"</td>
            `;
            tableBody.appendChild(emptyRow);
        } else {
            // Find the original indices of the filtered vaccinations
            filteredVaccinations.forEach((vaccination) => {
                const originalIndex = vaccinations.findIndex(v =>
                    v.name === vaccination.name &&
                    v.type === vaccination.type &&
                    v.dose === vaccination.dose &&
                    v.timeToTake === vaccination.timeToTake
                );
                addVaccinationToTable(vaccination, originalIndex);
            });
        }

        // Add event listeners to checkboxes
        addCheckboxEventListeners();

        console.log(`🔍 Search completed: Found ${filteredVaccinations.length} vaccination(s) matching ${searchField}: "${searchTerm}"`);
    }

    // Function to filter vaccinations by type
    function filterVaccinations(filterType) {
        // If "All" is selected, just load all vaccinations
        if (filterType === 'All') {
            loadVaccinations();
            return;
        }

        // Get vaccinations from localStorage
        const vaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');

        // Filter vaccinations by type
        const filteredVaccinations = vaccinations.filter(vaccination => {
            return vaccination.type && vaccination.type.toLowerCase() === filterType.toLowerCase();
        });

        // Clear existing table rows
        tableBody.innerHTML = '';

        // Add filtered vaccinations to table
        if (filteredVaccinations.length === 0) {
            // If no matching vaccinations, add a message row
            const emptyRow = document.createElement('tr');
            emptyRow.innerHTML = `
                <td colspan="4" style="text-align: center;">No ${filterType} vaccinations found</td>
            `;
            tableBody.appendChild(emptyRow);
        } else {
            // Find the original indices of the filtered vaccinations
            filteredVaccinations.forEach((vaccination) => {
                const originalIndex = vaccinations.findIndex(v =>
                    v.name === vaccination.name &&
                    v.type === vaccination.type &&
                    v.dose === vaccination.dose &&
                    v.timeToTake === vaccination.timeToTake
                );
                addVaccinationToTable(vaccination, originalIndex);
            });
        }

        // Add event listeners to checkboxes
        addCheckboxEventListeners();
    }

    // Function to delete selected vaccinations
    function deleteSelectedVaccinations(selectedCheckboxes) {
        // Get vaccinations from localStorage
        let vaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');

        // Get indices of vaccinations to delete (in descending order to avoid index shifting)
        const indicesToDelete = Array.from(selectedCheckboxes)
            .map(checkbox => parseInt(checkbox.value))
            .sort((a, b) => b - a); // Sort in descending order

        // Delete vaccinations
        indicesToDelete.forEach(index => {
            if (index >= 0 && index < vaccinations.length) {
                vaccinations.splice(index, 1);
            }
        });

        // Save updated vaccinations to localStorage
        localStorage.setItem('vaccinations', JSON.stringify(vaccinations));

        // Reload vaccinations
        loadVaccinations();

        // Show success message
        alert(`${indicesToDelete.length} vaccination(s) deleted successfully`);
    }

    // Function to add event listeners to checkboxes
    function addCheckboxEventListeners() {
        const checkboxes = document.querySelectorAll('.checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', handleCheckboxChange);
        });
    }

    // Function to handle checkbox selection
    function handleCheckboxChange() {
        const selectedCount = document.querySelectorAll('.checkbox:checked').length;

        // Enable or disable delete button based on selection
        if (deleteVaccinationBtn) {
            if (selectedCount > 0) {
                deleteVaccinationBtn.disabled = false;
                deleteVaccinationBtn.style.opacity = 1;
            } else {
                deleteVaccinationBtn.disabled = true;
                deleteVaccinationBtn.style.opacity = 0.5;
            }
        }

        // Enable or disable update button based on selection
        // Only enable if exactly one vaccination is selected
        if (updateVaccinationBtn) {
            if (selectedCount === 1) {
                updateVaccinationBtn.disabled = false;
                updateVaccinationBtn.style.opacity = 1;
            } else {
                updateVaccinationBtn.disabled = true;
                updateVaccinationBtn.style.opacity = 0.5;
            }
        }
    }

    // Initialize button states
    deleteVaccinationBtn.disabled = true;
    deleteVaccinationBtn.style.opacity = 0.5;
    updateVaccinationBtn.disabled = true;
    updateVaccinationBtn.style.opacity = 0.5;

    // Add sample vaccinations if none exist (for testing)
    function addSampleVaccinations() {
        const vaccinations = JSON.parse(localStorage.getItem('vaccinations') || '[]');
        if (vaccinations.length === 0) {
            const sampleVaccinations = [
                {
                    name: 'Foot and Mouth Disease',
                    type: 'Newborn',
                    dose: '2ml',
                    timeToTake: new Date().toISOString(),
                    dateAdded: new Date().toISOString()
                },
                {
                    name: 'Brucellosis',
                    type: 'Milch',
                    dose: '5ml',
                    timeToTake: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days from now
                    dateAdded: new Date().toISOString()
                },
                {
                    name: 'Anthrax',
                    type: 'Fattening',
                    dose: '3ml',
                    timeToTake: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days from now
                    dateAdded: new Date().toISOString()
                }
            ];

            localStorage.setItem('vaccinations', JSON.stringify(sampleVaccinations));
            loadVaccinations();
        }
    }

    // Uncomment the line below to add sample vaccinations for testing
    addSampleVaccinations();

    // Debug: Test search functionality
    window.testSearch = function() {
        console.log('🧪 Testing search functionality...');
        searchVaccinationsByField('Foot', 'name');
    };

    console.log('🔍 Search system initialized. Use testSearch() to test search functionality.');
});
