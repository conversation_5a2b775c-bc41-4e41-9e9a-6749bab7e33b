document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const emailInput = document.querySelector('input[placeholder="<EMAIL>"]');
    const passwordInput = document.querySelector('input[type="password"]');
    const rememberMeCheckbox = document.querySelector('.checkbox');
    const loginButton = document.querySelector('.component .cancel');
    const forgetPasswordLink = document.querySelector('.text-wrapper-8');
    const createAccountLink = document.querySelector('.p2');
    
    // Check if there's a remembered user
    checkRememberedUser();
    
    // Handle login button click
    loginButton.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Get form values
        const email = emailInput.value.trim();
        const password = passwordInput.value.trim();
        
        // Validate form
        if (!email) {
            alert('Please enter your email');
            emailInput.focus();
            return;
        }
        
        if (!validateEmail(email)) {
            alert('Please enter a valid email address');
            emailInput.focus();
            return;
        }
        
        if (!password) {
            alert('Please enter your password');
            passwordInput.focus();
            return;
        }
        
        // Authenticate user
        authenticateUser(email, password);
    });
    
    // Handle forget password link click
    if (forgetPasswordLink) {
        forgetPasswordLink.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = 'forgetpassbyemail.html';
        });
    }
    
    // Handle create account link click
    if (createAccountLink) {
        createAccountLink.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = 'createaccount.html';
        });
    }
    
    // Add event listeners for Enter key on input fields
    emailInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            passwordInput.focus();
        }
    });
    
    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loginButton.click();
        }
    });
    
    // Function to validate email format
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    // Function to authenticate user
    function authenticateUser(email, password) {
        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        
        // Find user with matching email and password
        const user = users.find(u => u.email === email && u.password === password);
        
        if (!user) {
            alert('Invalid email or password. Please try again.');
            return;
        }
        
        // If remember me is checked, store user credentials
        if (rememberMeCheckbox && rememberMeCheckbox.checked) {
            localStorage.setItem('rememberedUser', JSON.stringify({
                email: email,
                // In a real app, you would NOT store the password in localStorage
                // This is just for demo purposes
                password: password
            }));
        } else {
            // Clear any remembered user
            localStorage.removeItem('rememberedUser');
        }
        
        // Store current user in session
        sessionStorage.setItem('currentUser', JSON.stringify(user));
        
        // Show success message
        alert('Login successful!');
        
        // Redirect to dashboard based on user role
        if (user.role === 'owner') {
            window.location.href = 'dashboard.html';
        } else if (user.role === 'engineer') {
            window.location.href = 'engineerdashboard.html';
        } else {
            window.location.href = 'dashboard.html';
        }
    }
    
    // Function to check if there's a remembered user
    function checkRememberedUser() {
        const rememberedUser = JSON.parse(localStorage.getItem('rememberedUser'));
        
        if (rememberedUser && emailInput && passwordInput) {
            emailInput.value = rememberedUser.email;
            passwordInput.value = rememberedUser.password;
            if (rememberMeCheckbox) {
                rememberMeCheckbox.checked = true;
            }
        }
    }
    
    // Add password visibility toggle
    addPasswordToggle();
    
    function addPasswordToggle() {
        if (!passwordInput) return;
        
        // Create eye icon
        const eyeIcon = document.createElement('span');
        eyeIcon.innerHTML = '👁️';
        eyeIcon.style.position = 'absolute';
        eyeIcon.style.right = '10px';
        eyeIcon.style.top = '50%';
        eyeIcon.style.transform = 'translateY(-50%)';
        eyeIcon.style.cursor = 'pointer';
        eyeIcon.style.zIndex = '10';
        
        // Position the container relatively
        passwordInput.parentElement.style.position = 'relative';
        
        // Add eye icon after the input field
        passwordInput.parentElement.appendChild(eyeIcon);
        
        // Add click event to toggle password visibility
        eyeIcon.addEventListener('click', function() {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.style.opacity = '0.7';
            } else {
                passwordInput.type = 'password';
                eyeIcon.style.opacity = '1';
            }
        });
    }
    
    // Check if user is already logged in
    checkLoggedInUser();
    
    function checkLoggedInUser() {
        const currentUser = JSON.parse(sessionStorage.getItem('currentUser'));
        
        if (currentUser) {
            // Create logout confirmation
            const logoutConfirmation = document.createElement('div');
            logoutConfirmation.style.position = 'fixed';
            logoutConfirmation.style.top = '0';
            logoutConfirmation.style.left = '0';
            logoutConfirmation.style.width = '100%';
            logoutConfirmation.style.height = '100%';
            logoutConfirmation.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            logoutConfirmation.style.display = 'flex';
            logoutConfirmation.style.justifyContent = 'center';
            logoutConfirmation.style.alignItems = 'center';
            logoutConfirmation.style.zIndex = '1000';
            
            const confirmationBox = document.createElement('div');
            confirmationBox.style.backgroundColor = 'white';
            confirmationBox.style.padding = '20px';
            confirmationBox.style.borderRadius = '5px';
            confirmationBox.style.width = '300px';
            confirmationBox.style.textAlign = 'center';
            
            const message = document.createElement('p');
            message.textContent = `You are currently logged in as ${currentUser.name || currentUser.email}. Do you want to log out?`;
            message.style.marginBottom = '20px';
            
            const buttonContainer = document.createElement('div');
            buttonContainer.style.display = 'flex';
            buttonContainer.style.justifyContent = 'space-between';
            
            const logoutBtn = document.createElement('button');
            logoutBtn.textContent = 'Log Out';
            logoutBtn.style.padding = '10px 20px';
            logoutBtn.style.backgroundColor = '#32cd32';
            logoutBtn.style.color = 'white';
            logoutBtn.style.border = 'none';
            logoutBtn.style.borderRadius = '3px';
            logoutBtn.style.cursor = 'pointer';
            
            const cancelBtn = document.createElement('button');
            cancelBtn.textContent = 'Cancel';
            cancelBtn.style.padding = '10px 20px';
            cancelBtn.style.backgroundColor = '#f1f1f1';
            cancelBtn.style.border = '1px solid #ddd';
            cancelBtn.style.borderRadius = '3px';
            cancelBtn.style.cursor = 'pointer';
            
            logoutBtn.addEventListener('click', function() {
                // Clear session storage
                sessionStorage.removeItem('currentUser');
                
                // Show success message
                alert('You have been logged out successfully.');
                
                // Remove the confirmation box
                document.body.removeChild(logoutConfirmation);
                
                // Reload the page to show the login form
                window.location.reload();
            });
            
            cancelBtn.addEventListener('click', function() {
                // Redirect to dashboard
                if (currentUser.role === 'owner') {
                    window.location.href = 'dashboard.html';
                } else if (currentUser.role === 'engineer') {
                    window.location.href = 'engineerdashboard.html';
                } else {
                    window.location.href = 'dashboard.html';
                }
            });
            
            buttonContainer.appendChild(logoutBtn);
            buttonContainer.appendChild(cancelBtn);
            
            confirmationBox.appendChild(message);
            confirmationBox.appendChild(buttonContainer);
            
            logoutConfirmation.appendChild(confirmationBox);
            document.body.appendChild(logoutConfirmation);
        }
    }
    
    // Add social login buttons functionality
    const socialButtons = document.querySelectorAll('.div-2');
    if (socialButtons) {
        socialButtons.forEach(button => {
            button.addEventListener('click', function() {
                const socialPlatform = this.querySelector('.text-wrapper-10').textContent;
                alert(`${socialPlatform} login would be implemented here.`);
                // In a real application, this would integrate with OAuth
            });
        });
    }
    
    // Add session timeout warning
    let sessionTimeout;
    
    function startSessionTimer() {
        // Clear any existing timeout
        clearTimeout(sessionTimeout);
        
        // Set timeout for 30 minutes (1800000 ms)
        sessionTimeout = setTimeout(function() {
            showSessionTimeoutWarning();
        }, 1800000);
    }
    
    function showSessionTimeoutWarning() {
        // Create warning modal
        const warningModal = document.createElement('div');
        warningModal.style.position = 'fixed';
        warningModal.style.top = '0';
        warningModal.style.left = '0';
        warningModal.style.width = '100%';
        warningModal.style.height = '100%';
        warningModal.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        warningModal.style.display = 'flex';
        warningModal.style.justifyContent = 'center';
        warningModal.style.alignItems = 'center';
        warningModal.style.zIndex = '1000';
        
        const warningBox = document.createElement('div');
        warningBox.style.backgroundColor = 'white';
        warningBox.style.padding = '20px';
        warningBox.style.borderRadius = '5px';
        warningBox.style.width = '300px';
        warningBox.style.textAlign = 'center';
        
        const warningMessage = document.createElement('p');
        warningMessage.textContent = 'Your session is about to expire due to inactivity. Do you want to stay logged in?';
        warningMessage.style.marginBottom = '20px';
        
        const buttonContainer = document.createElement('div');
        buttonContainer.style.display = 'flex';
        buttonContainer.style.justifyContent = 'space-between';
        
        const stayLoggedInBtn = document.createElement('button');
        stayLoggedInBtn.textContent = 'Stay Logged In';
        stayLoggedInBtn.style.padding = '10px 20px';
        stayLoggedInBtn.style.backgroundColor = '#32cd32';
        stayLoggedInBtn.style.color = 'white';
        stayLoggedInBtn.style.border = 'none';
        stayLoggedInBtn.style.borderRadius = '3px';
        stayLoggedInBtn.style.cursor = 'pointer';
        
        const logoutBtn = document.createElement('button');
        logoutBtn.textContent = 'Log Out';
        logoutBtn.style.padding = '10px 20px';
        logoutBtn.style.backgroundColor = '#f1f1f1';
        logoutBtn.style.border = '1px solid #ddd';
        logoutBtn.style.borderRadius = '3px';
        logoutBtn.style.cursor = 'pointer';
        
        stayLoggedInBtn.addEventListener('click', function() {
            // Restart the session timer
            startSessionTimer();
            
            // Remove the warning modal
            document.body.removeChild(warningModal);
        });
        
        logoutBtn.addEventListener('click', function() {
            // Clear session storage
            sessionStorage.removeItem('currentUser');
            
            // Show success message
            alert('You have been logged out successfully.');
            
            // Reload the page to show the login form
            window.location.reload();
        });
        
        buttonContainer.appendChild(stayLoggedInBtn);
        buttonContainer.appendChild(logoutBtn);
        
        warningBox.appendChild(warningMessage);
        warningBox.appendChild(buttonContainer);
        
        warningModal.appendChild(warningBox);
        document.body.appendChild(warningModal);
    }
    
    // Start session timer when page loads
    startSessionTimer();
    
    // Reset timer on user activity
    document.addEventListener('click', startSessionTimer);
    document.addEventListener('keypress', startSessionTimer);
    document.addEventListener('mousemove', startSessionTimer);
    document.addEventListener('scroll', startSessionTimer);
});