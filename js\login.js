document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const emailInput = document.querySelector('input[placeholder="<EMAIL>"]');
    const passwordInput = document.querySelector('input[type="password"]');
    const rememberMeCheckbox = document.querySelector('.checkbox');
    const loginButton = document.querySelector('.cancel');
    const forgetPasswordLink = document.querySelector('.text-wrapper-8');
    
    // Check if there's a remembered user
    checkRememberedUser();
    
    // Handle login button click
    loginButton.addEventListener('click', function(e) {
        e.preventDefault();
        
        // Get form values
        const email = emailInput.value.trim();
        const password = passwordInput.value.trim();
        
        // Validate form
        if (!email) {
            alert('Please enter your email');
            emailInput.focus();
            return;
        }
        
        if (!validateEmail(email)) {
            alert('Please enter a valid email address');
            emailInput.focus();
            return;
        }
        
        if (!password) {
            alert('Please enter your password');
            passwordInput.focus();
            return;
        }
        
        // Authenticate user
        authenticateUser(email, password);
    });
    
    // Handle forget password link click
    forgetPasswordLink.addEventListener('click', function(e) {
        e.preventDefault();
        window.location.href = 'forgetpassbyemail.html';
    });
    
    // Add event listeners for Enter key on input fields
    emailInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            passwordInput.focus();
        }
    });
    
    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            loginButton.click();
        }
    });
    
    // Function to validate email format
    function validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    }
    
    // Function to authenticate user
    function authenticateUser(email, password) {
        // Get users from localStorage
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        
        // Find user with matching email and password
        const user = users.find(u => u.email === email && u.password === password);
        
        if (!user) {
            alert('Invalid email or password. Please try again.');
            return;
        }
        
        // If remember me is checked, store user credentials
        if (rememberMeCheckbox.checked) {
            localStorage.setItem('rememberedUser', JSON.stringify({
                email: email,
                // In a real app, you would NOT store the password in localStorage
                // This is just for demo purposes
                password: password
            }));
        } else {
            // Clear any remembered user
            localStorage.removeItem('rememberedUser');
        }
        
        // Store current user in session
        sessionStorage.setItem('currentUser', JSON.stringify(user));
        
        // Show success message
        alert('Login successful!');
        
        // Redirect to dashboard based on user role
        if (user.role === 'owner') {
            window.location.href = 'dashboard.html';
        } else if (user.role === 'engineer') {
            window.location.href = 'engineerdashboard.html';
        } else {
            window.location.href = 'dashboard.html';
        }
    }
    
    // Function to check if there's a remembered user
    function checkRememberedUser() {
        const rememberedUser = JSON.parse(localStorage.getItem('rememberedUser'));
        
        if (rememberedUser) {
            emailInput.value = rememberedUser.email;
            passwordInput.value = rememberedUser.password;
            rememberMeCheckbox.checked = true;
        }
    }
    
    // Optional: Add password visibility toggle
    addPasswordToggle();
    
    function addPasswordToggle() {
        // Create eye icon
        const eyeIcon = document.createElement('span');
        eyeIcon.innerHTML = '👁️';
        eyeIcon.style.position = 'absolute';
        eyeIcon.style.right = '10px';
        eyeIcon.style.top = '50%';
        eyeIcon.style.transform = 'translateY(-50%)';
        eyeIcon.style.cursor = 'pointer';
        eyeIcon.style.zIndex = '10';
        
        // Position the container relatively
        passwordInput.parentElement.style.position = 'relative';
        
        // Add eye icon after the input field
        passwordInput.parentElement.appendChild(eyeIcon);
        
        // Add click event to toggle password visibility
        eyeIcon.addEventListener('click', function() {
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.style.opacity = '0.7';
            } else {
                passwordInput.type = 'password';
                eyeIcon.style.opacity = '1';
            }
        });
    }
    
});