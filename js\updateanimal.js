/**
 * Update Animal Page - Complete OOP Implementation
 * This file provides comprehensive functionality for updating existing animals
 * with proper validation, data management, and user feedback
 *
 * @author: Animal Management System
 * @version: 1.0.0
 * @description: Object-oriented system for updating animal data with full CRUD operations
 */

// ==================== BASE CLASSES ====================

/**
 * Base Data Manager for localStorage operations
 * Provides common functionality for data management
 */
class BaseDataManager {
    constructor() {
        this.storageKeys = {
            animals: 'animals',
            dairyAnimals: 'dairyAnimals',
            newbornAnimals: 'newbornAnimals'
        };
    }

    /**
     * Get data from localStorage with error handling
     * @param {string} key - Storage key
     * @returns {Array} - Array of data or empty array
     */
    getData(key) {
        try {
            const data = localStorage.getItem(this.storageKeys[key] || key);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`Error getting data for key ${key}:`, error);
            return [];
        }
    }

    /**
     * Save data to localStorage with error handling
     * @param {string} key - Storage key
     * @param {Array} data - Data to save
     * @returns {boolean} - Success status
     */
    saveData(key, data) {
        try {
            localStorage.setItem(this.storageKeys[key] || key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error(`Error saving data for key ${key}:`, error);
            return false;
        }
    }

    /**
     * Generate unique ID
     * @returns {string} - Unique identifier
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    /**
     * Find animal by code (case-insensitive)
     * @param {string} code - Animal code to search for
     * @returns {Object|null} - Found animal or null
     */
    findAnimalByCode(code) {
        if (!code || typeof code !== 'string') {
            return null;
        }

        const animals = this.getData('animals');
        return animals.find(animal =>
            animal && animal.code && typeof animal.code === 'string' &&
            animal.code.toLowerCase() === code.toLowerCase()
        ) || null;
    }

    /**
     * Check if animal code exists (excluding current animal)
     * @param {string} code - Code to check
     * @param {string} excludeId - ID to exclude from check
     * @returns {boolean} - True if code exists
     */
    codeExistsExcluding(code, excludeId) {
        if (!code || typeof code !== 'string') {
            return false;
        }

        const animals = this.getData('animals');
        return animals.some(animal =>
            animal && animal.code && typeof animal.code === 'string' &&
            animal.id !== excludeId &&
            animal.code.toLowerCase() === code.toLowerCase()
        );
    }
}

/**
 * Validation Manager for form validation
 * Handles all validation logic with comprehensive rules
 */
class ValidationManager {
    constructor() {
        this.rules = {
            required: ['code', 'type', 'gender'],
            conditionalRequired: {
                dairy: ['herdNumber'],
                newborn: ['dateOfBirth']
            }
        };
    }

    /**
     * Validate single field with comprehensive rules
     * @param {string} fieldName - Name of the field
     * @param {*} value - Value to validate
     * @param {string} animalType - Type of animal for conditional validation
     * @returns {Array} - Array of error messages
     */
    validateField(fieldName, value, animalType = null) {
        const errors = [];

        // Required field validation
        if (this.rules.required.includes(fieldName) && !value) {
            errors.push(`${this.getFieldDisplayName(fieldName)} is required`);
        }

        // Conditional required validation
        if (animalType && this.rules.conditionalRequired[animalType]) {
            if (this.rules.conditionalRequired[animalType].includes(fieldName) && !value) {
                errors.push(`${this.getFieldDisplayName(fieldName)} is required for ${animalType} animals`);
            }
        }

        // Specific field validations
        switch (fieldName) {
            case 'code':
                if (value && !/^[A-Za-z0-9]+$/.test(value)) {
                    errors.push('Code can only contain letters and numbers');
                }
                if (value && value.length > 20) {
                    errors.push('Code cannot be longer than 20 characters');
                }
                break;
            case 'weight':
                if (value && (isNaN(value) || parseFloat(value) <= 0)) {
                    errors.push('Weight must be a positive number');
                }
                if (value && parseFloat(value) > 2000) {
                    errors.push('Weight seems unrealistic (max 2000kg)');
                }
                break;
            case 'dateOfBirth':
                if (value && new Date(value) > new Date()) {
                    errors.push('Date of birth cannot be in the future');
                }
                if (value && new Date(value) < new Date('1900-01-01')) {
                    errors.push('Date of birth cannot be before 1900');
                }
                break;
            case 'dateOfWeight':
                if (value && new Date(value) > new Date()) {
                    errors.push('Date of weight cannot be in the future');
                }
                break;
            case 'herdNumber':
                if (value && !/^[A-Za-z0-9]+$/.test(value)) {
                    errors.push('Herd number can only contain letters and numbers');
                }
                break;
        }

        return errors;
    }

    /**
     * Validate entire form data with cross-field validation
     * @param {Object} data - Form data to validate
     * @returns {Array} - Array of error messages
     */
    validateFormData(data) {
        const errors = [];

        // Validate all fields
        Object.entries(data).forEach(([field, value]) => {
            const fieldErrors = this.validateField(field, value, data.type);
            errors.push(...fieldErrors);
        });

        // Cross-field validations
        if (data.dateOfBirth && data.dateOfWeight) {
            if (new Date(data.dateOfWeight) < new Date(data.dateOfBirth)) {
                errors.push('Date of weight cannot be before date of birth');
            }
        }

        // Business rule validations
        if (data.type === 'dairy' && data.gender !== 'female') {
            errors.push('Dairy animals must be female');
        }

        return errors;
    }

    /**
     * Get display name for field
     * @param {string} fieldName - Field name
     * @returns {string} - Human-readable field name
     */
    getFieldDisplayName(fieldName) {
        const displayNames = {
            code: 'Code',
            type: 'Type',
            gender: 'Gender',
            herdNumber: 'Herd Number',
            weight: 'Weight',
            dateOfWeight: 'Date of Weight',
            dateOfBirth: 'Date of Birth',
            healthcareNotes: 'Healthcare Notes',
            takenVaccination: 'Vaccination Information'
        };
        return displayNames[fieldName] || fieldName;
    }
}

/**
 * UI Manager for user interface operations
 * Handles all UI interactions, notifications, and visual feedback
 */
class UIManager {
    constructor() {
        this.notifications = [];
    }

    /**
     * Show field error with visual styling
     * @param {HTMLElement} fieldElement - Form field element
     * @param {string} message - Error message to display
     */
    showFieldError(fieldElement, message) {
        this.clearFieldError(fieldElement);

        // Add error styling
        fieldElement.style.borderColor = '#dc3545';
        fieldElement.style.backgroundColor = '#fff5f5';

        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.style.cssText = `
            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
            padding: 2px 0;
            animation: shake 0.3s ease-in-out;
        `;
        errorDiv.textContent = message;

        // Insert after field
        fieldElement.parentNode.insertBefore(errorDiv, fieldElement.nextSibling);
    }

    /**
     * Clear field error styling and message
     * @param {HTMLElement} fieldElement - Form field element
     */
    clearFieldError(fieldElement) {
        // Reset styling
        fieldElement.style.borderColor = '';
        fieldElement.style.backgroundColor = '';

        // Remove error message
        const errorDiv = fieldElement.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * Clear all field errors
     */
    clearAllFieldErrors() {
        const errorDivs = document.querySelectorAll('.field-error');
        errorDivs.forEach(div => div.remove());

        const fields = document.querySelectorAll('.data-filled, .textarea');
        fields.forEach(field => {
            field.style.borderColor = '';
            field.style.backgroundColor = '';
        });
    }

    /**
     * Show notification with different types and auto-dismiss
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, warning, info)
     * @param {number} duration - Auto-dismiss duration in milliseconds
     * @returns {HTMLElement} - Notification element
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        this.clearNotifications();

        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            animation: slideIn 0.3s ease-out;
        `;

        // Set colors based on type
        const colors = {
            success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
            error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
            warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
            info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
        };

        const color = colors[type] || colors.info;
        notification.style.backgroundColor = color.bg;
        notification.style.border = `1px solid ${color.border}`;
        notification.style.color = color.text;

        notification.textContent = message;

        // Add close button
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            float: right;
            margin-left: 10px;
            cursor: pointer;
            font-size: 18px;
            line-height: 1;
        `;
        closeBtn.onclick = () => notification.remove();
        notification.appendChild(closeBtn);

        document.body.appendChild(notification);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }

        // Add animation styles if not already present
        if (!document.querySelector('#notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-5px); }
                    75% { transform: translateX(5px); }
                }
            `;
            document.head.appendChild(style);
        }

        this.notifications.push(notification);
        return notification;
    }

    /**
     * Clear all notifications
     */
    clearNotifications() {
        this.notifications.forEach(notification => {
            if (notification.parentNode) {
                notification.remove();
            }
        });
        this.notifications = [];
    }

    /**
     * Show loading state on button
     * @param {HTMLElement} button - Button element
     * @param {string} text - Loading text
     */
    showLoading(button, text = 'Updating...') {
        if (!button) return;

        button.disabled = true;
        const btnText = button.querySelector('.btntext');
        if (btnText) {
            button.dataset.originalText = btnText.textContent;
            btnText.textContent = text;
        }
        button.style.opacity = '0.7';
    }

    /**
     * Hide loading state on button
     * @param {HTMLElement} button - Button element
     */
    hideLoading(button) {
        if (!button) return;

        button.disabled = false;
        const btnText = button.querySelector('.btntext');
        if (btnText && button.dataset.originalText) {
            btnText.textContent = button.dataset.originalText;
            delete button.dataset.originalText;
        }
        button.style.opacity = '1';
    }

    /**
     * Highlight field with success styling
     * @param {HTMLElement} fieldElement - Form field element
     */
    highlightFieldSuccess(fieldElement) {
        fieldElement.style.borderColor = '#28a745';
        fieldElement.style.backgroundColor = '#f8fff9';

        setTimeout(() => {
            fieldElement.style.borderColor = '';
            fieldElement.style.backgroundColor = '';
        }, 2000);
    }
}

// ==================== MAIN CLASSES ====================

/**
 * Animal Data Manager - Handles all data operations for updating animals
 * Extends BaseDataManager with update-specific functionality
 */
class AnimalDataManager extends BaseDataManager {
    constructor() {
        super();
    }

    /**
     * Update animal in main table and type-specific tables
     * @param {string} animalId - ID of animal to update
     * @param {Object} updateData - New data for the animal
     * @returns {Object} - Updated animal object
     */
    updateAnimal(animalId, updateData) {
        if (!animalId) {
            throw new Error('Animal ID is required for update');
        }

        if (!updateData) {
            throw new Error('Update data is required');
        }

        const timestamp = new Date().toISOString();

        try {
            // 1. Update main animals table
            const updatedAnimal = this.updateMainTable(animalId, updateData, timestamp);

            // 2. Update type-specific table based on animal type
            switch (updatedAnimal.type) {
                case 'dairy':
                    this.updateDairyTable(animalId, updatedAnimal);
                    break;
                case 'newborn':
                    this.updateNewbornTable(animalId, updatedAnimal);
                    break;
                case 'fattening':
                    // Fattening animals only exist in main table
                    // But we need to remove from other tables if type changed
                    this.removeFromTypeSpecificTables(animalId, ['dairy', 'newborn']);
                    break;
                default:
                    console.warn(`Unknown animal type: ${updatedAnimal.type}`);
            }

            console.log(`✅ Animal ${updatedAnimal.code} updated successfully:`, {
                mainTable: true,
                typeSpecificTable: updatedAnimal.type !== 'fattening',
                type: updatedAnimal.type
            });

            return updatedAnimal;

        } catch (error) {
            console.error('❌ Error updating animal:', error);
            throw error;
        }
    }

    /**
     * Update animal in main animals table
     * @param {string} animalId - Animal ID
     * @param {Object} updateData - Update data
     * @param {string} timestamp - Update timestamp
     * @returns {Object} - Updated animal
     */
    updateMainTable(animalId, updateData, timestamp) {
        const animals = this.getData('animals');
        const animalIndex = animals.findIndex(a => a.id === animalId);

        if (animalIndex === -1) {
            throw new Error(`Animal with ID "${animalId}" not found`);
        }

        const currentAnimal = animals[animalIndex];

        // Check for duplicate code (excluding current animal)
        if (updateData.code && updateData.code !== currentAnimal.code) {
            if (this.codeExistsExcluding(updateData.code, animalId)) {
                throw new Error(`Animal with code "${updateData.code}" already exists`);
            }
        }

        // Create updated animal object
        const updatedAnimal = {
            ...currentAnimal,
            ...updateData,
            id: animalId, // Preserve original ID
            createdAt: currentAnimal.createdAt, // Preserve creation date
            updatedAt: timestamp
        };

        // Update in array
        animals[animalIndex] = updatedAnimal;

        if (!this.saveData('animals', animals)) {
            throw new Error('Failed to update main animals table');
        }

        console.log(`📋 Updated in main animals table: ${updatedAnimal.code}`);
        return updatedAnimal;
    }

    /**
     * Update animal in dairy animals table
     * @param {string} animalId - Animal ID
     * @param {Object} animalData - Updated animal data
     */
    updateDairyTable(animalId, animalData) {
        const dairyAnimals = this.getData('dairyAnimals');
        const dairyIndex = dairyAnimals.findIndex(a => a.id === animalId);

        if (dairyIndex === -1) {
            // Animal not in dairy table, add it
            const dairyAnimal = {
                ...animalData,
                animalId: animalData.id,
                milkProduction: 0,
                averageDailyMilk: 0,
                lastMilkingDate: null,
                totalMilkProduced: 0,
                lactationPeriod: null,
                breedType: null,
                calvingDate: null,
                dryPeriodStart: null,
                dryPeriodEnd: null
            };
            dairyAnimals.push(dairyAnimal);
        } else {
            // Update existing dairy record
            dairyAnimals[dairyIndex] = {
                ...dairyAnimals[dairyIndex],
                ...animalData,
                animalId: animalData.id
            };
        }

        if (!this.saveData('dairyAnimals', dairyAnimals)) {
            throw new Error('Failed to update dairy animals table');
        }

        console.log(`🥛 Updated in dairy animals table: ${animalData.code}`);
    }

    /**
     * Update animal in newborn animals table
     * @param {string} animalId - Animal ID
     * @param {Object} animalData - Updated animal data
     */
    updateNewbornTable(animalId, animalData) {
        const newbornAnimals = this.getData('newbornAnimals');
        const newbornIndex = newbornAnimals.findIndex(a => a.id === animalId);

        if (newbornIndex === -1) {
            // Animal not in newborn table, add it
            const newbornAnimal = {
                ...animalData,
                animalId: animalData.id,
                motherCode: null,
                fatherCode: null,
                birthWeight: animalData.weight || null,
                weaningDate: null,
                weaningWeight: null,
                tagNumber: null,
                registrationNumber: null,
                pedigreeInfo: null
            };
            newbornAnimals.push(newbornAnimal);
        } else {
            // Update existing newborn record
            newbornAnimals[newbornIndex] = {
                ...newbornAnimals[newbornIndex],
                ...animalData,
                animalId: animalData.id,
                birthWeight: animalData.weight || newbornAnimals[newbornIndex].birthWeight
            };
        }

        if (!this.saveData('newbornAnimals', newbornAnimals)) {
            throw new Error('Failed to update newborn animals table');
        }

        console.log(`👶 Updated in newborn animals table: ${animalData.code}`);
    }

    /**
     * Remove animal from type-specific tables when type changes
     * @param {string} animalId - Animal ID
     * @param {Array} tableTypes - Types of tables to remove from
     */
    removeFromTypeSpecificTables(animalId, tableTypes) {
        tableTypes.forEach(type => {
            const tableName = type === 'dairy' ? 'dairyAnimals' : 'newbornAnimals';
            const animals = this.getData(tableName);
            const filteredAnimals = animals.filter(a => a.id !== animalId);

            if (filteredAnimals.length !== animals.length) {
                this.saveData(tableName, filteredAnimals);
                console.log(`🗑️ Removed from ${tableName} table`);
            }
        });
    }

    /**
     * Get all animals with pagination support
     * @param {number} page - Page number (1-based)
     * @param {number} limit - Items per page
     * @returns {Object} - Paginated results
     */
    getAllAnimals(page = 1, limit = 50) {
        const animals = this.getData('animals');
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;

        return {
            animals: animals.slice(startIndex, endIndex),
            totalCount: animals.length,
            currentPage: page,
            totalPages: Math.ceil(animals.length / limit),
            hasNextPage: endIndex < animals.length,
            hasPreviousPage: page > 1
        };
    }

    /**
     * Search animals by various criteria
     * @param {string} searchTerm - Search term
     * @param {string} searchField - Field to search in (optional)
     * @returns {Array} - Matching animals
     */
    searchAnimals(searchTerm, searchField = null) {
        if (!searchTerm) {
            return this.getData('animals');
        }

        const animals = this.getData('animals');
        const term = searchTerm.toLowerCase();

        return animals.filter(animal => {
            if (searchField) {
                const fieldValue = animal[searchField];
                return fieldValue && fieldValue.toString().toLowerCase().includes(term);
            } else {
                // Search across multiple fields
                const searchableFields = ['code', 'type', 'gender', 'herdNumber', 'healthcareNotes'];
                return searchableFields.some(field => {
                    const fieldValue = animal[field];
                    return fieldValue && fieldValue.toString().toLowerCase().includes(term);
                });
            }
        });
    }

    /**
     * Get statistics about animals
     * @returns {Object} - Statistics object
     */
    getStatistics() {
        const animals = this.getData('animals');
        const dairyAnimals = this.getData('dairyAnimals');
        const newbornAnimals = this.getData('newbornAnimals');

        return {
            total: animals.length,
            dairy: animals.filter(a => a.type === 'dairy').length,
            newborn: animals.filter(a => a.type === 'newborn').length,
            fattening: animals.filter(a => a.type === 'fattening').length,
            female: animals.filter(a => a.gender === 'female').length,
            male: animals.filter(a => a.gender === 'male').length,
            dairyTableCount: dairyAnimals.length,
            newbornTableCount: newbornAnimals.length,
            dataIntegrity: {
                dairyConsistent: animals.filter(a => a.type === 'dairy').length === dairyAnimals.length,
                newbornConsistent: animals.filter(a => a.type === 'newborn').length === newbornAnimals.length
            }
        };
    }

    /**
     * Delete animal from all tables
     * @param {string} animalId - Animal ID to delete
     * @returns {Object} - Deleted animal data
     */
    deleteAnimal(animalId) {
        if (!animalId) {
            throw new Error('Animal ID is required for deletion');
        }

        const animals = this.getData('animals');
        const animalIndex = animals.findIndex(a => a.id === animalId);

        if (animalIndex === -1) {
            throw new Error(`Animal with ID "${animalId}" not found`);
        }

        const deletedAnimal = animals[animalIndex];

        // Remove from main table
        animals.splice(animalIndex, 1);
        this.saveData('animals', animals);

        // Remove from type-specific tables
        this.removeFromTypeSpecificTables(animalId, ['dairy', 'newborn']);

        console.log(`🗑️ Animal ${deletedAnimal.code} deleted successfully`);
        return deletedAnimal;
    }
}

/**
 * Main Update Animal Controller Class
 * Orchestrates all components and manages the update animal page
 */
class UpdateAnimal {
    constructor() {
        this.dataManager = new AnimalDataManager();
        this.validationManager = new ValidationManager();
        this.uiManager = new UIManager();
        this.formData = {};
        this.currentAnimal = null;
        this.isInitialized = false;
        this.searchMode = true; // Start in search mode
    }

    /**
     * Initialize the controller
     */
    init() {
        try {
            console.log('🚀 Initializing Update Animal system...');

            this.setupFormElements();
            this.setupEventListeners();
            this.setupValidation();
            this.setupSearchMode();

            this.isInitialized = true;
            console.log('✅ Update Animal system initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize Update Animal system:', error);
            this.uiManager.showNotification('Failed to initialize the form. Please refresh the page.', 'error');
        }
    }

    /**
     * Setup form elements
     */
    setupFormElements() {
        this.elements = {
            // Code input serves as both search and editable field
            codeInput: document.querySelector('.data-filled'),
            searchInput: document.querySelector('.data-filled'), // Same as codeInput for compatibility

            // Update button
            updateButton: document.querySelector('.frame-9'),

            // Type radio buttons
            typeRadios: document.querySelectorAll('input[name="access"]'),

            // Gender radio buttons
            genderRadios: document.querySelectorAll('input[name="gender"]'),

            // Other input fields
            herdNumberInput: document.querySelectorAll('.data-filled')[1],
            weightInput: document.querySelectorAll('.data-filled')[2],
            dateOfWeightInput: document.querySelectorAll('.data-filled')[3],
            dateOfBirthInput: document.querySelectorAll('.data-filled')[4],

            // Textarea fields
            healthcareNotesInput: document.querySelectorAll('.textarea')[0],
            vaccinationInput: document.querySelectorAll('.textarea')[1]
        };

        // Validate that all elements exist
        const missingElements = [];
        Object.entries(this.elements).forEach(([key, element]) => {
            if (!element || (Array.isArray(element) && element.length === 0)) {
                missingElements.push(key);
            }
        });

        if (missingElements.length > 0) {
            console.warn(`Some form elements not found: ${missingElements.join(', ')}`);
        }

        console.log('📋 Form elements found and mapped');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Code field handles both search and editing
        if (this.elements.codeInput) {
            // Real-time search as user types
            this.elements.codeInput.addEventListener('input', (e) => {
                const value = e.target.value.trim();

                // Update form data
                this.formData.code = value;

                // Clear field error when user starts typing
                this.uiManager.clearFieldError(e.target);

                // Perform real-time search
                this.handleRealTimeSearch(value);
            });

            // Handle Enter key for immediate search
            this.elements.codeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleSearch();
                }
            });

            // Handle focus to show instructions
            this.elements.codeInput.addEventListener('focus', () => {
                if (!this.currentAnimal) {
                    this.uiManager.showNotification('Start typing an animal code to load data...', 'info', 2000);
                }
            });

            // Handle blur for validation
            this.elements.codeInput.addEventListener('blur', (e) => {
                this.validateSingleField(e.target, 'code');
            });
        }

        // Update button
        if (this.elements.updateButton) {
            this.elements.updateButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleUpdate();
            });
        }

        // Type selection
        this.elements.typeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.handleTypeChange(e.target.id);
                }
            });
        });

        // Gender selection
        this.elements.genderRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.handleGenderChange(e.target.id);
                }
            });
        });

        // Other input field changes (excluding code field which is handled above)
        const otherInputs = [
            { element: this.elements.herdNumberInput, field: 'herdNumber' },
            { element: this.elements.weightInput, field: 'weight' },
            { element: this.elements.dateOfWeightInput, field: 'dateOfWeight' },
            { element: this.elements.dateOfBirthInput, field: 'dateOfBirth' },
            { element: this.elements.healthcareNotesInput, field: 'healthcareNotes' },
            { element: this.elements.vaccinationInput, field: 'takenVaccination' }
        ];

        otherInputs.forEach(({ element, field }) => {
            if (element) {
                // Real-time validation on input
                element.addEventListener('input', (e) => {
                    this.handleInputChange(e, field);
                });

                // Validation on blur
                element.addEventListener('blur', (e) => {
                    this.validateSingleField(e.target, field);
                });
            }
        });

        console.log('🔗 Event listeners setup complete');
    }

    /**
     * Get field name by index (legacy method, kept for compatibility)
     */
    getFieldName(index) {
        const fieldNames = [
            'code',
            'herdNumber',
            'weight',
            'dateOfWeight',
            'dateOfBirth',
            'healthcareNotes',
            'takenVaccination'
        ];
        return fieldNames[index] || 'unknown';
    }

    /**
     * Setup search mode - enable fields for immediate editing
     */
    setupSearchMode() {
        this.setFormMode('ready');
        this.uiManager.showNotification('Start typing an animal code to load and edit data', 'info', 3000);
    }

    /**
     * Set form mode (ready, search, or edit)
     * @param {string} mode - 'ready', 'search', or 'edit'
     */
    setFormMode(mode) {
        const otherFormFields = [
            ...this.elements.typeRadios,
            ...this.elements.genderRadios,
            this.elements.herdNumberInput,
            this.elements.weightInput,
            this.elements.dateOfWeightInput,
            this.elements.dateOfBirthInput,
            this.elements.healthcareNotesInput,
            this.elements.vaccinationInput
        ];

        if (mode === 'ready') {
            // Ready mode: All fields enabled but empty, waiting for animal to be loaded
            this.searchMode = false;

            // Code field is always enabled for search/edit
            if (this.elements.codeInput) {
                this.elements.codeInput.disabled = false;
                this.elements.codeInput.style.opacity = '1';
                this.elements.codeInput.placeholder = 'Type animal code to load and edit data...';
            }

            // Other fields enabled but update button disabled
            otherFormFields.forEach(field => {
                if (field) {
                    field.disabled = false;
                    field.style.opacity = '1';
                }
            });

            if (this.elements.updateButton) {
                this.elements.updateButton.disabled = true;
                this.elements.updateButton.style.opacity = '0.5';
                this.elements.updateButton.title = 'Load an animal first';
            }

        } else if (mode === 'search') {
            this.searchMode = true;

            // Code field always enabled for search
            if (this.elements.codeInput) {
                this.elements.codeInput.disabled = false;
                this.elements.codeInput.style.opacity = '1';
                this.elements.codeInput.placeholder = 'Type animal code to search...';
            }

            // Other fields disabled
            otherFormFields.forEach(field => {
                if (field) {
                    field.disabled = true;
                    field.style.opacity = '0.5';
                }
            });

            if (this.elements.updateButton) {
                this.elements.updateButton.disabled = true;
                this.elements.updateButton.style.opacity = '0.5';
            }

        } else if (mode === 'edit') {
            this.searchMode = false;

            // All fields enabled including code field
            if (this.elements.codeInput) {
                this.elements.codeInput.disabled = false;
                this.elements.codeInput.style.opacity = '1';
                this.elements.codeInput.placeholder = 'Animal code (editable)';
            }

            otherFormFields.forEach(field => {
                if (field) {
                    field.disabled = false;
                    field.style.opacity = '1';
                }
            });

            if (this.elements.updateButton) {
                this.elements.updateButton.disabled = false;
                this.elements.updateButton.style.opacity = '1';
                this.elements.updateButton.title = 'Update animal data';
            }
        }
    }

    /**
     * Handle real-time search as user types
     * @param {string} searchTerm - The search term
     */
    handleRealTimeSearch(searchTerm) {
        // Clear any existing timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // If search term is empty, clear form and set to ready mode
        if (!searchTerm) {
            this.clearFormData();
            this.setFormMode('ready');
            return;
        }

        // Debounce the search to avoid too many lookups
        this.searchTimeout = setTimeout(() => {
            this.performRealTimeSearch(searchTerm);
        }, 300); // 300ms delay
    }

    /**
     * Perform the actual real-time search
     * @param {string} searchTerm - The search term
     */
    performRealTimeSearch(searchTerm) {
        console.log(`🔍 Real-time searching for: ${searchTerm}`);

        // Search for animal
        const animal = this.dataManager.findAnimalByCode(searchTerm);

        if (animal) {
            // Animal found, load it into form immediately
            this.loadAnimalIntoForm(animal);
            this.setFormMode('edit');
            console.log(`✅ Animal "${animal.code}" loaded automatically`);
        } else {
            // Animal not found, clear form but keep fields enabled
            this.clearFormData();
            this.setFormMode('ready');
            this.showNotFoundFeedback(searchTerm);
            console.log(`❌ Animal "${searchTerm}" not found`);
        }
    }

    /**
     * Show visual feedback when animal is not found
     * @param {string} searchTerm - The search term that wasn't found
     */
    showNotFoundFeedback(searchTerm) {
        if (this.elements.codeInput) {
            // Add red border to indicate not found
            this.elements.codeInput.style.borderColor = '#dc3545';
            this.elements.codeInput.style.backgroundColor = '#fff5f5';

            // Reset after 2 seconds
            setTimeout(() => {
                this.elements.codeInput.style.borderColor = '';
                this.elements.codeInput.style.backgroundColor = '';
            }, 2000);
        }

        // Only show notification for longer search terms to avoid spam
        if (searchTerm.length >= 3) {
            // Clear any existing notification timeout
            if (this.notFoundTimeout) {
                clearTimeout(this.notFoundTimeout);
            }

            // Show notification after a delay to avoid too many notifications
            this.notFoundTimeout = setTimeout(() => {
                this.uiManager.showNotification(`Animal "${searchTerm}" not found`, 'warning', 2000);
            }, 1000);
        }
    }

    /**
     * Handle manual search (when search button is clicked or Enter is pressed)
     */
    handleSearch() {
        const searchTerm = this.elements.codeInput.value.trim();

        if (!searchTerm) {
            this.uiManager.showNotification('Please enter an animal code to search', 'warning');
            return;
        }

        console.log(`🔍 Manual search for animal: ${searchTerm}`);

        // Search for animal
        const animal = this.dataManager.findAnimalByCode(searchTerm);

        if (!animal) {
            this.uiManager.showNotification(`Animal with code "${searchTerm}" not found`, 'error');
            this.clearFormData();
            this.setFormMode('ready');
            return;
        }

        // Animal found, load it into form
        this.loadAnimalIntoForm(animal);
        this.setFormMode('edit');
        this.uiManager.showNotification(`Animal "${animal.code}" loaded successfully!`, 'success', 2000);
    }

    /**
     * Load animal data into form
     * @param {Object} animal - Animal data to load
     */
    loadAnimalIntoForm(animal) {
        this.currentAnimal = animal;
        this.formData = { ...animal };

        console.log('📝 Loading animal into form:', animal);

        // Add visual feedback to code input
        if (this.elements.codeInput) {
            this.elements.codeInput.style.borderColor = '#28a745';
            this.elements.codeInput.style.backgroundColor = '#f8fff9';
            setTimeout(() => {
                this.elements.codeInput.style.borderColor = '';
                this.elements.codeInput.style.backgroundColor = '';
            }, 2000);
        }

        // Set type radio button
        if (animal.type) {
            const typeRadio = document.getElementById(animal.type);
            if (typeRadio) {
                typeRadio.checked = true;
                this.handleTypeChange(animal.type);
            }
        }

        // Set gender radio button
        if (animal.gender) {
            const genderRadio = document.getElementById(animal.gender);
            if (genderRadio) {
                genderRadio.checked = true;
                this.handleGenderChange(animal.gender);
            }
        }

        // Fill input fields with smooth animation (code field already has the value from search)
        // Only update code field if it's different (for exact match confirmation)
        if (this.elements.codeInput && this.elements.codeInput.value.trim() !== animal.code) {
            this.fillFieldWithAnimation(this.elements.codeInput, animal.code || '');
        }

        this.fillFieldWithAnimation(this.elements.herdNumberInput, animal.herdNumber || '');
        this.fillFieldWithAnimation(this.elements.weightInput, animal.weight || '');
        this.fillFieldWithAnimation(this.elements.dateOfWeightInput, animal.dateOfWeight || '');
        this.fillFieldWithAnimation(this.elements.dateOfBirthInput, animal.dateOfBirth || '');
        this.fillFieldWithAnimation(this.elements.healthcareNotesInput, animal.healthcareNotes || '');
        this.fillFieldWithAnimation(this.elements.vaccinationInput, animal.takenVaccination || '');

        // Clear any previous errors
        this.uiManager.clearAllFieldErrors();
    }

    /**
     * Fill field with smooth animation
     * @param {HTMLElement} field - Field element
     * @param {string} value - Value to fill
     */
    fillFieldWithAnimation(field, value) {
        if (!field) return;

        // Add loading animation
        field.style.transition = 'all 0.3s ease';
        field.style.backgroundColor = '#e3f2fd';

        setTimeout(() => {
            field.value = value;
            field.style.backgroundColor = '#f8fff9';

            setTimeout(() => {
                field.style.backgroundColor = '';
                field.style.transition = '';
            }, 500);
        }, 100);
    }

    /**
     * Handle animal type change
     */
    handleTypeChange(type) {
        this.formData.type = type;
        console.log(`📝 Animal type changed to: ${type}`);

        // Clear previous validation errors
        this.uiManager.clearAllFieldErrors();

        // Auto-select female for dairy animals
        if (type === 'dairy' && this.formData.gender !== 'female') {
            const femaleRadio = document.getElementById('female');
            if (femaleRadio && !femaleRadio.checked) {
                femaleRadio.checked = true;
                this.handleGenderChange('female');
                this.uiManager.showNotification('Dairy animals must be female. Gender automatically set to female.', 'info', 3000);
            }
        }
    }

    /**
     * Handle gender change
     */
    handleGenderChange(gender) {
        this.formData.gender = gender;
        console.log(`👤 Gender changed to: ${gender}`);

        // Validate dairy animal gender
        if (this.formData.type === 'dairy' && gender !== 'female') {
            this.uiManager.showNotification('Dairy animals must be female', 'warning');

            // Auto-correct to female
            setTimeout(() => {
                const femaleRadio = document.getElementById('female');
                if (femaleRadio) {
                    femaleRadio.checked = true;
                    this.formData.gender = 'female';
                }
            }, 1000);
        }
    }

    /**
     * Handle input field changes
     */
    handleInputChange(event, fieldName) {
        const value = event.target.value.trim();
        this.formData[fieldName] = value;

        // Clear field error when user starts typing
        this.uiManager.clearFieldError(event.target);

        // Real-time validation for specific fields
        if (fieldName === 'code' && value && this.currentAnimal) {
            this.validateCodeUniqueness(event.target, value);
        }
    }

    /**
     * Validate code uniqueness (excluding current animal)
     */
    validateCodeUniqueness(fieldElement, code) {
        if (this.currentAnimal && this.dataManager.codeExistsExcluding(code, this.currentAnimal.id)) {
            this.uiManager.showFieldError(fieldElement, `Animal with code "${code}" already exists`);
            return false;
        }
        return true;
    }

    /**
     * Validate single field
     */
    validateSingleField(fieldElement, fieldName) {
        const value = fieldElement.value.trim();
        const errors = this.validationManager.validateField(fieldName, value, this.formData.type);

        if (errors.length > 0) {
            this.uiManager.showFieldError(fieldElement, errors[0]);
            return false;
        } else {
            this.uiManager.clearFieldError(fieldElement);
            return true;
        }
    }

    /**
     * Collect all form data
     */
    collectFormData() {
        const data = { ...this.formData };

        // Collect input field values safely
        if (this.elements.codeInput && this.elements.codeInput.value !== undefined) {
            data.code = this.elements.codeInput.value.trim();
        }
        if (this.elements.herdNumberInput && this.elements.herdNumberInput.value !== undefined) {
            data.herdNumber = this.elements.herdNumberInput.value.trim();
        }
        if (this.elements.weightInput && this.elements.weightInput.value !== undefined) {
            data.weight = this.elements.weightInput.value.trim();
        }
        if (this.elements.dateOfWeightInput && this.elements.dateOfWeightInput.value !== undefined) {
            data.dateOfWeight = this.elements.dateOfWeightInput.value;
        }
        if (this.elements.dateOfBirthInput && this.elements.dateOfBirthInput.value !== undefined) {
            data.dateOfBirth = this.elements.dateOfBirthInput.value;
        }
        if (this.elements.healthcareNotesInput && this.elements.healthcareNotesInput.value !== undefined) {
            data.healthcareNotes = this.elements.healthcareNotesInput.value.trim();
        }
        if (this.elements.vaccinationInput && this.elements.vaccinationInput.value !== undefined) {
            data.takenVaccination = this.elements.vaccinationInput.value.trim();
        }

        // Ensure required fields have default values if missing
        if (!data.code) data.code = '';
        if (!data.type) data.type = '';
        if (!data.gender) data.gender = '';

        return data;
    }

    /**
     * Validate entire form
     */
    validateForm() {
        if (!this.currentAnimal) {
            this.uiManager.showNotification('No animal loaded for update', 'error');
            return false;
        }

        const data = this.collectFormData();
        const errors = this.validationManager.validateFormData(data);

        // Clear all previous errors
        this.uiManager.clearAllFieldErrors();

        if (errors.length > 0) {
            // Show first error as notification
            this.uiManager.showNotification(errors[0], 'error');

            // Show field-specific errors
            this.showFieldErrors(errors);

            return false;
        }

        return true;
    }

    /**
     * Show field-specific errors
     */
    showFieldErrors(errors) {
        const fieldMap = {
            code: this.elements.codeInput,
            herdNumber: this.elements.herdNumberInput,
            weight: this.elements.weightInput,
            dateOfWeight: this.elements.dateOfWeightInput,
            dateOfBirth: this.elements.dateOfBirthInput,
            healthcareNotes: this.elements.healthcareNotesInput,
            takenVaccination: this.elements.vaccinationInput
        };

        // Map errors to fields
        errors.forEach(error => {
            Object.entries(fieldMap).forEach(([fieldName, element]) => {
                if (error.toLowerCase().includes(fieldName.toLowerCase()) ||
                    error.toLowerCase().includes(this.validationManager.getFieldDisplayName(fieldName).toLowerCase())) {
                    if (element) {
                        this.uiManager.showFieldError(element, error);
                    }
                }
            });
        });
    }

    /**
     * Handle update button click
     */
    async handleUpdate() {
        console.log('💾 Update button clicked');

        if (!this.validateForm()) {
            console.log('❌ Form validation failed');
            return;
        }

        const formData = this.collectFormData();
        console.log('📝 Form data collected:', formData);

        // Show loading state
        this.uiManager.showLoading(this.elements.updateButton);

        try {
            // Update the animal
            const updatedAnimal = this.dataManager.updateAnimal(this.currentAnimal.id, formData);

            console.log('✅ Animal updated successfully:', updatedAnimal);

            // Show success message
            this.uiManager.showNotification(
                `Animal "${updatedAnimal.code}" updated successfully!`,
                'success',
                4000
            );

            // Update current animal reference
            this.currentAnimal = updatedAnimal;

            // Highlight updated fields
            this.highlightUpdatedFields();

            // Trigger storage event for other pages to update
            window.dispatchEvent(new StorageEvent('storage', {
                key: 'animals',
                newValue: JSON.stringify(this.dataManager.getData('animals'))
            }));

        } catch (error) {
            console.error('❌ Error updating animal:', error);
            this.uiManager.showNotification(
                `Failed to update animal: ${error.message}`,
                'error'
            );
        } finally {
            // Hide loading state
            this.uiManager.hideLoading(this.elements.updateButton);
        }
    }

    /**
     * Highlight updated fields with success styling
     */
    highlightUpdatedFields() {
        const fields = [
            this.elements.codeInput,
            this.elements.herdNumberInput,
            this.elements.weightInput,
            this.elements.dateOfWeightInput,
            this.elements.dateOfBirthInput,
            this.elements.healthcareNotesInput,
            this.elements.vaccinationInput
        ];

        fields.forEach(field => {
            if (field) {
                this.uiManager.highlightFieldSuccess(field);
            }
        });
    }

    /**
     * Clear only form data (keep current animal reference and code field)
     */
    clearFormData() {
        // Clear form fields but keep code field and current animal reference for comparison
        const fieldsToKeep = ['codeInput', 'searchInput', 'updateButton'];

        Object.entries(this.elements).forEach(([key, element]) => {
            if (fieldsToKeep.includes(key)) {
                return; // Skip these elements
            }

            if (element && element.value !== undefined) {
                element.value = '';
            } else if (element && element.length) {
                // Handle NodeList
                element.forEach(el => {
                    if (el.value !== undefined) el.value = '';
                    if (el.checked !== undefined) el.checked = false;
                });
            }
        });

        // Clear form data but keep code for search
        const currentCode = this.formData.code;
        this.formData = { code: currentCode };

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        console.log('🔄 Form data cleared (keeping code field)');
    }

    /**
     * Clear form to initial state
     */
    clearForm() {
        // Clear form data
        this.formData = {};
        this.currentAnimal = null;

        // Clear all input fields
        Object.values(this.elements).forEach(element => {
            if (element && element.value !== undefined) {
                element.value = '';
            } else if (element && element.length) {
                // Handle NodeList
                element.forEach(el => {
                    if (el.value !== undefined) el.value = '';
                    if (el.checked !== undefined) el.checked = false;
                });
            }
        });

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        console.log('🔄 Form cleared completely');
    }

    /**
     * Setup validation styles
     */
    setupValidation() {
        // Add form validation styles
        const style = document.createElement('style');
        style.textContent = `
            .field-error {
                animation: shake 0.3s ease-in-out;
            }
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Get current statistics
     */
    getStatistics() {
        return this.dataManager.getStatistics();
    }

    /**
     * Search animals
     */
    searchAnimals(searchTerm, searchField = null) {
        return this.dataManager.searchAnimals(searchTerm, searchField);
    }

    /**
     * Get all animals with pagination
     */
    getAllAnimals(page = 1, limit = 50) {
        return this.dataManager.getAllAnimals(page, limit);
    }

    /**
     * Delete current animal
     */
    deleteCurrentAnimal() {
        if (!this.currentAnimal) {
            this.uiManager.showNotification('No animal loaded for deletion', 'error');
            return;
        }

        const confirmed = confirm(`Are you sure you want to delete animal "${this.currentAnimal.code}"? This action cannot be undone.`);
        if (confirmed) {
            try {
                this.dataManager.deleteAnimal(this.currentAnimal.id);
                this.uiManager.showNotification(`Animal "${this.currentAnimal.code}" deleted successfully`, 'success');
                this.clearForm();
                this.setFormMode('search');
            } catch (error) {
                this.uiManager.showNotification(`Failed to delete animal: ${error.message}`, 'error');
            }
        }
    }
}

// ==================== INITIALIZATION ====================

/**
 * Initialize the Update Animal system when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing Update Animal system...');

    try {
        // Create and initialize the controller
        const updateAnimal = new UpdateAnimal();
        updateAnimal.init();

        // Make it globally accessible for debugging and external access
        window.updateAnimal = updateAnimal;

        console.log('🎉 Update Animal system ready!');
        console.log('Available global methods:');
        console.log('- window.updateAnimal.searchAnimals(term)');
        console.log('- window.updateAnimal.getStatistics()');
        console.log('- window.updateAnimal.getAllAnimals()');
        console.log('- window.updateAnimal.deleteCurrentAnimal()');

    } catch (error) {
        console.error('💥 Failed to initialize Update Animal system:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        errorDiv.textContent = 'Failed to initialize the update form. Please refresh the page.';
        document.body.appendChild(errorDiv);
    }
});

// ==================== UTILITY FUNCTIONS ====================

/**
 * Search for animal by code (utility function)
 * @param {string} code - Animal code to search for
 * @returns {Object|null} - Found animal or null
 */
window.searchAnimalByCode = function(code) {
    if (!window.updateAnimal) {
        console.error('Update Animal system not initialized');
        return null;
    }

    return window.updateAnimal.dataManager.findAnimalByCode(code);
};

/**
 * Load animal into form by code (utility function)
 * @param {string} code - Animal code to load
 * @returns {boolean} - Success status
 */
window.loadAnimalByCode = function(code) {
    if (!window.updateAnimal) {
        console.error('Update Animal system not initialized');
        return false;
    }

    const animal = window.updateAnimal.dataManager.findAnimalByCode(code);
    if (animal) {
        window.updateAnimal.loadAnimalIntoForm(animal);
        window.updateAnimal.setFormMode('edit');
        return true;
    }
    return false;
};

/**
 * Get all animals (utility function)
 * @returns {Array} - Array of all animals
 */
window.getAllAnimals = function() {
    if (!window.updateAnimal) {
        console.error('Update Animal system not initialized');
        return [];
    }

    return window.updateAnimal.dataManager.getData('animals');
};

/**
 * Update animal programmatically (utility function)
 * @param {string} animalId - Animal ID
 * @param {Object} updateData - Data to update
 * @returns {Object|null} - Updated animal or null
 */
window.updateAnimalById = function(animalId, updateData) {
    if (!window.updateAnimal) {
        console.error('Update Animal system not initialized');
        return null;
    }

    try {
        return window.updateAnimal.dataManager.updateAnimal(animalId, updateData);
    } catch (error) {
        console.error('Error updating animal:', error);
        return null;
    }
};

/**
 * Debug function to check system status
 */
window.debugUpdateAnimal = function() {
    console.log('=== Update Animal Debug Info ===');

    if (!window.updateAnimal) {
        console.error('❌ Update Animal system not initialized');
        return;
    }

    console.log('✅ System initialized');
    console.log('📊 Statistics:', window.updateAnimal.getStatistics());
    console.log('🔍 Current animal:', window.updateAnimal.currentAnimal);
    console.log('📝 Form data:', window.updateAnimal.formData);
    console.log('🔄 Search mode:', window.updateAnimal.searchMode);

    // Check form elements
    const elements = window.updateAnimal.elements;
    console.log('📋 Form elements:');
    Object.entries(elements).forEach(([key, element]) => {
        if (element) {
            console.log(`  ✅ ${key}: Found`);
        } else {
            console.log(`  ❌ ${key}: Missing`);
        }
    });

    console.log('=== End Debug Info ===');
};

/**
 * Quick test function to load a test animal
 */
window.testLoadAnimal = function() {
    if (!window.updateAnimal) {
        console.error('Update Animal system not initialized');
        return;
    }

    // Get first animal from storage
    const animals = window.updateAnimal.dataManager.getData('animals');
    if (animals.length > 0) {
        const firstAnimal = animals[0];
        console.log('Loading test animal:', firstAnimal.code);

        // Set code input and trigger search
        if (window.updateAnimal.elements.codeInput) {
            window.updateAnimal.elements.codeInput.value = firstAnimal.code;
            window.updateAnimal.handleSearch();
        }

        return firstAnimal;
    } else {
        console.log('No animals found in storage');
        return null;
    }
};

/**
 * Create test animal for testing update functionality
 */
window.createTestAnimal = function() {
    if (!window.updateAnimal) {
        console.error('Update Animal system not initialized');
        return;
    }

    const testAnimal = {
        code: 'TEST_UPDATE_' + Date.now(),
        type: 'dairy',
        gender: 'female',
        herdNumber: 'H999',
        weight: '400',
        dateOfWeight: new Date().toISOString().split('T')[0],
        dateOfBirth: '2022-01-01',
        healthcareNotes: 'Test animal for update functionality',
        takenVaccination: 'Test vaccination'
    };

    try {
        // Use the add new animal system if available, otherwise add directly
        if (window.addNewAnimal && window.addNewAnimal.dataManager) {
            const result = window.addNewAnimal.dataManager.addAnimal(testAnimal);
            console.log('✅ Test animal created:', result.code);
            return result;
        } else {
            // Fallback: add directly to storage
            const animals = JSON.parse(localStorage.getItem('animals') || '[]');
            testAnimal.id = Date.now().toString(36) + Math.random().toString(36).substring(2);
            testAnimal.createdAt = new Date().toISOString();
            testAnimal.updatedAt = new Date().toISOString();
            animals.push(testAnimal);
            localStorage.setItem('animals', JSON.stringify(animals));

            console.log('✅ Test animal created (fallback):', testAnimal.code);
            return testAnimal;
        }
    } catch (error) {
        console.error('❌ Error creating test animal:', error);
        return null;
    }
};

// Close button functionality removed - handled by external close button in animal.js

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        UpdateAnimal,
        AnimalDataManager,
        ValidationManager,
        UIManager,
        BaseDataManager
    };
}