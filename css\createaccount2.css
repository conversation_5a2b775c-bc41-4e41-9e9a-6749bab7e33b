@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
    -webkit-font-smoothing: antialiased;
    box-sizing: border-box;
}
html,
body {
    margin: 0px;
    background-color: #e3e4e4;
}
   
/* a blue color as a generic focus style */
button:focus-visible {
    outline: 2px solid #4a90e2 !important;
    outline: -webkit-focus-ring-color auto 5px !important;
}
a {
    text-decoration: none;
}


/*style*/
.CREATE-ACCOUNT {
   
    display: flex;
    flex-direction: row;
    justify-content: center;
    width: 100%;
    height: 100%;
}


.CREATE-ACCOUNT .unsplash {
    position: absolute;
   
    width: 55%;
    height: 100%;
    
   left: 45%;
 
}
.writinghalf{
    width: 45%;
    align-items: flex-start;
}

.frame {
    display: flex;
    flex-direction: column;
    width: 139px;
    /**/height: 10%;
    align-items: center;
    gap: 5px;
    position: absolute;
    top: 10px;
    left: 20px;
  }
  
  
.text-wrapper {
    position: relative;
    align-self: stretch;
    height: 70%;
    margin-top: -1.00px;
    font-family: "<PERSON><PERSON><PERSON>", Helvetica;
    font-weight: 400;
    color: #0b291a;
    font-size: 50px;
    letter-spacing: 0;
    line-height: normal;
  }
  
.text-wrapper-2 {
    position: relative;
    width: 70%;
    margin-top: -1.00px;
    font-family: "Aboreto-Regular", Helvetica;
    font-weight: 400;
    color: #0b291a;
    font-size: 8px;
    text-align: center;
    letter-spacing: 0;
    line-height: 9px;
    left: -10%;
  }

 .log-in-pge {
    display: flex;
    flex-direction: column;
    width: 50.5%;
    height: 90%;
    align-items: flex-start;
    gap: 10px;
    position: absolute;
    top: 8%;
    left: 2%;
    justify-content: center;
    padding-top: 2%;
  }

.frame-3 {

    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    align-items: center;
    gap: 16px;
    position: relative;
}

 .welcome-back {
    display: flex;
    align-items: center;
    top: 10%;
    padding: 7px;
    position: relative;
    align-self: stretch;
    width: 90%;
    flex: 0 0 auto;
   
}

 .text-wrapper-3 {
    position: relative;
    width: 50%;
    margin-top: -1.00px;
    font-family: "Roboto-Bold", Helvetica;
    font-weight: 700;
    color: #0b291a;
    font-size: 40px;
    letter-spacing: 0;
    line-height: normal;
    left: 5%;
}

.registration {
   position: relative;
    width: 65px;
    height: 55px;
   
   
}

.dta-entered {
    top: 10%;
    display: flex;
    flex-direction: column;
    width:100%;
    height: 60%;
    align-items: flex-start;
    gap: 5px;
    position: relative;
    left: 10%;
}
 .frame-4 {
    display: flex;
    flex-direction: column;
    width: 50%;
    align-items: flex-start;
    gap: 16px;
    position: relative;
    flex: 0 0 auto;
}

.frame-5 {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 7px;
    position: relative;
    flex: 0 0 auto;
    margin-bottom: 1%;
    padding-top: 10px;
}

 .img {
    position: relative;
    width: 30px;
    height: 30px;
    
}

.CREATE-ACCOUNT .text-wrapper-4 {
    position: relative;
    width: fit-content;
    font-family: "Albert Sans-Medium", Helvetica;
    font-weight: 500;
    color: #000000;
    font-size: 24px;
    letter-spacing: 0;
    line-height: normal;
}

.CREATE-ACCOUNT .frame-6 {
    display: flex;
    height: 50px;
    align-items: center;
    gap: 4px;
    padding: 12px 80px 12px 16px;
    position: relative;
    align-self: stretch;
    width: 100%;
    background-color: #f5f5f5;
    border-radius: 12px;
    border: 1px solid;
    border-color: #1212121f;
}


.CREATE-ACCOUNT .frame-7 {
    top: 5%;
    display: flex;
    flex-direction: column;
    width: 60%;
    align-items: flex-start;
    gap: 8px;
    position: relative;
    flex: 0 0 auto;
}


.CREATE-ACCOUNT .eye {
    position: relative;
    width: 24px;
    height: 24px;
}


.CREATE-ACCOUNT .text-wrapper-8 {
    position: relative;
    width: fit-content;
    margin-top: -7.00px;
    margin-bottom: -5.00px;
    margin-left: -4px;
    font-family: "Albert Sans-Regular", Helvetica;
    font-weight: 400;
    color: #000000;
    font-size: 22px;
    text-align: center;
    letter-spacing: 0;
    line-height: normal;
}

.CREATE-ACCOUNT .frame-11 {
    position: relative;
    align-self: stretch;
    width: 100%;
    height: 30px;
   
    font-size: 20px;
    font-weight: 550;
    display:inline;
    display: flex;
    gap:10%
    
}

.raddiv{
    display: inline-flex;
align-items: center;
justify-content: center;
}
.radio{
  
   width: 30px;
   height: 30px;
   background: #e3e4e4;
   margin-right: 5px;
   accent-color: #32CD32;
}

 .component {
    display: flex;
    width: 60%;
    height: 50px;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 10px;
    position: relative;
    background-color: #32cd32;
    border-radius: 12px;
    margin-top: 5%;
    border: none;
}

 .cancel {
    position: relative;
    width: fit-content;
    font-family: "Roboto-Medium", Helvetica;
    font-weight: 500;
    color: #ffffff;
    font-size: 24px;
    text-align: center;
    letter-spacing: 0;
    line-height: normal;
    white-space: nowrap;
}



:root {
    --gray: rgba(179, 179, 179, 1);
    --highlights-font-family: "Roboto", Helvetica;
    --highlights-font-weight: 500;
    --highlights-font-size: 32px;
    --highlights-letter-spacing: 0px;
    --highlights-line-height: normal;
    --highlights-font-style: normal;
    --bottom-font-family: "Roboto", Helvetica;
    --bottom-font-weight: 500;
    --bottom-font-size: 24px;
    --bottom-letter-spacing: 0px;
    --bottom-line-height: normal;
    --bottom-font-style: normal;
    --topic-chart-behind-topic-font-family: "Roboto", Helvetica;
    --topic-chart-behind-topic-font-weight: 400;
    --topic-chart-behind-topic-font-size: 36px;
    --topic-chart-behind-topic-letter-spacing: 0px;
    --topic-chart-behind-topic-line-height: normal;
    --topic-chart-behind-topic-font-style: normal;
    --content-table-font-family: "Roboto", Helvetica;
    --content-table-font-weight: 500;
    --content-table-font-size: 22px;
    --content-table-letter-spacing: 0px;
    --content-table-line-height: normal;
    --content-table-font-style: normal;
    --para-20-roboto-font-family: "Roboto", Helvetica;
    --para-20-roboto-font-weight: 400;
    --para-20-roboto-font-size: 20px;
    --para-20-roboto-letter-spacing: 0px;
    --para-20-roboto-line-height: 55px;
    --para-20-roboto-font-style: normal;
}

