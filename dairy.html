<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8" />

    <link rel="stylesheet" href="css/dairy.css" />
    <link rel="stylesheet" href="css/navigation-components.css" />
    <!-- Added version parameter to prevent caching -->
</head>

<body>

    <div class="feed">

        <!-- Include the sidebar and navbar components -->
        <div class="page-container">
            <!-- Sidebar Component -->
            <div class="side-bar">
                <div class="s-r-a-parent-wrapper">
                    <div class="sra-parent">
                        <h1 class="sra">SRA</h1>
                        <div class="smart-raising-animal-wrapper">
                            <div class="smart-raising-animal">Smart Raising Animal</div>
                        </div>
                    </div>
                </div>
                <div class="side-bar-bottom">
                    <div class="side-bar-bottom-inner">
                        <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
                    </div>
                    <div class="side-bar-elements">
                        <div class="side-bar-options">
                            <div class="side-bar-option-parent">
                                <div class="vuesaxlinearcategory-2-parent">
                                    <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt=""
                                        src="./public/vuesaxlinearcategory21.svg" />
                                    <a class="animals" href="index.html">Dashboard</a>
                                </div>
                            </div>
                            <div class="side-bar-option-parent-inner">
                                <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
                            </div>
                            <div class="side-bar-option-parent1">
                                <div class="side-bar-element">
                                    <div class="side-animal">
                                        <img class="bull-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                                        <div class="animals-wrapper">
                                            <a class="animals" href="animal.html">Animals</a>
                                        </div>
                                    </div>
                                    <div class="side-animal">
                                        <img class="bull-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                                        <a class="animals" href="dairy.html">Dairy</a>
                                    </div>
                                    <div class="side-animal">
                                        <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                                        <a class="animals" href="newborn.html">New Born</a>
                                    </div>
                                    <div class="side-animal">
                                        <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                                        <a class="animals" href="feed.html">Feed</a>
                                    </div>
                                    <div class="side-animal">
                                        <img class="cow-1-icon" loading="lazy" alt=""
                                            src="./public/<EMAIL>" />
                                        <a class="animals" href="ingrediants.html">Ingredients</a>
                                    </div>
                                    <div class="side-animal">
                                        <div class="side-animal">
                                            <img class="cow-1-icon" loading="lazy" alt=""
                                                src="./public/<EMAIL>" />
                                            <a class="animals" href="vaccination.html">Vaccination</a>
                                        </div>
                                    </div>
                                    <div class="side-animal">
                                        <img class="cow-1-icon" loading="lazy" alt=""
                                            src="./public/<EMAIL>" />
                                        <a class="animals" href="reports.html">Reports</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navbar Component -->
            <header class="navbar">
                <img class="notification-bell-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                <div class="nav-profile" id="navProfileContainer">
                    <img class="male-avatar-portrait-of-a-youn-icon" loading="lazy" alt=""
                        src="./public/<EMAIL>" />
                    <div class="side-pregnant">
                        <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt=""
                            src="./public/vuesaxlineararrowdown.svg" />
                    </div>
                </div>
            </header>


            <!-- Your page content goes here -->
            <main class="content-container">
                <!-- Page specific content -->


                <div class="frame-4">


                    <button class="frame-9" title="Record daily milk production for dairy animals">
                        <div class="btntext">Record milk production</div>
                    </button>

                    <button class="frame-9" title="Add a new dairy animal to the system">
                        <div class="btntext">Add new Animal</div>
                    </button>

                    <button class="frame-9" title="Update information for existing dairy animals">
                        <div class="btntext">Updating Data</div>
                    </button>

                    <div class="search-dropdown-container">
                        <button class="search" id="searchButton" title="Search for specific dairy animals">
                            <img class="double-left1" src="icons/search.svg">
                            <div class="btntext1">Search</div>
                            <span class="search-dropdown-arrow">▼</span>
                        </button>
                        <div class="search-dropdown-menu" id="searchDropdown">
                            <div class="search-option" data-search="code">
                                <span class="search-icon">🏷️</span>
                                <span>By Code</span>
                            </div>
                            <div class="search-input-container" id="codeSearchContainer" style="display: none;">
                                <input type="text" class="search-input" id="codeSearchInput" placeholder="Enter dairy animal code...">
                                <div class="search-buttons">
                                    <button class="search-btn-small" onclick="performDairySearch('code')">Search</button>
                                    <button class="search-btn-cancel" onclick="cancelDairySearch()">Cancel</button>
                                </div>
                            </div>

                            <div class="search-option" data-search="herdNumber">
                                <span class="search-icon">🏠</span>
                                <span>By Herd Number</span>
                            </div>
                            <div class="search-input-container" id="herdNumberSearchContainer" style="display: none;">
                                <input type="text" class="search-input" id="herdNumberSearchInput" placeholder="Enter herd number...">
                                <div class="search-buttons">
                                    <button class="search-btn-small" onclick="performDairySearch('herdNumber')">Search</button>
                                    <button class="search-btn-cancel" onclick="cancelDairySearch()">Cancel</button>
                                </div>
                            </div>

                            <div class="search-option" data-search="milkProduction">
                                <span class="search-icon">🥛</span>
                                <span>By Milk Production</span>
                            </div>
                            <div class="search-input-container" id="milkProductionSearchContainer" style="display: none;">
                                <input type="text" class="search-input" id="milkProductionSearchInput" placeholder="Enter milk production (e.g., 25L)...">
                                <div class="search-buttons">
                                    <button class="search-btn-small" onclick="performDairySearch('milkProduction')">Search</button>
                                    <button class="search-btn-cancel" onclick="cancelDairySearch()">Cancel</button>
                                </div>
                            </div>

                            <div class="search-option" data-search="clear">
                                <span class="search-icon">🔄</span>
                                <span>Clear Search</span>
                            </div>
                        </div>
                    </div>

                    <div class="filter-dropdown-container">
                        <button class="frame-9" id="filterButton" title="Filter dairy animals by various criteria">
                            <img class="double-left1" src="icons/filter.svg">
                            <div class="btntext">Filter</div>
                            <span class="filter-dropdown-arrow">▼</span>
                        </button>
                        <div class="filter-dropdown-menu" id="filterDropdown">
                            <div class="filter-section">
                                <label for="typeFilter">Type:</label>
                                <select class="filter-select" id="typeFilter" data-field="type">
                                    <option value="all">All Types</option>
                                    <option value="dairy">Dairy</option>
                                    <option value="drying">Drying</option>
                                </select>
                            </div>

                            <div class="filter-section">
                                <label for="herdNumberFilter">Herd Number:</label>
                                <select class="filter-select" id="herdNumberFilter" data-field="herdNumber">
                                    <option value="all">All Herds</option>
                                    <!-- Options will be populated dynamically -->
                                </select>
                            </div>

                            <div class="filter-section">
                                <label for="milkProductionFilter">Milk Production:</label>
                                <select class="filter-select" id="milkProductionFilter" data-field="milkProduction">
                                    <option value="all">All Production Levels</option>
                                    <option value="high">High (>30L)</option>
                                    <option value="medium">Medium (20-30L)</option>
                                    <option value="low">Low (<20L)</option>
                                </select>
                            </div>

                            <div class="filter-section">
                                <label for="aiStatusFilter">AI Status:</label>
                                <select class="filter-select" id="aiStatusFilter" data-field="artificialInseminationStatus">
                                    <option value="all">All Statuses</option>
                                    <option value="yes">Yes</option>
                                    <option value="no">No</option>
                                    <option value="pregnant">Pregnant</option>
                                </select>
                            </div>

                            <div class="filter-actions">
                                <button class="filter-apply-btn" onclick="applyDairyFilters()">Apply Filters</button>
                                <button class="filter-clear-btn" onclick="clearDairyFilters()">Clear All</button>
                            </div>
                        </div>
                    </div>

                    <button class="frame-9" id="deleteAnimalBtn" title="Remove dairy animals from the system">
                        <div class="btntext">Delete Animal</div>
                    </button>




                </div>

                <div class="parent1">
                    <div class="frame-5">
                        <div class="text-wrapper-5">Dairy</div>
                    </div>
                    <div class="tablecontainer">
                        <table>
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Type</th>
                                    <th><span class="acronym" title="Herd Number">HM</span></th>
                                    <th><span class="acronym" title="Milk Production">MP</span></th>
                                    <th><span class="acronym" title="Fat Percentage">FP</span></th>
                                    <th>Weight</th>
                                    <th><span class="acronym" title="Date of Weight">DW</span></th>
                                    <th><span class="acronym" title="Status of Artificial Insemination">SAI</span></th>
                                    <th><span class="acronym" title="Date of Artificial Insemination">DAI</span></th>
                                    <th><span class="acronym" title="Expected Date of Calving">EDC</span></th>
                                    <th>Healthcare</th>

                                </tr>
                            </thead>
                            <tbody>




                            </tbody>
                        </table>
                    </div>
                </div>
            </main>
        </div>
    </div>
    <!-- Copy the profile popup component here -->
    <!-- Profile Popup -->
    <div id="navProfilePopup" class="popup-overlay1" style="display: none">
        <div class="nav-profile1">
            <div class="component-13">
                <a class="log-out" href="logout.html">Log Out</a>
            </div>
            <div class="component-131">
                <a class="log-out1" href="setting.html">Settings</a>
            </div>
            <div class="component-14">
                <a class="log-out2" href="about-us.html">About</a>
            </div>
        </div>
    </div>




    <script src="js/dairy.js"></script>
    <script src="js/navigation-components.js"></script>
     <script src="js/dairyPopupManager.js"></script>
     
</body>

</html>