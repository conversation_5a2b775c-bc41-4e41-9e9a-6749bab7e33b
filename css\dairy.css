@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0%;
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  
  text-decoration: none;
}

.feed {

    background-color: #e3e4e4;
    display: flex;
    flex-direction: row;
    justify-content: center;
    /* padding: 2%; */
    width: 100%;
    height: 100%;
}



 /* .navbar {
    position: absolute;
    width:75%;
    height: 54px;
    top: 3%;
    left: 22%;
    background-color: #ffffff;
    border-radius: 20px;
    overflow: hidden;
}

.notification-bell {
    position: absolute;
    width: 55px;
    height: 55px;
    top: 6px;
    left: 19px;
    background-image: url(./img/notification.png);
    background-size: 100% 100%;
}
.side-bar {
    position: absolute;
    width: 18%;
    height: 94%;
    left: 2%;
  margin-bottom: 2%;
    background-color: #0b291a;
    border-radius: 20px;
    overflow: hidden;
} */

 .frame {
    position: absolute;
    width: 143px;
    height: 64px;
    top: 1px;
    left: 915px;
}



.parent{
  display:flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 85%;


}

.child{
    height: 10%;

    color: #ffffff;
}





 .frame-3 {
     display: flex;
    flex-direction: column;

    position: relative;
    text-align: center;
justify-content: center;
align-items: center;
    width: 100%;
  height: 15%;


font-size: 40px;
color: #fbfaf0;
font-family: ABeeZee;
}

 .text-wrapper-3 {

    font-size: 4vw; /* Large font size for acronym */
    font-weight: 400;
    color: #f0f0f0; /* Light color for text */

}


 .text-wrapper-4 {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    font-size: .9vw; /* Smaller font size for full text */
    color: #f0f0f0; /* Light color for text */
    z-index: 3;
    background-color: #0b291a;

 }






 .frame-4 {
    position: absolute;
    width: 75%;
    height:6%;
    top:11%;
    left: 22%;
    background-color: transparent;
    border-radius: 16px;
    display: flex;
    direction: row;
    gap: 2%;
    justify-content: flex-start;

}

 .frame-5 {
    display: flex;
    width: 10%;
    height: 10%;
    justify-content: flex-start;
  margin-left: 1%;
}

.update {
    position: relative;
    width: 30px;
    height: 30px;

}


 .text-wrapper-5 {
    position: relative;
    width: fit-content;

    font-weight:bold;
    color: #0b291a;
    font-size: 20px;
    letter-spacing: var(--highlights-letter-spacing);
    line-height: var(--highlights-line-height);
    white-space: nowrap;
    font-style: var(--highlights-font-style);
}

/* Style for paragraph inside text-wrapper-5 */
.text-wrapper-5 p {
    margin: 0;
    padding: 0;
    font-weight: bold;
    color: #ff6600; /* Changed color to orange to make it obvious */
    font-size: 20px;
    white-space: nowrap;
}

 .frame-6 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 85%;
    align-items: center;
    position: absolute;
    top: 15%;
}

 .frame-7 {
    display: flex;
    align-items: center;

    padding: 0px 10px;
    position: relative;
    width: 100%;
    height: 50%;

}



 .text-wrapper-6 {
    position: relative;
    width: 20%;

    font-family: "Roboto-Medium", Helvetica;
    font-weight: bold;
    color: #000000;
    font-size: 25px;
    letter-spacing: 0;
    line-height: 18px;
    white-space: nowrap;
    left: 10%;

}

.data-filled {
    position: relative;
    width: 35%;
    height: 45px;
    background-color: #ffffff;
    border-radius: 16px;
   border: 1px solid;
    border-color: #0b291a;
    right: -25%;

}

.textarea{
    position: relative;
    width: 40%;
    height: 90px;
    border-radius: 16px;
    border: 1px solid;
     border-color: #0b291a;
     right: -25%;
}






 .frame-9 {
    display: flex;
    position: relative;
    width: 24%;
    height: 45px;
    align-items: center;
    justify-content: center;
   border: hidden;
    position: relative;
    background-color: #aedf32;
    border-radius: 14px;

}

.double-left1 {
    position: relative;
    width: 25px;
    height: 25px;


}


.btntext{
    font-size: 15px;
    font-weight: 40px;
    color: #ffffff;

}

/* Search dropdown container */
.search-dropdown-container {
    position: relative;
    display: inline-block;
    width: 24%;
    height: 45px;
}

.search{
    display: flex;
    position: relative;
    width: 100%;
    height: 45px;
    align-items: center;
    justify-content: center;
    border: hidden;
    border-radius: 14px;
    background-color: #c1d8b9;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search:hover {
    background-color: #a8c49a;
}

/* Search dropdown arrow */
.search-dropdown-arrow {
    font-size: 12px;
    color: #a9a9a9;
    transition: transform 0.3s ease;
    margin-left: 8px;
}

.search-dropdown-container.active .search-dropdown-arrow {
    transform: rotate(180deg);
}

.search-dropdown-container.active .search {
    background-color: #a8c49a;
    transform: translateY(2px);
}

/* Search dropdown menu */
.search-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: #c1d8b9;
    border: 2px solid #a8c49a;
    border-radius: 14px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 5px;
    overflow: hidden;
}

.search-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.search-option {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    gap: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: #a9a9a9;
}

.search-option:last-child {
    border-bottom: none;
}

.search-option:hover {
    background-color: #a8c49a;
    color: #ffffff;
}

.search-option.active {
    background-color: #a8c49a;
    color: #ffffff;
    font-weight: bold;
}

.search-icon {
    font-size: 18px;
    display: inline-block;
    transition: all 0.3s ease;
}

.search-option span {
    font-size: 16px;
    font-weight: 500;
    color: inherit;
}

/* Search input containers */
.search-input-container {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    border-bottom: 1px solid #e0e0e0;
}

.search-input {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #c1d8b9;
    border-radius: 8px;
    font-size: 14px;
    margin-bottom: 8px;
    outline: none;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    border-color: #a8c49a;
    box-shadow: 0 0 0 2px rgba(193, 216, 185, 0.2);
}

.search-input::placeholder {
    color: #999;
    font-style: italic;
}

.search-buttons {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.search-btn-small {
    padding: 6px 12px;
    background-color: #c1d8b9;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn-small:hover {
    background-color: #a8c49a;
    transform: translateY(-1px);
}

.search-btn-cancel {
    padding: 6px 12px;
    background-color: #e0e0e0;
    color: #666;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn-cancel:hover {
    background-color: #d0d0d0;
    transform: translateY(-1px);
}

/* Expanded dropdown when input is shown */
.search-dropdown-menu.expanded {
    min-height: auto;
    max-height: 400px;
}

/* Filter dropdown container */
.filter-dropdown-container {
    position: relative;
    display: inline-block;
    width: 24%;
    height: 45px;
}

.filter-dropdown-container .frame-9 {
    width: 100%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-dropdown-container .frame-9:hover {
    background-color: #8fb728;
}

/* Filter dropdown arrow */
.filter-dropdown-arrow {
    font-size: 12px;
    color: #ffffff;
    transition: transform 0.3s ease;
    margin-left: 8px;
}

.filter-dropdown-container.active .filter-dropdown-arrow {
    transform: rotate(180deg);
}

.filter-dropdown-container.active .frame-9 {
    background-color: #8fb728;
    transform: translateY(2px);
}

/* Filter dropdown menu */
.filter-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #aedf32;
    border-radius: 14px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 2px solid #8fb728;
    padding: 16px;
    min-width: 280px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    margin-top: 5px;
}

.filter-dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.filter-section {
    margin-bottom: 14px;
}

.filter-section label {
    display: block;
    font-size: 14px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 6px;
}

.filter-select {
    width: 100%;
    padding: 8px 12px;
    border: 2px solid #8fb728;
    border-radius: 8px;
    font-size: 14px;
    background-color: #ffffff;
    color: #333;
    outline: none;
    transition: border-color 0.3s ease;
}

.filter-select:focus {
    outline: none;
    border-color: #8fb728;
    box-shadow: 0 0 0 2px rgba(174, 223, 50, 0.2);
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-top: 18px;
}

.filter-apply-btn, .filter-clear-btn {
    flex: 1;
    padding: 10px 14px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-apply-btn {
    background: #aedf32;
    color: #ffffff;
    border: 2px solid #8fb728;
}

.filter-apply-btn:hover {
    background: #8fb728;
    transform: translateY(-1px);
}

.filter-clear-btn {
    background: #ffffff;
    color: #8fb728;
    border: 2px solid #8fb728;
}

.filter-clear-btn:hover {
    background: #8fb728;
    color: #ffffff;
    transform: translateY(-1px);
}

.filter-dropdown-container {
    z-index: 100;
}
.btntext1{
    font-size: 20px;
    font-weight: 40px;
    color: #a9a9a9  ;
}

.parent1{
    position: absolute;
    width: 75%;
    height: 77%;
    top:18%;
    left: 22%;
    background-color: #ffffff;
    border-radius: 16px;
    display: flex;
    direction: column;
   justify-content: flex-start;
    align-items:space-evenly;
    padding-top: 1%;

}

.checkbox{
    width: 15px;
    height: 15px;
   accent-color: #dfdddd66;
 }

 .tablecontainer {
    margin-top: 3%;
    width: 100%;
    height: 90%;
    position: absolute;

    overflow: hidden; /* Hide overflow on the container itself */
}

table {
    width: 100%;
    border-collapse: collapse;
    position: relative;
}

thead {
    display: table;
    width: calc(100% - 8px); /* Adjust for scrollbar width */
    table-layout: fixed;
    background-color: #ffffff; /* Keep header visible */
}

thead tr {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-bottom: .5px solid rgb(51, 51, 51) ; /* Green line matching your theme */
}
tbody {
    display: block;
    max-height: 400px; /* Set the height you want for the scrollable area */
    overflow-y: auto; /* Enable vertical scrolling */
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: #aedf32 #f1f1f1; /* For Firefox - thumb and track colors */
    width: 100%;
}

/* Styling the scrollbar for WebKit browsers */
tbody::-webkit-scrollbar {
    width: 8px;
}

tbody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

tbody::-webkit-scrollbar-thumb {
    background: #aedf32; /* Match your button color */
    border-radius: 4px;
}

tbody::-webkit-scrollbar-thumb:hover {
    background: #8fb728; /* Darker shade for hover */
}

tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

th, td {
    padding: 8px;
    text-align: left;
    font-size: 16px;
}

th {
    font-size: 16px;
    font-weight: bold;
}

/* Simple tooltip style */
.acronym {
    cursor: help;
    border-bottom: 1px dotted #0b291a;
    position: relative;
}


 /* Media Queries for Responsive Design */

/* Large screens (desktops) */
@media screen and (max-width: 1200px) {
    .navbar, .parent1 {
      width: 72%;
      left: 25%;
    }

    .side-bar {
      width: 22%;
    }

    .frame {
      left: 85%;
    }

    .data-filled, .textarea {
      width: 30%;
    }
  }

  /* Medium screens (tablets) */
  @media screen and (max-width: 992px) {
    .navbar, .parent1, .frame-4 {
      width: 68%;
      left: 30%;
    }

    .side-bar {
      width: 25%;
    }

    .frame {
      left: 80%;
    }

    .text-wrapper-6 {
      width: 25%;
      font-size: 20px;
    }

    .data-filled, .textarea {
      width: 40%;
      right: -20%;
    }

    .frame-9 {
      width: 30%;
    }

    table {
      font-size: 12px;
    }
  }

  /* Small screens (landscape phones) */
  @media screen and (max-width: 768px) {
    .navbar, .parent1, .frame-4 {
      width: 65%;
      left: 32%;
    }

    .side-bar {
      width: 28%;
    }

    .text-wrapper-3 {
      font-size: 6vw;
    }

    .text-wrapper-4 {
      font-size: 1.2vw;
    }

    .frame-7 {
      flex-direction: column;
      align-items: flex-start;
      height: auto;
      padding: 15px;
    }

    .text-wrapper-6 {
      width: 100%;
      left: 0;
      margin-bottom: 10px;
    }

    .data-filled, .textarea {
      width: 100%;
      right: 0;
      margin-bottom: 15px;
    }

    .frame-9 {
      width: 50%;
      margin: 0 auto;
    }

    .tablecontainer {
      margin-top: 5%;
    }
  }

  /* Extra small screens (portrait phones) */
  @media screen and (max-width: 576px) {
    .feed {
      padding: 0;
    }

    .navbar {
      width: 90%;
      left: 5%;
      top: 2%;
    }

    .side-bar {
      width: 0;
      left: -100%;
      display: none;
    }

    .parent1, .frame-4 {
      width: 90%;
      left: 5%;
      top: 12%;
    }

    .frame-4 {
      top: 8%;
    }

    .frame-5 {
      width: 20%;
    }

    .text-wrapper-5 {
      font-size: 16px;
    }

    .frame-9 {
      width: 70%;
    }

    .search-dropdown-container {
      width: 40%;
    }

    .search {
      width: 100%;
    }

    .filter-dropdown-container {
      width: 40%;
    }

    .filter-dropdown-container .frame-9 {
      width: 100%;
    }

    .btntext {
      font-size: 14px;
    }

    .tablecontainer {
      margin-top: 8%;
    }

    th, td {
      padding: 5px;
      font-size: 12px;
    }

    tbody {
      max-height: 300px;
    }
  }

  /* For very small screens */
  @media screen and (max-width: 400px) {
    .navbar {
      height: 45px;
    }

    .notification-bell {
      width: 40px;
      height: 40px;
    }

    .frame {
      width: 100px;
      height: 45px;
    }

    .parent1 {
      height: 80%;
    }

    .frame-9 {
      height: 40px;
    }

    .btntext {
      font-size: 12px;
    }
  }