@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0%;
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}

.choose-update-milch {

    background-color: transparent;
    display: flex;
    flex-direction: row;
    justify-content: center;
    padding: 2%;
    width: 100%;
    height: 100%;
}



 .frame-4 {
    position: absolute;
    width: 55%;
    height: 75%;
    top:12%;
    left: 24%;
    background-color:#fff;
    border-radius: 16px;
    display: flex;
    direction: column;

    align-items: flex-start;
}

.choose-update-milch .frame-5 {
    display: flex;
    width: 10%;
    height: 15%;
    align-items: center;
    justify-content: flex-start;
    gap: 15px;
    padding: 10px;
    position: relative;

    left: 1%;

}

 .update {
   position: relative;
    width: 40px;
    height: 40px;

}


 .text-wrapper-5 {
    position: relative;
    width: fit-content;

    font-weight:bold;
    color: #0b291a;
    font-size: 20px;
    letter-spacing: var(--highlights-letter-spacing);
    line-height: var(--highlights-line-height);
    white-space: nowrap;
    font-style: var(--highlights-font-style);
}

.choose-update-milch .frame-6 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 85%;
    align-items: center;
    position: absolute;
    top: 15%;
}

 .frame-7 {
    display: flex;
    align-items: center;

    padding: 0px 10px;
    position: relative;
    width: 100%;
    height: 50%;

}



.choose-update-milch .text-wrapper-6 {
    position: relative;
    width: 20%;

    font-family: "Roboto-Medium", Helvetica;

    color: #000000;
    font-size: 18px;
    letter-spacing: 0;
    line-height: 18px;
    white-space: nowrap;
    left: 20%;

}

.choose-update-milch .data-filled {
    position: absolute;
    width: 25%;
    height: 65px;
    background-color: #f1f1f1;
    border-radius: 12px;
   border: hidden;
    border-color: #0b291a;
   left: 50%;

}

.textarea{
    position: absolute;
    width: 25%;
    height: 80px;
    border-radius: 12px;
    border: hidden;
     background-color:#f1f1f1;
    left: 50%;
}

.choose-update-milch .frame-8 {
    display: flex;
    align-items: center;

    padding: 0px 10px;
    position: relative;
    width: 100%;
    height: 50%;
}


.choose-update-milch .frame-9 {
    display: flex;
   position: absolute;
    width: 40%;
    height: 60px;
    align-items: center;
    justify-content: center;
   border: hidden;
    position: relative;
    background-color: #aedf32;
    border-radius: 12px;
   left: 50%;

}

.btntext{
    font-size: 25px;
    font-weight: 40px;
    color: #ffffff;
}
.update1 {
    position: relative;
    width: 30px;
    height: 30px;

}

/* Close button removed - handled by external close button in animal.js */

