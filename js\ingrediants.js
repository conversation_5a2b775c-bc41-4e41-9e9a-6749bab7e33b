document.addEventListener('DOMContentLoaded', () => {
    // Get elements
    const addNewIngredientBtn = document.querySelector('.frame-9:nth-child(1)');
    const searchBtn = document.querySelector('.search');
    const filterBtn = document.querySelector('.frame-9:nth-child(3)');
    const editIngredientBtn = document.querySelector('.frame-9:nth-child(4)');
    const deleteIngredientBtn = document.querySelector('.frame-9:nth-child(5)');
    const tableBody = document.querySelector('tbody');
    const seasonRadios = document.querySelectorAll('input[name="access"]');    // Function to load ingredients
    function loadIngredients() {
        const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
        console.log('Loaded ingredients:', ingredients); // Debug log
        tableBody.innerHTML = '';

        if (ingredients.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="7" style="text-align: center;">No ingredients found</td>';
            tableBody.appendChild(row);
            return;
        }

        // Sort ingredients by creation date (newest first)
        ingredients.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // Add each ingredient to the table
        ingredients.forEach((ingredient, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input type="radio" name="select" class="radio" data-name="${ingredient.name}">${index + 1}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${ingredient.name}</td>
                <td>${ingredient.type || 'N/A'}</td>
                <td>${ingredient.proteins || 'N/A'}%</td>
                <td>${ingredient.crudeFiber || 'N/A'}%</td>
                <td>${ingredient.tdn || 'N/A'}%</td>
                <td>${ingredient.me || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });

        // Add event listeners to radios
        const radios = document.querySelectorAll('.radio');
        radios.forEach(radio => {
            radio.addEventListener('change', handleRadioChange);
        });
    }

    // Handle radio selection
    function handleRadioChange(e) {
        const selectedCount = document.querySelectorAll('.radio:checked').length;
        
        if (selectedCount > 0) {
            editIngredientBtn.style.opacity = '1';
            deleteIngredientBtn.style.opacity = '1';
        } else {
            editIngredientBtn.style.opacity = '0.5';
            deleteIngredientBtn.style.opacity = '0.5';
        }
    }

    // Load ingredients initially
    loadIngredients();

    // Add event listeners to buttons
    addNewIngredientBtn.addEventListener('click', () => {
        window.location.href = 'addnewingrediant.html';
    });

    searchBtn.addEventListener('click', () => {
        const searchTerm = prompt('Enter ingredient name to search:');
        if (searchTerm) {
            const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
            const filteredIngredients = ingredients.filter(ing => 
                ing.name.toLowerCase().includes(searchTerm.toLowerCase())
            );
            displayFilteredIngredients(filteredIngredients);
        }
    });

    filterBtn.addEventListener('click', () => {
        const types = ['concentrates', 'roughages', 'millByProduct', 'oilseedByProduct', 'forages'];
        const type = prompt(`Select type to filter by:\n${types.join('\n')}`);
        
        if (type && types.includes(type.toLowerCase())) {
            const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
            const filteredIngredients = ingredients.filter(ing => 
                ing.type.toLowerCase() === type.toLowerCase()
            );
            displayFilteredIngredients(filteredIngredients);
        }    });    

    editIngredientBtn.addEventListener('click', () => {
        const selectedRadio = document.querySelector('.radio:checked');
        if (!selectedRadio) {
            alert('Please select an ingredient to edit');
            return;
        }
        const ingredientName = selectedRadio.dataset.name;
        window.location.href = `editingrediant.html?name=${encodeURIComponent(ingredientName)}`;
    });

    deleteIngredientBtn.addEventListener('click', () => {
        const selectedRadio = document.querySelector('.radio:checked');
        if (!selectedRadio) {
            alert('Please select an ingredient to delete');
            return;
        }

        if (confirm('Are you sure you want to delete this ingredient?')) {
            const ingredientName = selectedRadio.getAttribute('data-name');
            const ingredients = JSON.parse(localStorage.getItem('ingredients') || '[]');
            const updatedIngredients = ingredients.filter(ing => ing.name !== ingredientName);
            localStorage.setItem('ingredients', JSON.stringify(updatedIngredients));
            loadIngredients();
        }
    });

    // Function to display filtered ingredients
    function displayFilteredIngredients(ingredients) {
        tableBody.innerHTML = '';
        
        if (ingredients.length === 0) {
            const row = document.createElement('tr');
            row.innerHTML = '<td colspan="7" style="text-align: center;">No ingredients found</td>';
            tableBody.appendChild(row);
            return;
        }

        ingredients.forEach((ingredient, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input type="radio" name="select" class="radio" data-name="${ingredient.name}">${index + 1}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${ingredient.name}</td>
                <td>${ingredient.type || 'N/A'}</td>
                <td>${ingredient.proteins || 'N/A'}%</td>
                <td>${ingredient.crudeFiber || 'N/A'}%</td>
                <td>${ingredient.tdn || 'N/A'}%</td>
                <td>${ingredient.me || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });

        // Re-add event listeners
        const radios = document.querySelectorAll('.radio');
        radios.forEach(radio => {
            radio.addEventListener('change', handleRadioChange);
        });
    }

    // Initialize button states
    editIngredientBtn.style.opacity = '0.5';
    deleteIngredientBtn.style.opacity = '0.5';
});