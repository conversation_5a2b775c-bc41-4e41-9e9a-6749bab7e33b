/**
 * Update Milk Data Page - Complete OOP Implementation
 * This file provides comprehensive functionality for updating dairy animal milk production data
 * with real-time search, validation, data management, and user feedback
 *
 * @author: Animal Management System
 * @version: 1.0.0
 * @description: Object-oriented system for updating dairy animal milk production data with full CRUD operations
 */

// ==================== BASE CLASSES ====================

/**
 * Base Data Manager for localStorage operations
 * Provides common functionality for data management specific to dairy operations
 */
class BaseDairyDataManager {
    constructor() {
        this.storageKeys = {
            animals: 'animals',
            dairyAnimals: 'dairyAnimals',
            milkProduction: 'milkProduction',
            newbornAnimals: 'newbornAnimals'
        };
    }

    /**
     * Get data from localStorage with error handling
     * @param {string} key - Storage key
     * @returns {Array} - Array of data or empty array
     */
    getData(key) {
        try {
            const data = localStorage.getItem(this.storageKeys[key] || key);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error(`<PERSON>rror getting data for key ${key}:`, error);
            return [];
        }
    }

    /**
     * Save data to localStorage with error handling
     * @param {string} key - Storage key
     * @param {Array} data - Data to save
     * @returns {boolean} - Success status
     */
    saveData(key, data) {
        try {
            localStorage.setItem(this.storageKeys[key] || key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error(`Error saving data for key ${key}:`, error);
            return false;
        }
    }

    /**
     * Generate unique ID
     * @returns {string} - Unique identifier
     */
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    /**
     * Find dairy animal by code (case-insensitive)
     * @param {string} code - Animal code to search for
     * @returns {Object|null} - Found dairy animal or null
     */
    findDairyAnimalByCode(code) {
        if (!code || typeof code !== 'string') {
            return null;
        }

        const dairyAnimals = this.getData('dairyAnimals');
        return dairyAnimals.find(animal =>
            animal && animal.code && typeof animal.code === 'string' &&
            animal.code.toLowerCase() === code.toLowerCase()
        ) || null;
    }

    /**
     * Find main animal by code (case-insensitive)
     * @param {string} code - Animal code to search for
     * @returns {Object|null} - Found animal or null
     */
    findMainAnimalByCode(code) {
        if (!code || typeof code !== 'string') {
            return null;
        }

        const animals = this.getData('animals');
        return animals.find(animal =>
            animal && animal.code && typeof animal.code === 'string' &&
            animal.code.toLowerCase() === code.toLowerCase()
        ) || null;
    }

    /**
     * Check if animal is dairy type (includes both dairy and drying animals)
     * @param {string} code - Animal code to check
     * @returns {boolean} - True if animal is dairy type (dairy, drying, or milch)
     */
    isDairyAnimal(code) {
        const mainAnimal = this.findMainAnimalByCode(code);
        // Drying animals are a category of dairy animals, so include 'dairy', 'drying', and 'milch' types
        return mainAnimal &&
               (mainAnimal.type === 'dairy' || mainAnimal.type === 'drying' || mainAnimal.type === 'milch') &&
               mainAnimal.gender === 'female';
    }

    /**
     * Calculate average daily milk production
     * @param {Array} milkRecords - Array of milk production records
     * @param {number} days - Number of days to calculate average for
     * @returns {number} - Average daily milk production
     */
    calculateAverageDailyMilk(milkRecords, days = 30) {
        if (!milkRecords || milkRecords.length === 0) {
            return 0;
        }

        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        const recentRecords = milkRecords.filter(record =>
            new Date(record.date) >= cutoffDate
        );

        if (recentRecords.length === 0) {
            return 0;
        }

        const totalMilk = recentRecords.reduce((sum, record) => sum + (record.amount || 0), 0);
        return Math.round((totalMilk / recentRecords.length) * 100) / 100;
    }
}

/**
 * Validation Manager for dairy data validation
 * Handles all validation logic with comprehensive rules for dairy operations
 */
class DairyValidationManager {
    constructor() {
        this.rules = {
            required: ['code'],
            dairyRequired: ['dateOfBirth', 'weight', 'herdNumber'],
            conditionalRequired: {
                artificialInsemination: ['dateOfArtificialInsemination'],
                pregnant: ['expectedDateOfCalving']
            }
        };
    }

    /**
     * Validate single field with comprehensive rules
     * @param {string} fieldName - Name of the field
     * @param {*} value - Value to validate
     * @param {Object} formData - Complete form data for context
     * @returns {Array} - Array of error messages
     */
    validateField(fieldName, value, formData = {}) {
        const errors = [];

        // Required field validation
        if (this.rules.required.includes(fieldName) && !value) {
            errors.push(`${this.getFieldDisplayName(fieldName)} is required`);
        }

        // Dairy required fields
        if (this.rules.dairyRequired.includes(fieldName) && !value) {
            errors.push(`${this.getFieldDisplayName(fieldName)} is required for dairy animals`);
        }

        // Conditional required validation
        if (formData.madeArtificialInsemination === 'yes' &&
            this.rules.conditionalRequired.artificialInsemination.includes(fieldName) && !value) {
            errors.push(`${this.getFieldDisplayName(fieldName)} is required when artificial insemination is made`);
        }

        if (formData.statusOfInsemination === 'pregnant' &&
            this.rules.conditionalRequired.pregnant.includes(fieldName) && !value) {
            errors.push(`${this.getFieldDisplayName(fieldName)} is required for pregnant animals`);
        }

        // Specific field validations
        switch (fieldName) {
            case 'code':
                if (value && !/^[A-Za-z0-9]+$/.test(value)) {
                    errors.push('Code can only contain letters and numbers');
                }
                if (value && value.length > 20) {
                    errors.push('Code cannot be longer than 20 characters');
                }
                break;
            case 'weight':
                if (value && (isNaN(value) || parseFloat(value) <= 0)) {
                    errors.push('Weight must be a positive number');
                }
                if (value && parseFloat(value) > 2000) {
                    errors.push('Weight seems unrealistic (max 2000kg)');
                }
                if (value && parseFloat(value) < 200) {
                    errors.push('Weight seems too low for dairy cow (min 200kg)');
                }
                break;
            case 'dateOfBirth':
                if (value && new Date(value) > new Date()) {
                    errors.push('Date of birth cannot be in the future');
                }
                if (value && new Date(value) < new Date('1900-01-01')) {
                    errors.push('Date of birth cannot be before 1900');
                }
                // Check minimum age for dairy cow
                if (value) {
                    const birthDate = new Date(value);
                    const minAge = new Date();
                    minAge.setFullYear(minAge.getFullYear() - 1); // Minimum 1 year old
                    if (birthDate > minAge) {
                        errors.push('Dairy cow must be at least 1 year old');
                    }
                }
                break;
            case 'dateOfWeight':
                if (value && new Date(value) > new Date()) {
                    errors.push('Date of weight cannot be in the future');
                }
                break;
            case 'dateOfArtificialInsemination':
                if (value && new Date(value) > new Date()) {
                    errors.push('Date of artificial insemination cannot be in the future');
                }
                if (value && formData.dateOfBirth) {
                    const birthDate = new Date(formData.dateOfBirth);
                    const inseminationDate = new Date(value);
                    const minInseminationAge = new Date(birthDate);
                    minInseminationAge.setFullYear(minInseminationAge.getFullYear() + 1.5); // Minimum 1.5 years old
                    if (inseminationDate < minInseminationAge) {
                        errors.push('Artificial insemination date too early (cow must be at least 1.5 years old)');
                    }
                }
                break;
            case 'expectedDateOfCalving':
                if (value && formData.dateOfArtificialInsemination) {
                    const inseminationDate = new Date(formData.dateOfArtificialInsemination);
                    const expectedCalving = new Date(value);
                    const gestationPeriod = 280; // Average cow gestation period in days
                    const expectedDate = new Date(inseminationDate);
                    expectedDate.setDate(expectedDate.getDate() + gestationPeriod);

                    const diffDays = Math.abs((expectedCalving - expectedDate) / (1000 * 60 * 60 * 24));
                    if (diffDays > 30) {
                        errors.push('Expected calving date should be approximately 280 days after insemination');
                    }
                }
                break;
            case 'herdNumber':
                if (value && !/^[A-Za-z0-9]+$/.test(value)) {
                    errors.push('Herd number can only contain letters and numbers');
                }
                break;
        }

        return errors;
    }

    /**
     * Validate entire form data with cross-field validation
     * @param {Object} data - Form data to validate
     * @returns {Array} - Array of error messages
     */
    validateFormData(data) {
        const errors = [];

        // Validate all fields
        Object.entries(data).forEach(([field, value]) => {
            const fieldErrors = this.validateField(field, value, data);
            errors.push(...fieldErrors);
        });

        // Cross-field validations
        if (data.dateOfBirth && data.dateOfWeight) {
            if (new Date(data.dateOfWeight) < new Date(data.dateOfBirth)) {
                errors.push('Date of weight cannot be before date of birth');
            }
        }

        if (data.dateOfArtificialInsemination && data.dateOfBirth) {
            if (new Date(data.dateOfArtificialInsemination) < new Date(data.dateOfBirth)) {
                errors.push('Date of artificial insemination cannot be before date of birth');
            }
        }

        // Business rule validations
        if (data.madeArtificialInsemination === 'no' && data.statusOfInsemination) {
            errors.push('Cannot set insemination status if no artificial insemination was made');
        }

        if (data.statusOfInsemination === 'pregnant' && !data.expectedDateOfCalving) {
            errors.push('Expected date of calving is required for pregnant animals');
        }

        return errors;
    }

    /**
     * Get display name for field
     * @param {string} fieldName - Field name
     * @returns {string} - Human-readable field name
     */
    getFieldDisplayName(fieldName) {
        const displayNames = {
            code: 'Code',
            dateOfBirth: 'Date of Birth',
            dairyType: 'Dairy Type',
            madeArtificialInsemination: 'Made Artificial Insemination',
            weight: 'Weight',
            dateOfArtificialInsemination: 'Date of Artificial Insemination',
            dateOfWeight: 'Date of Weight',
            statusOfInsemination: 'Status of Insemination',
            herdNumber: 'Herd Number',
            healthcareNotes: 'Healthcare Notes',
            expectedDateOfCalving: 'Expected Date of Calving',
            takenVaccination: 'Vaccination Information'
        };
        return displayNames[fieldName] || fieldName;
    }
}

/**
 * UI Manager for user interface operations
 * Handles all UI interactions, notifications, and visual feedback for dairy operations
 */
class DairyUIManager {
    constructor() {
        this.notifications = [];
    }

    /**
     * Show field error with visual styling
     * @param {HTMLElement} fieldElement - Form field element
     * @param {string} message - Error message to display
     */
    showFieldError(fieldElement, message) {
        this.clearFieldError(fieldElement);

        // Add error styling
        fieldElement.style.borderColor = '#dc3545';
        fieldElement.style.backgroundColor = '#fff5f5';

        // Create error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.style.cssText = `
            position:relative;
            top:46%;
            left:-48%;
            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
            padding: 2px 0;
            animation: shake 0.3s ease-in-out;
        `;
        errorDiv.textContent = message;

        // Insert after field
        fieldElement.parentNode.insertBefore(errorDiv, fieldElement.nextSibling);
    }

    /**
     * Clear field error styling and message
     * @param {HTMLElement} fieldElement - Form field element
     */
    clearFieldError(fieldElement) {
        // Reset styling
        fieldElement.style.borderColor = '';
        fieldElement.style.backgroundColor = '';

        // Remove error message
        const errorDiv = fieldElement.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * Clear all field errors
     */
    clearAllFieldErrors() {
        const errorDivs = document.querySelectorAll('.field-error');
        errorDivs.forEach(div => div.remove());

        const fields = document.querySelectorAll('.data-filled, .textarea');
        fields.forEach(field => {
            field.style.borderColor = '';
            field.style.backgroundColor = '';
        });
    }

    /**
     * Show notification with different types and auto-dismiss
     * @param {string} message - Notification message
     * @param {string} type - Notification type (success, error, warning, info)
     * @param {number} duration - Auto-dismiss duration in milliseconds
     * @returns {HTMLElement} - Notification element
     */
    showNotification(message, type = 'info', duration = 5000) {
        // Remove existing notifications
        this.clearNotifications();

        const notification = document.createElement('div');
        notification.className = 'notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            animation: slideIn 0.3s ease-out;
        `;

        // Set colors based on type
        const colors = {
            success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
            error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
            warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
            info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
        };

        const color = colors[type] || colors.info;
        notification.style.backgroundColor = color.bg;
        notification.style.border = `1px solid ${color.border}`;
        notification.style.color = color.text;

        notification.textContent = message;

        // Add close button
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            float: right;
            margin-left: 10px;
            cursor: pointer;
            font-size: 18px;
            line-height: 1;
        `;
        closeBtn.onclick = () => notification.remove();
        notification.appendChild(closeBtn);

        document.body.appendChild(notification);

        // Auto remove
        if (duration > 0) {
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, duration);
        }

        // Add animation styles if not already present
        if (!document.querySelector('#dairy-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'dairy-notification-styles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes shake {
                    0%, 100% { transform: translateX(0); }
                    25% { transform: translateX(-5px); }
                    75% { transform: translateX(5px); }
                }
            `;
            document.head.appendChild(style);
        }

        this.notifications.push(notification);
        return notification;
    }

    /**
     * Clear all notifications
     */
    clearNotifications() {
        this.notifications.forEach(notification => {
            if (notification.parentNode) {
                notification.remove();
            }
        });
        this.notifications = [];
    }

    /**
     * Show loading state on button
     * @param {HTMLElement} button - Button element
     * @param {string} text - Loading text
     */
    showLoading(button, text = 'Updating...') {
        if (!button) return;

        button.disabled = true;
        const btnText = button.querySelector('.btntext');
        if (btnText) {
            button.dataset.originalText = btnText.textContent;
            btnText.textContent = text;
        }
        button.style.opacity = '0.7';
    }

    /**
     * Hide loading state on button
     * @param {HTMLElement} button - Button element
     */
    hideLoading(button) {
        if (!button) return;

        button.disabled = false;
        const btnText = button.querySelector('.btntext');
        if (btnText && button.dataset.originalText) {
            btnText.textContent = button.dataset.originalText;
            delete button.dataset.originalText;
        }
        button.style.opacity = '1';
    }

    /**
     * Highlight field with success styling
     * @param {HTMLElement} fieldElement - Form field element
     */
    highlightFieldSuccess(fieldElement) {
        fieldElement.style.borderColor = '#28a745';
        fieldElement.style.backgroundColor = '#f8fff9';

        setTimeout(() => {
            fieldElement.style.borderColor = '';
            fieldElement.style.backgroundColor = '';
        }, 2000);
    }

    /**
     * Show dairy-specific status indicator
     * @param {string} status - Status to show (dairy, drying, pregnant, etc.)
     * @param {HTMLElement} container - Container element
     */
    showDairyStatus(status, container) {
        if (!container) return;

        const statusIndicator = document.createElement('div');
        statusIndicator.className = 'dairy-status-indicator';
        statusIndicator.style.cssText = `
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: bold;
            margin-left: 8px;
        `;

        const statusColors = {
            dairy: { bg: '#d4edda', text: '#155724', label: '🥛 Dairy' },
            drying: { bg: '#fff3cd', text: '#856404', label: '🌾 Drying' },
            pregnant: { bg: '#f8d7da', text: '#721c24', label: '🤱 Pregnant' },
            inseminated: { bg: '#d1ecf1', text: '#0c5460', label: '💉 Inseminated' }
        };

        const statusConfig = statusColors[status] || statusColors.dairy;
        statusIndicator.style.backgroundColor = statusConfig.bg;
        statusIndicator.style.color = statusConfig.text;
        statusIndicator.textContent = statusConfig.label;

        container.appendChild(statusIndicator);
    }
}

// ==================== MAIN CLASSES ====================

/**
 * Dairy Data Manager - Handles all data operations for updating dairy animals
 * Extends BaseDairyDataManager with update-specific functionality
 */
class DairyDataManager extends BaseDairyDataManager {
    constructor() {
        super();
    }

    /**
     * Update dairy animal in both main table and dairy-specific table
     * @param {string} animalCode - Code of animal to update
     * @param {Object} updateData - New data for the dairy animal
     * @returns {Object} - Updated animal object
     */
    updateDairyAnimal(animalCode, updateData) {
        if (!animalCode) {
            throw new Error('Animal code is required for update');
        }

        if (!updateData) {
            throw new Error('Update data is required');
        }

        // Verify this is a dairy animal
        if (!this.isDairyAnimal(animalCode)) {
            throw new Error(`Animal "${animalCode}" is not a dairy animal or does not exist`);
        }

        const timestamp = new Date().toISOString();

        try {
            // 1. Update main animals table
            const updatedMainAnimal = this.updateMainAnimalTable(animalCode, updateData, timestamp);

            // 2. Update dairy-specific table
            const updatedDairyAnimal = this.updateDairyAnimalTable(animalCode, updateData, timestamp);

            console.log(`✅ Dairy animal ${animalCode} updated successfully:`, {
                mainTable: true,
                dairyTable: true,
                milkProduction: updateData.milkProduction || 'not updated'
            });

            return {
                mainAnimal: updatedMainAnimal,
                dairyAnimal: updatedDairyAnimal
            };

        } catch (error) {
            console.error('❌ Error updating dairy animal:', error);
            throw error;
        }
    }

    /**
     * Update animal in main animals table
     * @param {string} animalCode - Animal code
     * @param {Object} updateData - Update data
     * @param {string} timestamp - Update timestamp
     * @returns {Object} - Updated animal
     */
    updateMainAnimalTable(animalCode, updateData, timestamp) {
        const animals = this.getData('animals');
        const animalIndex = animals.findIndex(a =>
            a.code && a.code.toLowerCase() === animalCode.toLowerCase()
        );

        if (animalIndex === -1) {
            throw new Error(`Animal with code "${animalCode}" not found in main table`);
        }

        const currentAnimal = animals[animalIndex];

        // Create updated animal object (only update relevant fields for main table)
        const mainTableFields = {
            weight: updateData.weight,
            dateOfWeight: updateData.dateOfWeight,
            dateOfBirth: updateData.dateOfBirth,
            herdNumber: updateData.herdNumber,
            healthcareNotes: updateData.healthcareNotes,
            takenVaccination: updateData.takenVaccination
        };

        // Update type field based on dairy type selection
        if (updateData.dairyType) {
            // Show the specific type (dairy or drying) in both main and dairy tables
            // This allows proper filtering and display while maintaining dairy animal functionality
            mainTableFields.type = updateData.dairyType;
        }

        // Filter out undefined values
        const filteredFields = Object.fromEntries(
            Object.entries(mainTableFields).filter(([_, value]) => value !== undefined)
        );

        const updatedAnimal = {
            ...currentAnimal,
            ...filteredFields,
            updatedAt: timestamp
        };

        // Update in array
        animals[animalIndex] = updatedAnimal;

        if (!this.saveData('animals', animals)) {
            throw new Error('Failed to update main animals table');
        }

        console.log(`📋 Updated in main animals table: ${animalCode}`);
        return updatedAnimal;
    }

    /**
     * Update animal in dairy animals table
     * @param {string} animalCode - Animal code
     * @param {Object} updateData - Update data
     * @param {string} timestamp - Update timestamp
     * @returns {Object} - Updated dairy animal
     */
    updateDairyAnimalTable(animalCode, updateData, timestamp) {
        const dairyAnimals = this.getData('dairyAnimals');
        const dairyIndex = dairyAnimals.findIndex(a =>
            a.code && a.code.toLowerCase() === animalCode.toLowerCase()
        );

        if (dairyIndex === -1) {
            throw new Error(`Dairy animal with code "${animalCode}" not found in dairy table`);
        }

        const currentDairyAnimal = dairyAnimals[dairyIndex];

        // Create updated dairy animal object
        const updatedDairyAnimal = {
            ...currentDairyAnimal,
            ...updateData,
            updatedAt: timestamp
        };

        // Calculate average daily milk if milk production data is provided
        if (updateData.milkProduction) {
            const milkRecords = this.getMilkProductionRecords(animalCode);
            updatedDairyAnimal.averageDailyMilk = this.calculateAverageDailyMilk(milkRecords);
        }

        // Update pregnancy and insemination status
        if (updateData.statusOfInsemination === 'pregnant') {
            updatedDairyAnimal.isPregnant = true;
            updatedDairyAnimal.pregnancyDate = updateData.dateOfArtificialInsemination;
            updatedDairyAnimal.expectedCalvingDate = updateData.expectedDateOfCalving;
        }

        // Update dairy type status
        if (updateData.dairyType) {
            updatedDairyAnimal.dairyType = updateData.dairyType;
            updatedDairyAnimal.isDrying = updateData.dairyType === 'drying';
            // In dairy table, show the specific type (dairy or drying) for filtering and display
            updatedDairyAnimal.type = updateData.dairyType;
        }

        // Update in array
        dairyAnimals[dairyIndex] = updatedDairyAnimal;

        if (!this.saveData('dairyAnimals', dairyAnimals)) {
            throw new Error('Failed to update dairy animals table');
        }

        console.log(`🥛 Updated in dairy animals table: ${animalCode}`);
        return updatedDairyAnimal;
    }

    /**
     * Get milk production records for an animal
     * @param {string} animalCode - Animal code
     * @returns {Array} - Array of milk production records
     */
    getMilkProductionRecords(animalCode) {
        const milkProduction = this.getData('milkProduction');
        return milkProduction.filter(record =>
            record.animalCode && record.animalCode.toLowerCase() === animalCode.toLowerCase()
        );
    }

    /**
     * Add milk production record
     * @param {string} animalCode - Animal code
     * @param {Object} milkData - Milk production data
     * @returns {Object} - Created milk production record
     */
    addMilkProductionRecord(animalCode, milkData) {
        const milkProduction = this.getData('milkProduction');

        const milkRecord = {
            id: this.generateId(),
            animalCode: animalCode,
            date: milkData.date || new Date().toISOString().split('T')[0],
            amount: parseFloat(milkData.amount) || 0,
            quality: milkData.quality || 'normal',
            notes: milkData.notes || '',
            createdAt: new Date().toISOString()
        };

        milkProduction.push(milkRecord);

        if (!this.saveData('milkProduction', milkProduction)) {
            throw new Error('Failed to save milk production record');
        }

        // Update average daily milk in dairy table
        this.updateAverageDailyMilk(animalCode);

        console.log(`🥛 Added milk production record for ${animalCode}: ${milkRecord.amount}L`);
        return milkRecord;
    }

    /**
     * Update average daily milk production for an animal
     * @param {string} animalCode - Animal code
     */
    updateAverageDailyMilk(animalCode) {
        const milkRecords = this.getMilkProductionRecords(animalCode);
        const averageDailyMilk = this.calculateAverageDailyMilk(milkRecords);

        const dairyAnimals = this.getData('dairyAnimals');
        const dairyIndex = dairyAnimals.findIndex(a =>
            a.code && a.code.toLowerCase() === animalCode.toLowerCase()
        );

        if (dairyIndex !== -1) {
            dairyAnimals[dairyIndex].averageDailyMilk = averageDailyMilk;
            dairyAnimals[dairyIndex].totalMilkProduced = milkRecords.reduce((sum, record) => sum + record.amount, 0);
            dairyAnimals[dairyIndex].lastMilkingDate = milkRecords.length > 0 ?
                milkRecords[milkRecords.length - 1].date : null;

            this.saveData('dairyAnimals', dairyAnimals);
            console.log(`📊 Updated average daily milk for ${animalCode}: ${averageDailyMilk}L`);
        }
    }

    /**
     * Get comprehensive dairy animal data
     * @param {string} animalCode - Animal code
     * @returns {Object} - Complete dairy animal data
     */
    getCompleteDairyAnimalData(animalCode) {
        const mainAnimal = this.findMainAnimalByCode(animalCode);
        const dairyAnimal = this.findDairyAnimalByCode(animalCode);
        const milkRecords = this.getMilkProductionRecords(animalCode);

        if (!mainAnimal || !dairyAnimal) {
            return null;
        }

        return {
            ...mainAnimal,
            ...dairyAnimal,
            milkRecords: milkRecords,
            averageDailyMilk: this.calculateAverageDailyMilk(milkRecords),
            totalMilkProduced: milkRecords.reduce((sum, record) => sum + record.amount, 0),
            lastMilkingDate: milkRecords.length > 0 ? milkRecords[milkRecords.length - 1].date : null
        };
    }

    /**
     * Search dairy animals by various criteria
     * @param {string} searchTerm - Search term
     * @param {string} searchField - Field to search in (optional)
     * @returns {Array} - Matching dairy animals
     */
    searchDairyAnimals(searchTerm, searchField = null) {
        if (!searchTerm) {
            return this.getData('dairyAnimals');
        }

        const dairyAnimals = this.getData('dairyAnimals');
        const term = searchTerm.toLowerCase();

        return dairyAnimals.filter(animal => {
            if (searchField) {
                const fieldValue = animal[searchField];
                return fieldValue && fieldValue.toString().toLowerCase().includes(term);
            } else {
                // Search across multiple fields
                const searchableFields = ['code', 'herdNumber', 'healthcareNotes', 'dairyType'];
                return searchableFields.some(field => {
                    const fieldValue = animal[field];
                    return fieldValue && fieldValue.toString().toLowerCase().includes(term);
                });
            }
        });
    }

    /**
     * Get dairy statistics
     * @returns {Object} - Statistics object
     */
    getDairyStatistics() {
        const dairyAnimals = this.getData('dairyAnimals');
        const milkProduction = this.getData('milkProduction');

        const totalDairyAnimals = dairyAnimals.length;
        // Active dairy animals are those currently producing milk (type = 'dairy')
        const activeDairyAnimals = dairyAnimals.filter(a =>
            a.type === 'dairy' || a.dairyType === 'dairy' || (!a.type && !a.dairyType)
        ).length;
        const pregnantAnimals = dairyAnimals.filter(a => a.isPregnant).length;
        // Drying animals (type = 'drying' or dairyType = 'drying')
        const dryingAnimals = dairyAnimals.filter(a =>
            a.type === 'drying' || a.dairyType === 'drying' || a.type === 'milch'
        ).length;

        const totalMilkProduced = milkProduction.reduce((sum, record) => sum + record.amount, 0);
        const averageDailyMilkAll = totalDairyAnimals > 0 ?
            dairyAnimals.reduce((sum, animal) => sum + (animal.averageDailyMilk || 0), 0) / totalDairyAnimals : 0;

        return {
            totalDairyAnimals, // Includes both dairy and drying animals
            activeDairyAnimals, // Only currently producing animals (type = 'dairy')
            pregnantAnimals,
            dryingAnimals, // Dairy animals in drying period (type = 'drying')
            totalMilkProduced: Math.round(totalMilkProduced * 100) / 100,
            averageDailyMilkAll: Math.round(averageDailyMilkAll * 100) / 100,
            milkProductionRecords: milkProduction.length
        };
    }
}

/**
 * Main Update Milk Data Controller Class
 * Orchestrates all components and manages the update milk data page
 */
class UpdateMilkData {
    constructor() {
        this.dataManager = new DairyDataManager();
        this.validationManager = new DairyValidationManager();
        this.uiManager = new DairyUIManager();
        this.formData = {};
        this.currentDairyAnimal = null;
        this.isInitialized = false;
        this.searchTimeout = null;
        this.notFoundTimeout = null;
    }

    /**
     * Initialize the controller
     */
    init() {
        try {
            console.log('🚀 Initializing Update Milk Data system...');

            this.setupFormElements();
            this.setupEventListeners();
            this.setupValidation();
            this.setupReadyMode();

            this.isInitialized = true;
            console.log('✅ Update Milk Data system initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize Update Milk Data system:', error);
            this.uiManager.showNotification('Failed to initialize the form. Please refresh the page.', 'error');
        }
    }

    /**
     * Setup form elements
     */
    setupFormElements() {
        this.elements = {
            // Code input serves as both search and editable field
            codeInput: document.querySelector('.data-filled'),

            // Type radio buttons (animal type)
            animalTypeRadios: document.querySelectorAll('input[name="access"]'),

            // Dairy type radio buttons
            dairyTypeRadios: document.querySelectorAll('.frame-11 input[name="access"]'),

            // Insemination radio buttons
            inseminationRadios: document.querySelectorAll('.frame-11 input[name="access"]'),

            // Status radio buttons
            statusRadios: document.querySelectorAll('.frame-12 input[name="access"]'),

            // Input fields
            dateOfBirthInput: document.querySelectorAll('.data-filled')[1],
            weightInput: document.querySelectorAll('.data-filled')[2],
            dateOfArtificialInseminationInput: document.querySelectorAll('.data-filled')[3],
            dateOfWeightInput: document.querySelectorAll('.data-filled')[4],
            herdNumberInput: document.querySelectorAll('.data-filled')[5],
            expectedDateOfCalvingInput: document.querySelectorAll('.data-filled')[6],

            // Textarea fields
            healthcareNotesInput: document.querySelectorAll('.textarea')[0],
            vaccinationInput: document.querySelectorAll('.textarea')[1],

            // Update button
            updateButton: document.querySelector('.frame-79')
        };

        // Validate that all elements exist
        const missingElements = [];
        Object.entries(this.elements).forEach(([key, element]) => {
            if (!element || (Array.isArray(element) && element.length === 0)) {
                missingElements.push(key);
            }
        });

        if (missingElements.length > 0) {
            console.warn(`Some form elements not found: ${missingElements.join(', ')}`);
        }

        console.log('📋 Form elements found and mapped');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Code field handles both search and editing
        if (this.elements.codeInput) {
            // Real-time search as user types
            this.elements.codeInput.addEventListener('input', (e) => {
                const value = e.target.value.trim();

                // Update form data
                this.formData.code = value;

                // Clear field error when user starts typing
                this.uiManager.clearFieldError(e.target);

                // Perform real-time search
                this.handleRealTimeSearch(value);
            });

            // Handle Enter key for immediate search
            this.elements.codeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.handleSearch();
                }
            });

            // Handle focus to show instructions
            this.elements.codeInput.addEventListener('focus', () => {
                if (!this.currentDairyAnimal) {
                    this.uiManager.showNotification('Start typing a dairy animal code to load data...', 'info', 2000);
                }
            });

            // Handle blur for validation
            this.elements.codeInput.addEventListener('blur', (e) => {
                this.validateSingleField(e.target, 'code');
            });
        }

        // Animal type radio buttons (should be disabled for dairy-only)
        this.elements.animalTypeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked && e.target.id !== 'dairy') {
                    this.uiManager.showNotification('Only dairy animals can be updated on this page', 'warning');
                    // Auto-select dairy
                    document.getElementById('dairy').checked = true;
                    this.formData.animalType = 'dairy';
                }
            });
        });

        // Dairy type radio buttons
        this.elements.dairyTypeRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.handleDairyTypeChange(e.target.id);
                }
            });
        });

        // Insemination radio buttons
        this.elements.inseminationRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.handleInseminationChange(e.target.id);
                }
            });
        });

        // Status radio buttons
        this.elements.statusRadios.forEach(radio => {
            radio.addEventListener('change', (e) => {
                if (e.target.checked) {
                    this.handleStatusChange(e.target.id);
                }
            });
        });

        // Other input field changes
        const otherInputs = [
            { element: this.elements.dateOfBirthInput, field: 'dateOfBirth' },
            { element: this.elements.weightInput, field: 'weight' },
            { element: this.elements.dateOfArtificialInseminationInput, field: 'dateOfArtificialInsemination' },
            { element: this.elements.dateOfWeightInput, field: 'dateOfWeight' },
            { element: this.elements.herdNumberInput, field: 'herdNumber' },
            { element: this.elements.expectedDateOfCalvingInput, field: 'expectedDateOfCalving' },
            { element: this.elements.healthcareNotesInput, field: 'healthcareNotes' },
            { element: this.elements.vaccinationInput, field: 'takenVaccination' }
        ];

        otherInputs.forEach(({ element, field }) => {
            if (element) {
                // Real-time validation on input
                element.addEventListener('input', (e) => {
                    this.handleInputChange(e, field);
                });

                // Validation on blur
                element.addEventListener('blur', (e) => {
                    this.validateSingleField(e.target, field);
                });
            }
        });

        // Update button
        if (this.elements.updateButton) {
            this.elements.updateButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleUpdate();
            });
        }

        console.log('🔗 Event listeners setup complete');
    }

    /**
     * Setup ready mode - enable fields for immediate editing
     */
    setupReadyMode() {
        this.setFormMode('ready');
        this.uiManager.showNotification('Start typing a dairy animal code to load and edit data', 'info', 3000);
    }

    /**
     * Set form mode (ready, search, or edit)
     * @param {string} mode - 'ready', 'search', or 'edit'
     */
    setFormMode(mode) {
        const otherFormFields = [
            ...this.elements.animalTypeRadios,
            ...this.elements.dairyTypeRadios,
            ...this.elements.inseminationRadios,
            ...this.elements.statusRadios,
            this.elements.dateOfBirthInput,
            this.elements.weightInput,
            this.elements.dateOfArtificialInseminationInput,
            this.elements.dateOfWeightInput,
            this.elements.herdNumberInput,
            this.elements.expectedDateOfCalvingInput,
            this.elements.healthcareNotesInput,
            this.elements.vaccinationInput
        ];

        if (mode === 'ready') {
            // Ready mode: All fields enabled but empty, waiting for dairy animal to be loaded

            // Code field is always enabled for search/edit
            if (this.elements.codeInput) {
                this.elements.codeInput.disabled = false;
                this.elements.codeInput.style.opacity = '1';
                this.elements.codeInput.placeholder = 'Type dairy animal code to load and edit data...';
            }

            // Other fields enabled but update button disabled
            otherFormFields.forEach(field => {
                if (field) {
                    field.disabled = false;
                    field.style.opacity = '1';
                }
            });

            if (this.elements.updateButton) {
                this.elements.updateButton.disabled = true;
                this.elements.updateButton.style.opacity = '0.5';
                this.elements.updateButton.title = 'Load a dairy animal first';
            }

        } else if (mode === 'edit') {
            // All fields enabled including code field
            if (this.elements.codeInput) {
                this.elements.codeInput.disabled = false;
                this.elements.codeInput.style.opacity = '1';
                this.elements.codeInput.placeholder = 'Dairy animal code (editable)';
            }

            otherFormFields.forEach(field => {
                if (field) {
                    field.disabled = false;
                    field.style.opacity = '1';
                }
            });

            if (this.elements.updateButton) {
                this.elements.updateButton.disabled = false;
                this.elements.updateButton.style.opacity = '1';
                this.elements.updateButton.title = 'Update dairy animal data';
            }
        }
    }

    /**
     * Handle real-time search as user types
     * @param {string} searchTerm - The search term
     */
    handleRealTimeSearch(searchTerm) {
        // Clear any existing timeout
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // If search term is empty, clear form and set to ready mode
        if (!searchTerm) {
            this.clearFormData();
            this.setFormMode('ready');
            return;
        }

        // Debounce the search to avoid too many lookups
        this.searchTimeout = setTimeout(() => {
            this.performRealTimeSearch(searchTerm);
        }, 300); // 300ms delay
    }

    /**
     * Perform the actual real-time search
     * @param {string} searchTerm - The search term
     */
    performRealTimeSearch(searchTerm) {
        console.log(`🔍 Real-time searching for dairy animal: ${searchTerm}`);

        // Check if this is a dairy animal
        if (!this.dataManager.isDairyAnimal(searchTerm)) {
            this.clearFormData();
            this.setFormMode('ready');
            this.showNotFoundFeedback(searchTerm, 'not_dairy');
            console.log(`❌ Animal "${searchTerm}" is not a dairy animal or does not exist`);
            return;
        }

        // Get complete dairy animal data
        const dairyAnimal = this.dataManager.getCompleteDairyAnimalData(searchTerm);

        if (dairyAnimal) {
            // Dairy animal found, load it into form immediately
            this.loadDairyAnimalIntoForm(dairyAnimal);
            this.setFormMode('edit');
            console.log(`✅ Dairy animal "${dairyAnimal.code}" loaded automatically`);
        } else {
            // Dairy animal not found, clear form but keep fields enabled
            this.clearFormData();
            this.setFormMode('ready');
            this.showNotFoundFeedback(searchTerm, 'not_found');
            console.log(`❌ Dairy animal "${searchTerm}" not found`);
        }
    }

    /**
     * Handle manual search (when Enter is pressed)
     */
    handleSearch() {
        const searchTerm = this.elements.codeInput.value.trim();

        if (!searchTerm) {
            this.uiManager.showNotification('Please enter a dairy animal code to search', 'warning');
            return;
        }

        console.log(`🔍 Manual search for dairy animal: ${searchTerm}`);

        // Check if this is a dairy animal
        if (!this.dataManager.isDairyAnimal(searchTerm)) {
            this.uiManager.showNotification(`Animal "${searchTerm}" is not a dairy animal or does not exist`, 'error');
            this.clearFormData();
            this.setFormMode('ready');
            return;
        }

        // Get complete dairy animal data
        const dairyAnimal = this.dataManager.getCompleteDairyAnimalData(searchTerm);

        if (!dairyAnimal) {
            this.uiManager.showNotification(`Dairy animal with code "${searchTerm}" not found`, 'error');
            this.clearFormData();
            this.setFormMode('ready');
            return;
        }

        // Dairy animal found, load it into form
        this.loadDairyAnimalIntoForm(dairyAnimal);
        this.setFormMode('edit');
        this.uiManager.showNotification(`Dairy animal "${dairyAnimal.code}" loaded successfully!`, 'success', 2000);
    }

    /**
     * Show visual feedback when animal is not found
     * @param {string} searchTerm - The search term that wasn't found
     * @param {string} reason - Reason for not found ('not_found' or 'not_dairy')
     */
    showNotFoundFeedback(searchTerm, reason = 'not_found') {
        if (this.elements.codeInput) {
            // Add red border to indicate not found
            this.elements.codeInput.style.borderColor = '#dc3545';
            this.elements.codeInput.style.backgroundColor = '#fff5f5';

            // Reset after 2 seconds
            setTimeout(() => {
                this.elements.codeInput.style.borderColor = '';
                this.elements.codeInput.style.backgroundColor = '';
            }, 2000);
        }

        // Only show notification for longer search terms to avoid spam
        if (searchTerm.length >= 3) {
            // Clear any existing notification timeout
            if (this.notFoundTimeout) {
                clearTimeout(this.notFoundTimeout);
            }

            // Show notification after a delay to avoid too many notifications
            this.notFoundTimeout = setTimeout(() => {
                const message = reason === 'not_dairy'
                    ? `"${searchTerm}" is not a dairy animal`
                    : `Dairy animal "${searchTerm}" not found`;
                this.uiManager.showNotification(message, 'warning', 2000);
            }, 1000);
        }
    }

    /**
     * Load dairy animal data into form
     * @param {Object} dairyAnimal - Dairy animal data to load
     */
    loadDairyAnimalIntoForm(dairyAnimal) {
        this.currentDairyAnimal = dairyAnimal;
        this.formData = { ...dairyAnimal };

        console.log('📝 Loading dairy animal into form:', dairyAnimal);

        // Debug date of birth field specifically
        console.log('🗓️ Date of birth debug:', {
            dateOfBirth: dairyAnimal.dateOfBirth,
            birthDate: dairyAnimal.birthDate,
            born: dairyAnimal.born,
            dateOfBirthInput: this.elements.dateOfBirthInput
        });

        // Add visual feedback to code input
        if (this.elements.codeInput) {
            this.elements.codeInput.style.borderColor = '#28a745';
            this.elements.codeInput.style.backgroundColor = '#f8fff9';
            setTimeout(() => {
                this.elements.codeInput.style.borderColor = '';
                this.elements.codeInput.style.backgroundColor = '';
            }, 2000);
        }

        // Set animal type to dairy (always)
        const dairyRadio = document.getElementById('dairy');
        if (dairyRadio) {
            dairyRadio.checked = true;
            this.formData.animalType = 'dairy';
        }

        // Set dairy type radio button
        let animalDairyType = null;

        // Determine dairy type from available fields
        if (dairyAnimal.dairyType) {
            animalDairyType = dairyAnimal.dairyType;
        } else if (dairyAnimal.type) {
            // Map type field to dairy type
            if (dairyAnimal.type === 'dairy') {
                animalDairyType = 'dairy';
            } else if (dairyAnimal.type === 'drying' || dairyAnimal.type === 'milch') {
                animalDairyType = 'drying';
            }
        }

        if (animalDairyType) {
            let radioId;

            if (animalDairyType === 'dairy') {
                radioId = 'milch'; // 'milch' radio button shows "Dairy" label
            } else if (animalDairyType === 'drying') {
                radioId = 'drying'; // 'drying' radio button shows "Drying" label
            }

            if (radioId) {
                const dairyTypeRadio = document.getElementById(radioId);
                if (dairyTypeRadio) {
                    dairyTypeRadio.checked = true;
                    this.handleDairyTypeChange(animalDairyType);
                }
            }
        } else {
            // Fallback: default to 'dairy' for all dairy animals
            const milchRadio = document.getElementById('milch');
            if (milchRadio) {
                milchRadio.checked = true;
                this.handleDairyTypeChange('dairy');
            }
        }

        // Set insemination radio button
        if (dairyAnimal.madeArtificialInsemination !== undefined || dairyAnimal.artificialInseminationStatus !== undefined) {
            // Use madeArtificialInsemination if available, otherwise check artificialInseminationStatus
            let hasInsemination = false;
            if (dairyAnimal.madeArtificialInsemination !== undefined) {
                hasInsemination = dairyAnimal.madeArtificialInsemination;
            } else if (dairyAnimal.artificialInseminationStatus) {
                hasInsemination = dairyAnimal.artificialInseminationStatus.toLowerCase() === 'yes';
            }

            const inseminationRadio = document.getElementById(hasInsemination ? 'yes' : 'no');
            if (inseminationRadio) {
                inseminationRadio.checked = true;
                this.handleInseminationChange(hasInsemination ? 'yes' : 'no');
            }
        }

        // Set status radio button
        if (dairyAnimal.statusOfInsemination) {
            const statusRadio = document.getElementById(dairyAnimal.statusOfInsemination);
            if (statusRadio) {
                statusRadio.checked = true;
                this.handleStatusChange(dairyAnimal.statusOfInsemination);
            }
        }

        // Fill input fields with smooth animation
        // Date of birth with multiple fallback field names and format handling
        const dateOfBirth = this.formatDateForInput(dairyAnimal.dateOfBirth || dairyAnimal.birthDate || dairyAnimal.born || '');
        this.fillFieldWithAnimation(this.elements.dateOfBirthInput, dateOfBirth);

        this.fillFieldWithAnimation(this.elements.weightInput, dairyAnimal.weight || '');
        this.fillFieldWithAnimation(this.elements.dateOfArtificialInseminationInput,
            this.formatDateForInput(dairyAnimal.dateOfArtificialInsemination || dairyAnimal.artificialInseminationDate || ''));
        this.fillFieldWithAnimation(this.elements.dateOfWeightInput,
            this.formatDateForInput(dairyAnimal.dateOfWeight || dairyAnimal.weightDate || ''));
        this.fillFieldWithAnimation(this.elements.herdNumberInput, dairyAnimal.herdNumber || '');
        this.fillFieldWithAnimation(this.elements.expectedDateOfCalvingInput,
            this.formatDateForInput(dairyAnimal.expectedDateOfCalving || dairyAnimal.expectedCalvingDate || ''));
        this.fillFieldWithAnimation(this.elements.healthcareNotesInput, dairyAnimal.healthcareNotes || '');
        this.fillFieldWithAnimation(this.elements.vaccinationInput, dairyAnimal.takenVaccination || '');

        // Show dairy status indicator
        const titleElement = document.querySelector('.text-wrapper-5');
        if (titleElement) {
            // Remove existing status indicators
            const existingIndicators = titleElement.parentNode.querySelectorAll('.dairy-status-indicator');
            existingIndicators.forEach(indicator => indicator.remove());

            // Add new status indicator
            const status = dairyAnimal.isPregnant ? 'pregnant' :
                          dairyAnimal.dairyType === 'drying' ? 'drying' : 'dairy';
            this.uiManager.showDairyStatus(status, titleElement.parentNode);
        }

        // Clear any previous errors
        this.uiManager.clearAllFieldErrors();
    }

    /**
     * Format date for input field (ensures YYYY-MM-DD format)
     * @param {string} dateValue - Date value in various formats
     * @returns {string} - Formatted date string for input field
     */
    formatDateForInput(dateValue) {
        if (!dateValue) return '';

        try {
            // Handle various date formats
            let date;

            // If it's already in YYYY-MM-DD format, return as is
            if (/^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
                return dateValue;
            }

            // Try to parse the date
            date = new Date(dateValue);

            // Check if date is valid
            if (isNaN(date.getTime())) {
                console.warn(`Invalid date format: ${dateValue}`);
                return '';
            }

            // Format as YYYY-MM-DD for input field
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');

            return `${year}-${month}-${day}`;

        } catch (error) {
            console.error(`Error formatting date: ${dateValue}`, error);
            return '';
        }
    }

    /**
     * Fill field with smooth animation
     * @param {HTMLElement} field - Field element
     * @param {string} value - Value to fill
     */
    fillFieldWithAnimation(field, value) {
        if (!field) {
            console.warn('⚠️ Field element not found for value:', value);
            return;
        }

        // Debug logging for date fields
        if (field.type === 'date') {
            console.log(`📅 Filling date field with value: "${value}"`);
        }

        // Add loading animation
        field.style.transition = 'all 0.3s ease';
        field.style.backgroundColor = '#e3f2fd';

        setTimeout(() => {
            field.value = value;
            field.style.backgroundColor = '#f8fff9';

            // Verify the value was set correctly
            if (field.type === 'date' && field.value !== value) {
                console.warn(`⚠️ Date field value mismatch. Expected: "${value}", Actual: "${field.value}"`);
            }

            setTimeout(() => {
                field.style.backgroundColor = '';
                field.style.transition = '';
            }, 500);
        }, 100);
    }

    /**
     * Handle dairy type change
     */
    handleDairyTypeChange(type) {
        this.formData.dairyType = type;
        console.log(`🥛 Dairy type changed to: ${type}`);

        // Clear previous validation errors
        this.uiManager.clearAllFieldErrors();

        // Show appropriate status
        if (type === 'drying') {
            this.uiManager.showNotification('Dairy cow set to drying period', 'info', 2000);
        }
    }

    /**
     * Handle artificial insemination change
     */
    handleInseminationChange(value) {
        this.formData.madeArtificialInsemination = value;
        console.log(`💉 Artificial insemination changed to: ${value}`);

        // Enable/disable related fields based on selection
        if (value === 'yes') {
            // Enable insemination date and status fields
            if (this.elements.dateOfArtificialInseminationInput) {
                this.elements.dateOfArtificialInseminationInput.disabled = false;
                this.elements.dateOfArtificialInseminationInput.style.opacity = '1';
            }
            this.elements.statusRadios.forEach(radio => {
                radio.disabled = false;
                radio.parentNode.style.opacity = '1';
            });
        } else {
            // Disable and clear insemination-related fields
            if (this.elements.dateOfArtificialInseminationInput) {
                this.elements.dateOfArtificialInseminationInput.disabled = true;
                this.elements.dateOfArtificialInseminationInput.style.opacity = '0.5';
                this.elements.dateOfArtificialInseminationInput.value = '';
            }
            if (this.elements.expectedDateOfCalvingInput) {
                this.elements.expectedDateOfCalvingInput.value = '';
            }
            this.elements.statusRadios.forEach(radio => {
                radio.disabled = true;
                radio.checked = false;
                radio.parentNode.style.opacity = '0.5';
            });
            this.formData.statusOfInsemination = null;
            this.formData.dateOfArtificialInsemination = null;
            this.formData.expectedDateOfCalving = null;
        }
    }

    /**
     * Handle insemination status change
     */
    handleStatusChange(status) {
        this.formData.statusOfInsemination = status;
        console.log(`📊 Insemination status changed to: ${status}`);

        // Enable/disable expected calving date based on status
        if (status === 'pregnant') {
            if (this.elements.expectedDateOfCalvingInput) {
                this.elements.expectedDateOfCalvingInput.disabled = false;
                this.elements.expectedDateOfCalvingInput.style.opacity = '1';
            }
            this.uiManager.showNotification('Cow is pregnant! Please set expected calving date.', 'success', 3000);
        } else {
            if (this.elements.expectedDateOfCalvingInput) {
                this.elements.expectedDateOfCalvingInput.disabled = true;
                this.elements.expectedDateOfCalvingInput.style.opacity = '0.5';
                this.elements.expectedDateOfCalvingInput.value = '';
            }
            this.formData.expectedDateOfCalving = null;
        }
    }

    /**
     * Handle input field changes
     */
    handleInputChange(event, fieldName) {
        const value = event.target.value.trim();
        this.formData[fieldName] = value;

        // Clear field error when user starts typing
        this.uiManager.clearFieldError(event.target);

        // Special handling for date fields
        if (fieldName === 'dateOfArtificialInsemination' && value) {
            this.calculateExpectedCalvingDate(value);
        }
    }

    /**
     * Calculate and suggest expected calving date
     * @param {string} inseminationDate - Date of artificial insemination
     */
    calculateExpectedCalvingDate(inseminationDate) {
        if (!inseminationDate || !this.elements.expectedDateOfCalvingInput) return;

        const insemDate = new Date(inseminationDate);
        const expectedDate = new Date(insemDate);
        expectedDate.setDate(expectedDate.getDate() + 280); // Average gestation period

        const expectedDateString = expectedDate.toISOString().split('T')[0];

        // Only set if field is empty or user hasn't manually set it
        if (!this.elements.expectedDateOfCalvingInput.value) {
            this.elements.expectedDateOfCalvingInput.value = expectedDateString;
            this.formData.expectedDateOfCalving = expectedDateString;

            // Show notification
            this.uiManager.showNotification(
                `Expected calving date calculated: ${expectedDate.toLocaleDateString()}`,
                'info',
                3000
            );
        }
    }

    /**
     * Validate single field
     */
    validateSingleField(fieldElement, fieldName) {
        const value = fieldElement.value.trim();
        const errors = this.validationManager.validateField(fieldName, value, this.formData);

        if (errors.length > 0) {
            this.uiManager.showFieldError(fieldElement, errors[0]);
            return false;
        } else {
            this.uiManager.clearFieldError(fieldElement);
            return true;
        }
    }

    /**
     * Collect all form data
     */
    collectFormData() {
        const data = { ...this.formData };

        // Collect input field values safely
        if (this.elements.codeInput && this.elements.codeInput.value !== undefined) {
            data.code = this.elements.codeInput.value.trim();
        }
        if (this.elements.dateOfBirthInput && this.elements.dateOfBirthInput.value !== undefined) {
            data.dateOfBirth = this.elements.dateOfBirthInput.value;
        }
        if (this.elements.weightInput && this.elements.weightInput.value !== undefined) {
            data.weight = this.elements.weightInput.value.trim();
        }
        if (this.elements.dateOfArtificialInseminationInput && this.elements.dateOfArtificialInseminationInput.value !== undefined) {
            data.dateOfArtificialInsemination = this.elements.dateOfArtificialInseminationInput.value;
        }
        if (this.elements.dateOfWeightInput && this.elements.dateOfWeightInput.value !== undefined) {
            data.dateOfWeight = this.elements.dateOfWeightInput.value;
        }
        if (this.elements.herdNumberInput && this.elements.herdNumberInput.value !== undefined) {
            data.herdNumber = this.elements.herdNumberInput.value.trim();
        }
        if (this.elements.expectedDateOfCalvingInput && this.elements.expectedDateOfCalvingInput.value !== undefined) {
            data.expectedDateOfCalving = this.elements.expectedDateOfCalvingInput.value;
        }
        if (this.elements.healthcareNotesInput && this.elements.healthcareNotesInput.value !== undefined) {
            data.healthcareNotes = this.elements.healthcareNotesInput.value.trim();
        }
        if (this.elements.vaccinationInput && this.elements.vaccinationInput.value !== undefined) {
            data.takenVaccination = this.elements.vaccinationInput.value.trim();
        }

        // Collect radio button values
        // Dairy type radio buttons (milch/drying)
        const checkedDairyType = document.querySelector('input[name="access"]:checked[id="milch"], input[name="access"]:checked[id="drying"]');
        if (checkedDairyType) {
            data.dairyType = checkedDairyType.id === 'milch' ? 'dairy' : 'drying';
        }

        // Insemination radio buttons (yes/no)
        const checkedInsemination = document.querySelector('input[name="access"]:checked[id="yes"], input[name="access"]:checked[id="no"]');
        if (checkedInsemination) {
            data.madeArtificialInsemination = checkedInsemination.id === 'yes';
        }

        const checkedStatus = document.querySelector('.frame-12 input[name="access"]:checked');
        if (checkedStatus) {
            data.statusOfInsemination = checkedStatus.id;
        }

        // Ensure required fields have default values if missing
        if (!data.code) data.code = '';

        return data;
    }

    /**
     * Validate entire form
     */
    validateForm() {
        if (!this.currentDairyAnimal) {
            this.uiManager.showNotification('No dairy animal loaded for update', 'error');
            return false;
        }

        const data = this.collectFormData();
        const errors = this.validationManager.validateFormData(data);

        // Clear all previous errors
        this.uiManager.clearAllFieldErrors();

        if (errors.length > 0) {
            // Show first error as notification
            this.uiManager.showNotification(errors[0], 'error');

            // Show field-specific errors
            this.showFieldErrors(errors);

            return false;
        }

        return true;
    }

    /**
     * Show field-specific errors
     */
    showFieldErrors(errors) {
        const fieldMap = {
            code: this.elements.codeInput,
            dateOfBirth: this.elements.dateOfBirthInput,
            weight: this.elements.weightInput,
            dateOfArtificialInsemination: this.elements.dateOfArtificialInseminationInput,
            dateOfWeight: this.elements.dateOfWeightInput,
            herdNumber: this.elements.herdNumberInput,
            expectedDateOfCalving: this.elements.expectedDateOfCalvingInput,
            healthcareNotes: this.elements.healthcareNotesInput,
            takenVaccination: this.elements.vaccinationInput
        };

        // Map errors to fields
        errors.forEach(error => {
            Object.entries(fieldMap).forEach(([fieldName, element]) => {
                if (error.toLowerCase().includes(fieldName.toLowerCase()) ||
                    error.toLowerCase().includes(this.validationManager.getFieldDisplayName(fieldName).toLowerCase())) {
                    if (element) {
                        this.uiManager.showFieldError(element, error);
                    }
                }
            });
        });
    }

    /**
     * Handle update button click
     */
    async handleUpdate() {
        console.log('💾 Update button clicked');

        if (!this.validateForm()) {
            console.log('❌ Form validation failed');
            return;
        }

        const formData = this.collectFormData();
        console.log('📝 Form data collected:', formData);

        // Show loading state
        this.uiManager.showLoading(this.elements.updateButton);

        try {
            // Update the dairy animal
            const updatedAnimal = this.dataManager.updateDairyAnimal(this.currentDairyAnimal.code, formData);

            console.log('✅ Dairy animal updated successfully:', updatedAnimal);

            // Show success message
            this.uiManager.showNotification(
                `Dairy animal "${updatedAnimal.dairyAnimal.code}" updated successfully!`,
                'success',
                4000
            );

            // Update current animal reference
            this.currentDairyAnimal = { ...this.currentDairyAnimal, ...updatedAnimal.dairyAnimal };

            // Highlight updated fields
            this.highlightUpdatedFields();

            // Trigger storage event for other pages to update
            window.dispatchEvent(new StorageEvent('storage', {
                key: 'dairyAnimals',
                newValue: JSON.stringify(this.dataManager.getData('dairyAnimals'))
            }));

        } catch (error) {
            console.error('❌ Error updating dairy animal:', error);
            this.uiManager.showNotification(
                `Failed to update dairy animal: ${error.message}`,
                'error'
            );
        } finally {
            
            // Hide loading state
            this.uiManager.hideLoading(this.elements.updateButton);
        }
         // Navigate back to dairy page
         window.parent.location.reload();
    }

    /**
     * Highlight updated fields with success styling
     */
    highlightUpdatedFields() {
        const fields = [
            this.elements.codeInput,
            this.elements.dateOfBirthInput,
            this.elements.weightInput,
            this.elements.dateOfArtificialInseminationInput,
            this.elements.dateOfWeightInput,
            this.elements.herdNumberInput,
            this.elements.expectedDateOfCalvingInput,
            this.elements.healthcareNotesInput,
            this.elements.vaccinationInput
        ];

        fields.forEach(field => {
            if (field) {
                this.uiManager.highlightFieldSuccess(field);
            }
        });
    }

    /**
     * Clear only form data (keep current animal reference and code field)
     */
    clearFormData() {
        // Clear form fields but keep code field and current animal reference for comparison
        const fieldsToKeep = ['codeInput', 'updateButton'];

        Object.entries(this.elements).forEach(([key, element]) => {
            if (fieldsToKeep.includes(key)) {
                return; // Skip these elements
            }

            if (element && element.value !== undefined) {
                element.value = '';
            } else if (element && element.length) {
                // Handle NodeList
                element.forEach(el => {
                    if (el.value !== undefined) el.value = '';
                    if (el.checked !== undefined) el.checked = false;
                });
            }
        });

        // Clear form data but keep code for search
        const currentCode = this.formData.code;
        this.formData = { code: currentCode };

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        console.log('🔄 Form data cleared (keeping code field)');
    }

    /**
     * Clear form to initial state
     */
    clearForm() {
        // Clear form data
        this.formData = {};
        this.currentDairyAnimal = null;

        // Clear all input fields
        Object.values(this.elements).forEach(element => {
            if (element && element.value !== undefined) {
                element.value = '';
            } else if (element && element.length) {
                // Handle NodeList
                element.forEach(el => {
                    if (el.value !== undefined) el.value = '';
                    if (el.checked !== undefined) el.checked = false;
                });
            }
        });

        // Clear all errors
        this.uiManager.clearAllFieldErrors();

        console.log('🔄 Form cleared completely');
    }

    /**
     * Setup validation styles
     */
    setupValidation() {
        // Add form validation styles
        const style = document.createElement('style');
        style.textContent = `
            .field-error {
                animation: shake 0.3s ease-in-out;
            }
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Get current dairy statistics
     */
    getDairyStatistics() {
        return this.dataManager.getDairyStatistics();
    }

    /**
     * Search dairy animals
     */
    searchDairyAnimals(searchTerm, searchField = null) {
        return this.dataManager.searchDairyAnimals(searchTerm, searchField);
    }

    /**
     * Get all dairy animals
     */
    getAllDairyAnimals() {
        return this.dataManager.getData('dairyAnimals');
    }
}

// ==================== INITIALIZATION ====================

/**
 * Initialize the Update Milk Data system when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    console.log('🌟 DOM loaded, initializing Update Milk Data system...');

    try {
        // Create and initialize the controller
        const updateMilkData = new UpdateMilkData();
        updateMilkData.init();

        // Make it globally accessible for debugging and external access
        window.updateMilkData = updateMilkData;

        console.log('🎉 Update Milk Data system ready!');
        console.log('Available global methods:');
        console.log('- window.updateMilkData.searchDairyAnimals(term)');
        console.log('- window.updateMilkData.getDairyStatistics()');
        console.log('- window.updateMilkData.getAllDairyAnimals()');

    } catch (error) {
        console.error('💥 Failed to initialize Update Milk Data system:', error);

        // Show user-friendly error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #f8d7da;
            color: #721c24;
            padding: 15px 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            z-index: 10000;
            font-family: Arial, sans-serif;
        `;
        errorDiv.textContent = 'Failed to initialize the dairy update form. Please refresh the page.';
        document.body.appendChild(errorDiv);
    }
});

// ==================== UTILITY FUNCTIONS ====================

/**
 * Search for dairy animal by code (utility function)
 * @param {string} code - Animal code to search for
 * @returns {Object|null} - Found dairy animal or null
 */
window.searchDairyAnimalByCode = function(code) {
    if (!window.updateMilkData) {
        console.error('Update Milk Data system not initialized');
        return null;
    }

    return window.updateMilkData.dataManager.getCompleteDairyAnimalData(code);
};

/**
 * Load dairy animal into form by code (utility function)
 * @param {string} code - Animal code to load
 * @returns {boolean} - Success status
 */
window.loadDairyAnimalByCode = function(code) {
    if (!window.updateMilkData) {
        console.error('Update Milk Data system not initialized');
        return false;
    }

    if (!window.updateMilkData.dataManager.isDairyAnimal(code)) {
        console.error(`Animal "${code}" is not a dairy animal`);
        return false;
    }

    const dairyAnimal = window.updateMilkData.dataManager.getCompleteDairyAnimalData(code);
    if (dairyAnimal) {
        window.updateMilkData.loadDairyAnimalIntoForm(dairyAnimal);
        window.updateMilkData.setFormMode('edit');
        return true;
    }
    return false;
};

/**
 * Get all dairy animals (utility function)
 * @returns {Array} - Array of all dairy animals
 */
window.getAllDairyAnimals = function() {
    if (!window.updateMilkData) {
        console.error('Update Milk Data system not initialized');
        return [];
    }

    return window.updateMilkData.dataManager.getData('dairyAnimals');
};

/**
 * Update dairy animal programmatically (utility function)
 * @param {string} animalCode - Animal code
 * @param {Object} updateData - Data to update
 * @returns {Object|null} - Updated animal or null
 */
window.updateDairyAnimalByCode = function(animalCode, updateData) {
    if (!window.updateMilkData) {
        console.error('Update Milk Data system not initialized');
        return null;
    }

    try {
        return window.updateMilkData.dataManager.updateDairyAnimal(animalCode, updateData);
    } catch (error) {
        console.error('Error updating dairy animal:', error);
        return null;
    }
};

/**
 * Add milk production record (utility function)
 * @param {string} animalCode - Animal code
 * @param {Object} milkData - Milk production data
 * @returns {Object|null} - Created milk record or null
 */
window.addMilkProductionRecord = function(animalCode, milkData) {
    if (!window.updateMilkData) {
        console.error('Update Milk Data system not initialized');
        return null;
    }

    try {
        return window.updateMilkData.dataManager.addMilkProductionRecord(animalCode, milkData);
    } catch (error) {
        console.error('Error adding milk production record:', error);
        return null;
    }
};

/**
 * Debug function to check system status
 */
window.debugUpdateMilkData = function() {
    console.log('=== Update Milk Data Debug Info ===');

    if (!window.updateMilkData) {
        console.error('❌ Update Milk Data system not initialized');
        return;
    }

    console.log('✅ System initialized');
    console.log('📊 Dairy Statistics:', window.updateMilkData.getDairyStatistics());
    console.log('🔍 Current dairy animal:', window.updateMilkData.currentDairyAnimal);
    console.log('📝 Form data:', window.updateMilkData.formData);

    // Check form elements
    const elements = window.updateMilkData.elements;
    console.log('📋 Form elements:');
    Object.entries(elements).forEach(([key, element]) => {
        if (element) {
            console.log(`  ✅ ${key}: Found`);
        } else {
            console.log(`  ❌ ${key}: Missing`);
        }
    });

    console.log('=== End Debug Info ===');
};

/**
 * Quick test function to load a test dairy animal
 */
window.testLoadDairyAnimal = function() {
    if (!window.updateMilkData) {
        console.error('Update Milk Data system not initialized');
        return;
    }

    // Get first dairy animal from storage
    const dairyAnimals = window.updateMilkData.dataManager.getData('dairyAnimals');
    if (dairyAnimals.length > 0) {
        const firstDairyAnimal = dairyAnimals[0];
        console.log('Loading test dairy animal:', firstDairyAnimal.code);

        // Set code input and trigger search
        if (window.updateMilkData.elements.codeInput) {
            window.updateMilkData.elements.codeInput.value = firstDairyAnimal.code;
            window.updateMilkData.handleSearch();
        }

        return firstDairyAnimal;
    } else {
        console.log('No dairy animals found in storage');
        return null;
    }
};

/**
 * Create test dairy animal for testing update functionality
 */
window.createTestDairyAnimal = function() {
    if (!window.updateMilkData) {
        console.error('Update Milk Data system not initialized');
        return;
    }

    const testAnimal = {
        code: 'DAIRY_TEST_' + Date.now(),
        type: 'dairy',
        gender: 'female',
        herdNumber: 'H999',
        weight: '450',
        dateOfWeight: new Date().toISOString().split('T')[0],
        dateOfBirth: '2022-03-10',
        healthcareNotes: 'Test dairy cow for update functionality',
        takenVaccination: 'Annual vaccination',
        dairyType: 'dairy',
        madeArtificialInsemination: false,
        averageDailyMilk: 25.5,
        totalMilkProduced: 1500,
        isPregnant: false
    };

    try {
        // Use the add new animal system if available, otherwise add directly
        if (window.addNewAnimal && window.addNewAnimal.dataManager) {
            const result = window.addNewAnimal.dataManager.addAnimal(testAnimal);
            console.log('✅ Test dairy animal created:', result.code);
            return result;
        } else {
            // Fallback: add directly to storage
            const animals = JSON.parse(localStorage.getItem('animals') || '[]');
            const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');

            testAnimal.id = Date.now().toString(36) + Math.random().toString(36).substring(2);
            testAnimal.createdAt = new Date().toISOString();
            testAnimal.updatedAt = new Date().toISOString();

            animals.push(testAnimal);
            dairyAnimals.push({ ...testAnimal, animalId: testAnimal.id });

            localStorage.setItem('animals', JSON.stringify(animals));
            localStorage.setItem('dairyAnimals', JSON.stringify(dairyAnimals));

            console.log('✅ Test dairy animal created (fallback):', testAnimal.code);
            return testAnimal;
        }
    } catch (error) {
        console.error('❌ Error creating test dairy animal:', error);
        return null;
    }
};

// Close button functionality removed - handled by external close button in animal.js

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        UpdateMilkData,
        DairyDataManager,
        DairyValidationManager,
        DairyUIManager,
        BaseDairyDataManager
    };
}