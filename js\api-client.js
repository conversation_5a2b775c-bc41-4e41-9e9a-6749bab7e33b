/**
 * API Client for SRA API
 */
class ApiClient {
    constructor() {
        this.baseUrl = 'https://sra.runasp.net';
    }

    /**
     * Make a GET request to the API
     * @param {string} endpoint - API endpoint
     * @returns {Promise<any>} - Promise with response data
     */
    async get(endpoint) {
        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            
            return this.handleResponse(response);
        } catch (error) {
            this.handleError(error, 'GET', endpoint);
            throw error;
        }
    }

    /**
     * Make a POST request to the API
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Data to send
     * @returns {Promise<any>} - Promise with response data
     */
    async post(endpoint, data) {
        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            return this.handleResponse(response);
        } catch (error) {
            this.handleError(error, 'POST', endpoint);
            throw error;
        }
    }

    /**
     * Make a PUT request to the API
     * @param {string} endpoint - API endpoint
     * @param {Object} data - Data to send
     * @returns {Promise<any>} - Promise with response data
     */
    async put(endpoint, data) {
        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(data)
            });
            
            return this.handleResponse(response);
        } catch (error) {
            this.handleError(error, 'PUT', endpoint);
            throw error;
        }
    }

    /**
     * Make a DELETE request to the API
     * @param {string} endpoint - API endpoint
     * @returns {Promise<any>} - Promise with response data
     */
    async delete(endpoint) {
        try {
            const response = await fetch(`${this.baseUrl}${endpoint}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });
            
            return this.handleResponse(response);
        } catch (error) {
            this.handleError(error, 'DELETE', endpoint);
            throw error;
        }
    }

    /**
     * Handle API response
     * @param {Response} response - Fetch API response
     * @returns {Promise<any>} - Parsed response data
     */
    async handleResponse(response) {
        if (!response.ok) {
            const errorText = await response.text();
            let errorData;
            try {
                errorData = JSON.parse(errorText);
            } catch (e) {
                errorData = { message: errorText };
            }
            
            const error = new Error(errorData.message || 'API request failed');
            error.status = response.status;
            error.data = errorData;
            throw error;
        }
        
        // Check if response is empty
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return await response.json();
        } else {
            return await response.text();
        }
    }

    /**
     * Handle and log errors
     * @param {Error} error - Error object
     * @param {string} method - HTTP method
     * @param {string} endpoint - API endpoint
     */
    handleError(error, method, endpoint) {
        console.error(`API ${method} request to ${endpoint} failed:`, error);
        
        // Show user-friendly notification if available
        if (typeof showNotification === 'function') {
            showNotification('Failed to connect to the server. Please try again later.', 'error');
        }
    }
}

// Create global instance
window.apiClient = new ApiClient();
