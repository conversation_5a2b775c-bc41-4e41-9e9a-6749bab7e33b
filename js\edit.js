document.addEventListener('DOMContentLoaded', function() {
  // Get elements
  const nameInput = document.querySelector('.layla-hassan');
  const emailInput = document.querySelector('.azzamaaexampleocm');
  const passwordInput = document.querySelector('.password-input');
  const changePasswordBtn = document.getElementById('frameContainer');
  const backButton = document.getElementById('backLeftContainer');
  const saveButton = document.getElementById('saveContainer');
  const photoUploadArea = document.querySelector('.photo2');
  
  // Load user data
  loadUserData();
  
  // Set up event listeners
  setupEventListeners();
  
  // Track if form has been modified
  let formModified = false;
  
  /**
   * Load user data from localStorage or sessionStorage
   */
  function loadUserData() {
    // Try to get user data from sessionStorage first, then localStorage
    const userData = JSON.parse(sessionStorage.getItem('currentUser') || localStorage.getItem('userData') || '{}');
    
    if (userData) {
      // Fill form fields with user data
      nameInput.value = userData.name || '';
      emailInput.value = userData.email || '';
      
      // Password is masked, so we don't fill it
      // Instead, we store the original password in a data attribute for comparison
      if (userData.password) {
        passwordInput.setAttribute('data-original-password', userData.password);
      }
      
      console.log('User data loaded successfully');
    } else {
      console.log('No user data found');
    }
  }
  
  /**
   * Set up event listeners for the edit page
   */
  function setupEventListeners() {
    // Track form modifications
    [nameInput, emailInput, passwordInput].forEach(input => {
      input.addEventListener('input', function() {
        formModified = true;
        
        // Visual feedback that the form has been modified
        saveButton.querySelector('.save1').textContent = 'Save*';
        saveButton.style.backgroundColor = '#4CAF50';
      });
    });
    
    // Change password button
    if (changePasswordBtn) {
      changePasswordBtn.addEventListener('click', function() {
        // If form is modified, ask user if they want to save changes first
        if (formModified) {
          const confirmSave = confirm('You have unsaved changes. Do you want to save them before changing your password?');
          if (confirmSave) {
            saveUserData();
          }
        }
        
        // Navigate to change password page
        window.location.href = 'change-password.html';
      });
    }
    
    // Back button
    if (backButton) {
      backButton.addEventListener('click', function() {
        // If form is modified, ask for confirmation before leaving
        if (formModified) {
          const confirmLeave = confirm('You have unsaved changes. Are you sure you want to leave without saving?');
          if (!confirmLeave) {
            return; // Stay on the page
          }
        }
        
        // Navigate back to settings page
        window.location.href = 'setting.html';
      });
    }
    
    // Save button
    if (saveButton) {
      saveButton.addEventListener('click', saveUserData);
    }
    
    // Photo upload
    if (photoUploadArea) {
      photoUploadArea.addEventListener('click', function() {
        // In a real implementation, this would open a file picker
        // For now, we'll just show an alert
        alert('This would open a file picker to change your profile photo');
        
        // Mark form as modified
        formModified = true;
      });
    }
  }
  
  /**
   * Save user data to localStorage and sessionStorage
   */
  function saveUserData() {
    // Validate form data
    if (!validateForm()) {
      return;
    }
    
    // Get form values
    const name = nameInput.value.trim();
    const email = emailInput.value.trim();
    const password = passwordInput.value;
    
    // Create user data object
    const userData = {
      name: name,
      email: email,
      // Only update password if it was changed
      password: password === '**********' ? 
        passwordInput.getAttribute('data-original-password') : 
        password,
      lastUpdated: new Date().toISOString()
    };
    
    try {
      // Save to localStorage and sessionStorage
      localStorage.setItem('userData', JSON.stringify(userData));
      sessionStorage.setItem('currentUser', JSON.stringify(userData));
      
      // Visual feedback for successful save
      const originalButtonText = saveButton.querySelector('.save1').textContent;
      const originalButtonColor = saveButton.style.backgroundColor;
      
      saveButton.style.backgroundColor = '#4CAF50';
      saveButton.querySelector('.save1').textContent = 'Saved!';
      
      // Reset button after a delay
      setTimeout(() => {
        saveButton.style.backgroundColor = originalButtonColor;
        saveButton.querySelector('.save1').textContent = 'Save';
        formModified = false;
      }, 2000);
      
      // Show success message
      showMessage('Profile updated successfully!', 'success');
      
      console.log('User data saved successfully');
    } catch (error) {
      console.error('Error saving user data:', error);
      showMessage('Error saving profile. Please try again.', 'error');
    }
  }
  
  /**
   * Validate form data
   * @returns {boolean} True if form is valid, false otherwise
   */
  function validateForm() {
    // Validate name
    if (!nameInput.value.trim()) {
      showMessage('Please enter your name', 'error');
      nameInput.focus();
      return false;
    }
    
    // Validate email
    const email = emailInput.value.trim();
    if (!email) {
      showMessage('Please enter your email', 'error');
      emailInput.focus();
      return false;
    }
    
    // Simple email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      showMessage('Please enter a valid email address', 'error');
      emailInput.focus();
      return false;
    }
    
    return true;
  }
  
  /**
   * Show a message to the user
   * @param {string} message - The message to show
   * @param {string} type - The type of message (success, error, info)
   */
  function showMessage(message, type = 'info') {
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.textContent = message;
    messageElement.style.position = 'fixed';
    messageElement.style.top = '20px';
    messageElement.style.left = '50%';
    messageElement.style.transform = 'translateX(-50%)';
    messageElement.style.padding = '10px 20px';
    messageElement.style.borderRadius = '5px';
    messageElement.style.zIndex = '1000';
    
    // Set styles based on message type
    switch (type) {
      case 'success':
        messageElement.style.backgroundColor = '#4CAF50';
        messageElement.style.color = 'white';
        break;
      case 'error':
        messageElement.style.backgroundColor = '#f44336';
        messageElement.style.color = 'white';
        break;
      default:
        messageElement.style.backgroundColor = '#2196F3';
        messageElement.style.color = 'white';
    }
    
    // Add to document
    document.body.appendChild(messageElement);
    
    // Remove after delay
    setTimeout(() => {
      document.body.removeChild(messageElement);
    }, 3000);
  }
});