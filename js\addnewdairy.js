/**
 * Comprehensive Add New Dairy Animal Management System
 * High-quality OOP implementation following established patterns
 *
 * Features:
 * - Real-time animal code lookup and data loading
 * - Comprehensive validation with business rules
 * - Dual table storage (animals + dairy)
 * - Professional UI/UX with animations and feedback
 * - Conditional field logic for AI-related fields
 * - Auto-calculation of breeding dates and production metrics
 *
 * <AUTHOR> Management System
 * @version 3.0.0
 */

// ==================== DATA MANAGEMENT CLASS ====================

/**
 * Handles all data operations for dairy animals
 */
class DairyDataManager {
    constructor() {
        this.animals = [];
        this.dairyAnimals = [];
        this.loadData();
    }

    /**
     * Load data from localStorage using correct storage keys
     */
    loadData() {
        try {
            this.animals = JSON.parse(localStorage.getItem('animals') || '[]');
            // FIX: Use 'dairyAnimals' key to match main dairy page
            this.dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
            console.log(`📊 Data loaded: ${this.animals.length} animals, ${this.dairyAnimals.length} dairy records`);
        } catch (error) {
            console.error('❌ Error loading data:', error);
            this.animals = [];
            this.dairyAnimals = [];
        }
    }

    /**
     * Check if animal code exists (for new animal validation only)
     * @param {string} code - Animal code to check
     */
    findAnimalByCode(code) {
        // This system is for adding NEW dairy animals only
        // We only check if code exists to prevent duplicates
        return null; // Always return null since we're adding new animals
    }

    /**
     * Check if animal code exists
     * @param {string} code - Code to check
     */
    isCodeExists(code) {
        if (!code) return false;
        return this.animals.some(animal =>
            animal.code && animal.code.toLowerCase() === code.toLowerCase()
        );
    }

    /**
     * Generate unique dairy animal code
     */
    generateUniqueCode() {
        let attempts = 0;
        let newCode;

        do {
            attempts++;
            const timestamp = Date.now().toString().slice(-6);
            const random = Math.floor(Math.random() * 100).toString().padStart(2, '0');
            newCode = `D${timestamp}${random}`;
        } while (this.isCodeExists(newCode) && attempts < 100);

        if (attempts >= 100) {
            throw new Error('Unable to generate unique dairy code');
        }

        return newCode;
    }

    /**
     * Save animal to both tables
     * @param {Object} animalData - Animal data to save
     */
    async saveAnimal(animalData) {
        try {
            console.log('🔄 Starting to save animal to both tables...');
            console.log('📊 Animal data to save:', animalData);

            // Reload data to ensure we have the latest
            this.loadData();
            console.log('📊 Data loaded - Animals:', this.animals.length, 'Dairy:', this.dairyAnimals.length);

            // Ensure arrays are properly initialized
            if (!Array.isArray(this.animals)) {
                console.log('🔧 Initializing animals array');
                this.animals = [];
            }
            if (!Array.isArray(this.dairyAnimals)) {
                console.log('🔧 Initializing dairy animals array');
                this.dairyAnimals = [];
            }

            // Save to main animals table
            console.log('💾 Saving to animals table...');
            this.animals.push(animalData);
            localStorage.setItem('animals', JSON.stringify(this.animals));
            console.log('✅ Animal saved to animals table. Total animals:', this.animals.length);

            // Create dairy-specific record
            console.log('🥛 Creating dairy-specific record...');
            const dairyData = this.createDairyRecord(animalData);
            console.log('🥛 Dairy data created:', dairyData);

            // Save to dairy table with extensive logging
            console.log('💾 Saving to dairy table...');
            console.log('📊 Current dairy animals before save:', this.dairyAnimals.length);
            this.dairyAnimals.push(dairyData);
            console.log('📊 Dairy animals after push:', this.dairyAnimals.length);

            // FIX: Use 'dairyAnimals' key to match main dairy page
            localStorage.setItem('dairyAnimals', JSON.stringify(this.dairyAnimals));
            console.log('✅ Animal saved to dairy table. Total dairy animals:', this.dairyAnimals.length);

            // Immediate verification
            const immediateCheck = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
            console.log('🔍 Immediate verification - dairy animals in localStorage:', immediateCheck.length);
            if (immediateCheck.length !== this.dairyAnimals.length) {
                console.error('❌ MISMATCH: Memory vs localStorage dairy count!');
                console.error('Memory:', this.dairyAnimals.length, 'localStorage:', immediateCheck.length);
            }

            // Verify the data was saved
            const savedAnimals = JSON.parse(localStorage.getItem('animals') || '[]');
            const savedDairy = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
            console.log('🔍 Verification - Animals in localStorage:', savedAnimals.length);
            console.log('🔍 Verification - Dairy animals in localStorage:', savedDairy.length);

            // Trigger storage events
            this.triggerStorageEvents();

            console.log('✅ Animal saved to both tables successfully');
            console.log('📋 Final animals table:', savedAnimals);
            console.log('🥛 Final dairy table:', savedDairy);

            return { success: true, animalData, dairyData };

        } catch (error) {
            console.error('❌ Error saving animal:', error);
            console.error('❌ Error details:', error.stack);
            throw error;
        }
    }

    /**
     * Create comprehensive dairy-specific record ensuring ALL TABLE COLUMNS are filled
     * @param {Object} animalData - Source animal data
     */
    createDairyRecord(animalData) {
        const dairyRecord = {
            // Basic information (matching animal table)
            id: animalData.id,
            code: animalData.code,
            name: animalData.name,
            breed: animalData.breed,
            gender: animalData.gender, // Always Female for dairy
            type: animalData.type, // Always dairy
            birthDate: animalData.birthDate,
            herdNumber: animalData.herdNumber,
            status: animalData.status,
            registrationDate: animalData.registrationDate,

            // Physical information
            weight: animalData.weight,
            weightDate: animalData.weightDate,

            // Breeding information (dairy-specific)
            artificialInsemination: animalData.artificialInsemination,
            artificialInseminationDate: animalData.artificialInseminationDate,
            inseminationStatus: animalData.inseminationStatus,
            expectedCalvingDate: animalData.expectedCalvingDate,
            breedingCycleStage: animalData.breedingCycleStage,

            // Health information
            healthCareNotes: animalData.healthCareNotes,
            vaccination: animalData.vaccination,
            lastHealthCheckDate: animalData.lastHealthCheckDate,
            nextVaccinationDue: animalData.nextVaccinationDue,

            // === DAIRY TABLE COLUMN MAPPINGS ===
            // These fields map directly to dairy table columns

            // Column: MP (Milk Production) → lastMilkProduction
            lastMilkProduction: animalData.averageDailyMilk || 0,

            // Column: FP (Fat Percentage) → fatPercentage
            fatPercentage: animalData.fatPercentage || 'N/A',

            // Column: SAI (Status of Artificial Insemination) → artificialInseminationStatus
            artificialInseminationStatus: animalData.inseminationStatus || animalData.artificialInsemination || 'N/A',

            // Column: EDC (Expected Date of Calving) → expectedCalfingDate (note: different spelling)
            expectedCalfingDate: animalData.expectedCalvingDate,

            // Column: Healthcare → healthcareNotes (healthcare only, no vaccination)
            healthcareNotes: animalData.healthCareNotes || '',

            // Dairy-specific production data
            lactationNumber: animalData.lactationNumber || 0,
            totalMilkProduced: animalData.totalMilkProduced || 0,
            averageDailyMilk: animalData.averageDailyMilk || 0,
            lastMilkingDate: animalData.lastMilkingDate,
            milkingSchedule: animalData.milkingSchedule || 'twice-daily',

            // Dairy-specific additional fields
            currentLactationMilk: 0, // Milk produced in current lactation
            peakMilkProduction: 0, // Peak daily milk production
            daysInMilk: 0, // Days since last calving
            dryPeriodStart: null, // When dry period started
            expectedNextCalving: animalData.expectedCalvingDate,
            milkQuality: 'Good', // Default milk quality
            feedIntake: '', // Daily feed intake
            bodyConditionScore: '', // Body condition score

            // Metadata (ensuring all columns have data)
            createdAt: animalData.createdAt,
            updatedAt: animalData.updatedAt,
            source: animalData.source,
            lastUpdatedBy: 'addnewdairy_system'
        };

        console.log('🥛 Dairy record created with table column mappings:');
        console.log('  Code:', dairyRecord.code);
        console.log('  Type:', dairyRecord.type);
        console.log('  Herd Number:', dairyRecord.herdNumber);
        console.log('  Milk Production:', dairyRecord.lastMilkProduction);
        console.log('  Fat Percentage:', dairyRecord.fatPercentage);
        console.log('  Weight:', dairyRecord.weight);
        console.log('  Weight Date:', dairyRecord.weightDate);
        console.log('  AI Status:', dairyRecord.artificialInseminationStatus);
        console.log('  AI Date:', dairyRecord.artificialInseminationDate);
        console.log('  Expected Calving:', dairyRecord.expectedCalfingDate);
        console.log('  Healthcare Notes:', dairyRecord.healthcareNotes);

        return dairyRecord;
    }

    /**
     * Trigger storage events for other pages
     */
    triggerStorageEvents() {
        window.dispatchEvent(new StorageEvent('storage', {
            key: 'animals',
            newValue: JSON.stringify(this.animals)
        }));

        // FIX: Use 'dairyAnimals' key to match main dairy page
        window.dispatchEvent(new StorageEvent('storage', {
            key: 'dairyAnimals',
            newValue: JSON.stringify(this.dairyAnimals)
        }));
    }

    /**
     * Calculate breeding cycle stage
     * @param {string} artificialInsemination - AI status
     * @param {string} inseminationStatus - Insemination status
     */
    calculateBreedingCycleStage(artificialInsemination, inseminationStatus) {
        if (artificialInsemination === 'Yes') {
            if (inseminationStatus === 'Pregnant') {
                return 'Pregnant';
            } else if (inseminationStatus === 'Re-inseminate') {
                return 'Awaiting Re-insemination';
            } else {
                return 'Post-AI Monitoring';
            }
        } else if (artificialInsemination === 'No') {
            return 'Pre-breeding';
        } else {
            return 'Not Specified';
        }
    }

    /**
     * Calculate next vaccination date
     */
    calculateNextVaccinationDate() {
        const nextYear = new Date();
        nextYear.setFullYear(nextYear.getFullYear() + 1);
        return nextYear.toISOString().split('T')[0];
    }

    /**
     * Calculate expected calving date from AI date
     * @param {string} aiDate - Artificial insemination date
     */
    calculateExpectedCalvingDate(aiDate) {
        if (!aiDate) return null;

        try {
            const aiDateObj = new Date(aiDate);
            const expectedCalving = new Date(aiDateObj);
            expectedCalving.setDate(expectedCalving.getDate() + 280); // Average gestation period
            return expectedCalving.toISOString().split('T')[0];
        } catch (error) {
            console.error('❌ Error calculating expected calving date:', error);
            return null;
        }
    }

    /**
     * Generate intelligent animal name
     * @param {string} code - Animal code
     * @param {string} herdNumber - Herd number
     * @param {string} birthDate - Birth date
     */
    generateIntelligentName(code, herdNumber, birthDate) {
        let name = 'Dairy';

        if (code) {
            name += ` ${code}`;
        } else if (herdNumber) {
            name += ` ${herdNumber}`;
        }

        if (birthDate) {
            const year = new Date(birthDate).getFullYear();
            if (!isNaN(year)) {
                name += ` (${year})`;
            }
        }

        return name;
    }
}

// ==================== VALIDATION CLASS ====================

/**
 * Handles comprehensive validation for dairy animal forms
 */
class DairyValidationManager {
    constructor() {
        this.validationRules = {
            required: [], // No required fields - all fields are optional
            optional: ['code', 'birthDate', 'herdNumber', 'weight', 'weightDate', 'healthCareNotes', 'vaccination'],
            conditional: ['artificialInseminationDate', 'expectedCalvingDate']
        };
    }

    /**
     * Validate individual field with comprehensive rules
     * @param {string} fieldName - Name of the field to validate
     * @param {string} value - Value to validate
     * @param {Object} allData - All form data for cross-field validation
     */
    validateField(fieldName, value, allData = {}) {
        const result = { isValid: true, message: '' };

        switch (fieldName) {
            case 'code':
                // Allow any length code for dairy animals
                if (value && !/^[A-Za-z0-9]+$/.test(value)) {
                    result.isValid = false;
                    result.message = 'Code should contain only letters and numbers';
                }
                break;

            case 'birthDate':
                // Birth date is now optional - only validate if provided
                if (value) {
                    const birthDate = new Date(value);
                    const today = new Date();
                    const maxAge = new Date();
                    maxAge.setFullYear(today.getFullYear() - 20); // Max 20 years old

                    if (birthDate > today) {
                        result.isValid = false;
                        result.message = 'Birth date cannot be in the future';
                    } else if (birthDate < maxAge) {
                        result.isValid = false;
                        result.message = 'Birth date seems too old (max 20 years)';
                    }
                }
                break;

            case 'weight':
                if (value) {
                    const weight = parseFloat(value);
                    if (isNaN(weight) || weight <= 0) {
                        result.isValid = false;
                        result.message = 'Weight must be a positive number';
                    } else if (weight < 50 || weight > 1000) {
                        result.isValid = false;
                        result.message = 'Weight should be between 50-1000 kg for dairy animals';
                    }
                }
                break;

            case 'herdNumber':
                if (value && !/^[A-Za-z0-9]+$/.test(value)) {
                    result.isValid = false;
                    result.message = 'Herd number should contain only letters and numbers';
                }
                break;

            case 'weightDate':
                if (value) {
                    const weightDate = new Date(value);
                    const today = new Date();
                    if (weightDate > today) {
                        result.isValid = false;
                        result.message = 'Weight date cannot be in the future';
                    }
                }
                break;

            case 'artificialInseminationDate':
                if (value) {
                    const aiDate = new Date(value);
                    const today = new Date();
                    const birthDate = allData.birthDate ? new Date(allData.birthDate) : null;

                    if (aiDate > today) {
                        result.isValid = false;
                        result.message = 'AI date cannot be in the future';
                    } else if (birthDate && aiDate < birthDate) {
                        result.isValid = false;
                        result.message = 'AI date cannot be before birth date';
                    }
                }
                break;

            case 'expectedCalvingDate':
                if (value) {
                    const calvingDate = new Date(value);
                    const aiDate = allData.artificialInseminationDate ?
                                  new Date(allData.artificialInseminationDate) : null;

                    if (aiDate) {
                        const expectedDate = new Date(aiDate);
                        expectedDate.setDate(expectedDate.getDate() + 280); // ~9 months
                        const minDate = new Date(aiDate);
                        minDate.setDate(minDate.getDate() + 260); // Min gestation
                        const maxDate = new Date(aiDate);
                        maxDate.setDate(maxDate.getDate() + 300); // Max gestation

                        if (calvingDate < minDate || calvingDate > maxDate) {
                            result.isValid = false;
                            result.message = 'Expected calving date should be 260-300 days after AI';
                        }
                    }
                }
                break;
        }

        return result;
    }

    /**
     * Validate all form fields
     * @param {Object} formData - All form data
     * @param {Object} fieldStates - Field enabled/disabled states
     */
    validateAllFields(formData, fieldStates = {}) {
        const errors = {};
        let isValid = true;

        // Validate required fields
        for (const fieldName of this.validationRules.required) {
            const value = formData[fieldName] || '';
            const validation = this.validateField(fieldName, value, formData);

            if (!validation.isValid) {
                errors[fieldName] = validation.message;
                isValid = false;
            }
        }

        // Validate optional fields if they have values
        for (const fieldName of this.validationRules.optional) {
            const value = formData[fieldName] || '';
            if (value.trim()) {
                const validation = this.validateField(fieldName, value, formData);

                if (!validation.isValid) {
                    errors[fieldName] = validation.message;
                    isValid = false;
                }
            }
        }

        // Validate conditional fields only if they're enabled and have values
        for (const fieldName of this.validationRules.conditional) {
            const isFieldEnabled = fieldStates[fieldName] !== false;
            const value = formData[fieldName] || '';

            if (isFieldEnabled && value.trim()) {
                const validation = this.validateField(fieldName, value, formData);

                if (!validation.isValid) {
                    errors[fieldName] = validation.message;
                    isValid = false;
                }
            }
        }

        return { isValid, errors };
    }

    /**
     * Validate business rules
     * @param {Object} formData - Form data to validate
     */
    validateBusinessRules(formData) {
        const errors = [];

        // Dairy animals must be female
        if (formData.gender && formData.gender !== 'Female') {
            errors.push('Dairy animals must be female');
        }

        // Dairy animals must have dairy type
        if (formData.type && formData.type !== 'dairy') {
            errors.push('Animal type must be dairy for this form');
        }

        // AI date must be before expected calving date
        if (formData.artificialInseminationDate && formData.expectedCalvingDate) {
            const aiDate = new Date(formData.artificialInseminationDate);
            const calvingDate = new Date(formData.expectedCalvingDate);

            if (aiDate >= calvingDate) {
                errors.push('Expected calving date must be after AI date');
            }
        }

        return errors;
    }
}

// ==================== UI MANAGEMENT CLASS ====================

/**
 * Handles all UI interactions and visual feedback
 */
class DairyUIManager {
    constructor() {
        this.fields = {};
        this.isAnimating = false;
        this.setupFieldReferences();
    }

    /**
     * Setup form field references based on exact HTML structure
     */
    setupFieldReferences() {
        try {
            // Get all input elements in order they appear in HTML
            const dataFilledInputs = document.querySelectorAll('.data-filled');
            const textareaInputs = document.querySelectorAll('.textarea');

            console.log('🔍 Found elements:');
            console.log(`  .data-filled inputs: ${dataFilledInputs.length}`);
            console.log(`  .textarea inputs: ${textareaInputs.length}`);

            // Map fields according to exact HTML order
            this.fields = {
                // Line 26: Code - 1st .data-filled input
                code: dataFilledInputs[0],

                // Line 44: Date of Birth - 2nd .data-filled input (has type="date")
                birthDate: dataFilledInputs[1],

                // Line 49: Herd Number - 3rd .data-filled input
                herdNumber: dataFilledInputs[2],

                // Line 68: Weight - 4th .data-filled input
                weight: dataFilledInputs[3],

                // Line 76: Date of Artificial Insemination - 5th .data-filled input (has type="date")
                artificialInseminationDate: dataFilledInputs[4],

                // Line 81: Date of weight - 6th .data-filled input (has type="date")
                weightDate: dataFilledInputs[5],

                // Line 98: Expected Date of Calving - 7th .data-filled input (has type="date")
                expectedCalvingDate: dataFilledInputs[6],

                // Line 104: Health care notes - 1st .textarea input
                healthCareNotes: textareaInputs[0],

                // Line 109: Taken Vaccination - 2nd .textarea input
                vaccination: textareaInputs[1],

                // Radio Buttons
                dairyTypeRadio: document.querySelector('#milch'),
                dryingTypeRadio: document.querySelector('#drying'),
                yesAIRadio: document.querySelector('#yes'),
                noAIRadio: document.querySelector('#no'),
                pregnantRadio: document.querySelector('#pregnant'),
                reinseminateRadio: document.querySelector('#make-insemination-again'),

                // Submit button
                saveButton: document.querySelector('.frame-79')
            };

            // Debug: Log which fields were found and their details
            console.log('🔍 Field mapping results:');
            Object.keys(this.fields).forEach(fieldName => {
                const field = this.fields[fieldName];
                if (field) {
                    const type = field.type || 'text';
                    const className = field.className || '';
                    const id = field.id || '';
                    console.log(`  ✅ ${fieldName}: ${field.tagName} (type: ${type}, class: ${className}, id: ${id})`);
                } else {
                    console.log(`  ❌ ${fieldName}: Not found`);
                }
            });

            // Fix radio button naming conflicts
            this.fixRadioButtonGroups();

            console.log('✅ UI field references setup completed with exact HTML mapping');

        } catch (error) {
            console.error('❌ Error setting up field references:', error);
        }
    }

    /**
     * Fix radio button naming conflicts in HTML
     */
    fixRadioButtonGroups() {
        try {
            // Fix animal type radio group
            if (this.fields.dairyTypeRadio) {
                this.fields.dairyTypeRadio.name = 'animalType';
                this.fields.dairyTypeRadio.value = 'dairy';
                console.log('🔧 Fixed dairy type radio:', this.fields.dairyTypeRadio);
            }
            if (this.fields.dryingTypeRadio) {
                this.fields.dryingTypeRadio.name = 'animalType';
                this.fields.dryingTypeRadio.value = 'drying';
                console.log('🔧 Fixed drying type radio:', this.fields.dryingTypeRadio);
            }

            // Fix artificial insemination radio group
            if (this.fields.yesAIRadio) {
                this.fields.yesAIRadio.name = 'artificialInsemination';
                this.fields.yesAIRadio.value = 'Yes';
                console.log('🔧 Fixed yes AI radio:', this.fields.yesAIRadio);
            }
            if (this.fields.noAIRadio) {
                this.fields.noAIRadio.name = 'artificialInsemination';
                this.fields.noAIRadio.value = 'No';
                console.log('🔧 Fixed no AI radio:', this.fields.noAIRadio);
            }

            // Fix insemination status radio group
            if (this.fields.pregnantRadio) {
                this.fields.pregnantRadio.name = 'inseminationStatus';
                this.fields.pregnantRadio.value = 'Pregnant';
                console.log('🔧 Fixed pregnant radio:', this.fields.pregnantRadio);
            }
            if (this.fields.reinseminateRadio) {
                this.fields.reinseminateRadio.name = 'inseminationStatus';
                this.fields.reinseminateRadio.value = 'Re-inseminate';
                console.log('🔧 Fixed reinseminate radio:', this.fields.reinseminateRadio);
            }

            console.log('✅ Radio button groups fixed');

        } catch (error) {
            console.error('❌ Error fixing radio button groups:', error);
        }
    }

    /**
     * Show field error with professional styling
     * @param {string} fieldName - Name of the field
     * @param {string} message - Error message
     */
    showFieldError(fieldName, message) {
        const field = this.fields[fieldName];
        if (!field) return;

        // Remove existing error
        this.clearFieldError(fieldName);

        // Add error styling with animation
        field.style.transition = 'all 0.3s ease';
        field.style.borderColor = '#dc3545';
        field.style.backgroundColor = '#fff5f5';
        field.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';

        // Create error message element
        const errorElement = document.createElement('div');
        errorElement.className = 'field-error';
        errorElement.style.cssText = `
            position: relative;
            top:48%;
            left:-45%;

            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
            display: block;
            font-weight: bold;
            opacity: 0;
            // transform: translateY(-10px);
            // transition: all 0.3s ease;
            
        `;
        errorElement.textContent = message;

        // Insert error message after field
        field.parentNode.insertBefore(errorElement, field.nextSibling);

        // Animate error message in
        setTimeout(() => {
            errorElement.style.opacity = '1';
            errorElement.style.transform = 'translateY(0)';
        }, 10);
    }

    /**
     * Clear field error styling
     * @param {string} fieldName - Name of the field
     */
    clearFieldError(fieldName) {
        const field = this.fields[fieldName];
        if (!field) return;

        // Remove error styling with animation
        field.style.transition = 'all 0.3s ease';
        field.style.borderColor = '';
        field.style.backgroundColor = '';
        field.style.boxShadow = '';

        // Remove error message with animation
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.style.opacity = '0';
            errorElement.style.transform = 'translateY(-10px)';
            setTimeout(() => {
                if (errorElement.parentNode) {
                    errorElement.remove();
                }
            }, 300);
        }
    }

    /**
     * Show loading state on form
     * @param {boolean} isLoading - Whether to show loading state
     */
    showLoadingState(isLoading) {
        try {
            if (this.fields.saveButton) {
                if (isLoading) {
                    this.fields.saveButton.disabled = true;
                    this.fields.saveButton.style.opacity = '0.6';
                    this.fields.saveButton.style.cursor = 'not-allowed';
                    this.fields.saveButton.style.transform = 'scale(0.98)';

                    // Change button text if it has text content
                    const textElement = this.fields.saveButton.querySelector('.text-wrapper-7');
                    if (textElement) {
                        textElement.setAttribute('data-original-text', textElement.textContent);
                        textElement.textContent = 'Saving...';
                    }
                } else {
                    this.fields.saveButton.disabled = false;
                    this.fields.saveButton.style.opacity = '';
                    this.fields.saveButton.style.cursor = '';
                    this.fields.saveButton.style.transform = '';

                    // Restore button text
                    const textElement = this.fields.saveButton.querySelector('.text-wrapper-7');
                    if (textElement) {
                        const originalText = textElement.getAttribute('data-original-text');
                        if (originalText) {
                            textElement.textContent = originalText;
                            textElement.removeAttribute('data-original-text');
                        }
                    }
                }
            }

        } catch (error) {
            console.error('❌ Error showing loading state:', error);
        }
    }

    /**
     * Show small message at the header of the page
     * @param {string} message - Message to show
     * @param {string} type - Message type (success, error, warning, info)
     */
    showMessage(message, type = 'info') {
        try {
            // Remove existing messages
            const existingMessages = document.querySelectorAll('.header-message');
            existingMessages.forEach(msg => {
                msg.style.opacity = '0';
                setTimeout(() => msg.remove(), 300);
            });

            // Create small message element for header
            const messageElement = document.createElement('div');
            messageElement.className = 'header-message';
            messageElement.textContent = message;

            // Style based on type - smaller and more subtle
            const colors = {
                success: { bg: '#d4edda', border: '#c3e6cb', text: '#155724' },
                error: { bg: '#f8d7da', border: '#f5c6cb', text: '#721c24' },
                warning: { bg: '#fff3cd', border: '#ffeaa7', text: '#856404' },
                info: { bg: '#d1ecf1', border: '#bee5eb', text: '#0c5460' }
            };

            const color = colors[type] || colors.info;
            messageElement.style.cssText = `
                background-color: ${color.bg};
                border: 1px solid ${color.border};
                color: ${color.text};
                padding: 8px 12px;
                border-radius: 4px;
                margin: 8px 0;
                font-size: 12px;
                font-weight: 500;
                display: block;
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 9999;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                max-width: 300px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;

            // Insert at body level for fixed positioning
            document.body.appendChild(messageElement);

            // Animate message in from right
            setTimeout(() => {
                messageElement.style.opacity = '1';
                messageElement.style.transform = 'translateX(0)';
            }, 10);

            // Auto-remove after 4 seconds
            setTimeout(() => {
                if (messageElement.parentNode) {
                    messageElement.style.opacity = '0';
                    messageElement.style.transform = 'translateX(100%)';
                    setTimeout(() => messageElement.remove(), 300);
                }
            }, 4000);

        } catch (error) {
            console.error('❌ Error showing header message:', error);
            // Fallback to console log instead of alert
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * Handle conditional field logic for AI-related fields
     * @param {boolean} isAISelected - Whether AI is selected
     */
    handleConditionalFields(isAISelected) {
        try {
            console.log(`🔄 Handling conditional fields - AI selected: ${isAISelected}`);

            // Handle AI Date field
            if (this.fields.artificialInseminationDate) {
                if (isAISelected) {
                    this.enableField('artificialInseminationDate');
                } else {
                    this.disableField('artificialInseminationDate');
                }
            }

            // Handle Expected Calving Date field
            if (this.fields.expectedCalvingDate) {
                if (isAISelected) {
                    this.enableField('expectedCalvingDate');
                } else {
                    this.disableField('expectedCalvingDate');
                }
            }

            // Handle Insemination Status radio buttons
            [this.fields.pregnantRadio, this.fields.reinseminateRadio].forEach(radio => {
                if (radio) {
                    if (isAISelected) {
                        this.enableRadioButton(radio);
                    } else {
                        this.disableRadioButton(radio);
                    }
                }
            });

            // Handle pregnancy-specific logic
            this.handlePregnancyLogic();

            console.log(`✅ Conditional fields ${isAISelected ? 'enabled' : 'disabled'}`);

        } catch (error) {
            console.error('❌ Error handling conditional fields:', error);
        }
    }

    /**
     * Handle pregnancy-specific logic for calving date
     */
    handlePregnancyLogic() {
        try {
            // Check if pregnant radio is selected
            const isPregnant = this.fields.pregnantRadio && this.fields.pregnantRadio.checked;

            if (isPregnant && this.fields.expectedCalvingDate && !this.fields.expectedCalvingDate.value) {
                // Auto-fill expected calving date if AI date is available
                const aiDate = this.fields.artificialInseminationDate ? this.fields.artificialInseminationDate.value : null;
                if (aiDate) {
                    const calvingDate = this.calculateExpectedCalvingDate(aiDate);
                    if (calvingDate) {
                        this.fields.expectedCalvingDate.value = calvingDate;
                        console.log(`✅ Auto-filled calving date for pregnant dairy: ${calvingDate}`);
                    }
                }
            }

        } catch (error) {
            console.error('❌ Error handling pregnancy logic:', error);
        }
    }

    /**
     * Calculate expected calving date from AI date
     * @param {string} aiDate - Artificial insemination date
     */
    calculateExpectedCalvingDate(aiDate) {
        if (!aiDate) return null;

        try {
            const aiDateObj = new Date(aiDate);
            const expectedCalving = new Date(aiDateObj);
            expectedCalving.setDate(expectedCalving.getDate() + 280); // Average gestation period
            return expectedCalving.toISOString().split('T')[0];
        } catch (error) {
            console.error('❌ Error calculating expected calving date:', error);
            return null;
        }
    }

    /**
     * Enable a form field
     * @param {string} fieldName - Name of the field to enable
     */
    enableField(fieldName) {
        const field = this.fields[fieldName];
        if (!field) return;

        field.disabled = false;
        field.style.backgroundColor = '';
        field.style.cursor = '';
        field.style.opacity = '';
        field.style.transition = 'all 0.3s ease';
    }

    /**
     * Disable a form field and clear its value
     * @param {string} fieldName - Name of the field to disable
     */
    disableField(fieldName) {
        const field = this.fields[fieldName];
        if (!field) return;

        field.disabled = true;
        field.value = '';
        field.style.backgroundColor = '#f5f5f5';
        field.style.cursor = 'not-allowed';
        field.style.opacity = '0.6';
        field.style.transition = 'all 0.3s ease';

        // Clear any errors
        this.clearFieldError(fieldName);
    }

    /**
     * Enable a radio button
     * @param {HTMLElement} radio - Radio button element
     */
    enableRadioButton(radio) {
        if (!radio) return;

        radio.disabled = false;
        radio.style.opacity = '';
        radio.style.cursor = '';

        // Enable associated label
        const label = document.querySelector(`label[for="${radio.id}"]`);
        if (label) {
            label.style.opacity = '';
            label.style.cursor = '';
        }
    }

    /**
     * Disable a radio button and clear its selection
     * @param {HTMLElement} radio - Radio button element
     */
    disableRadioButton(radio) {
        if (!radio) return;

        radio.disabled = true;
        radio.checked = false;
        radio.style.opacity = '0.6';
        radio.style.cursor = 'not-allowed';

        // Disable associated label
        const label = document.querySelector(`label[for="${radio.id}"]`);
        if (label) {
            label.style.opacity = '0.6';
            label.style.cursor = 'not-allowed';
        }
    }

    /**
     * Clear all form fields
     */
    clearForm() {
        try {
            // Clear input fields
            const inputFields = ['code', 'birthDate', 'herdNumber', 'weight', 'weightDate',
                               'artificialInseminationDate', 'expectedCalvingDate',
                               'healthCareNotes', 'vaccination'];

            inputFields.forEach(fieldName => {
                const field = this.fields[fieldName];
                if (field) {
                    field.value = '';
                    this.clearFieldError(fieldName);
                }
            });

            // Clear radio button selections except dairy type
            const radioFields = ['yesAIRadio', 'noAIRadio', 'pregnantRadio', 'reinseminateRadio'];
            radioFields.forEach(fieldName => {
                const field = this.fields[fieldName];
                if (field) {
                    field.checked = false;
                }
            });

            // Keep dairy type selected
            if (this.fields.dairyTypeRadio) {
                this.fields.dairyTypeRadio.checked = true;
            }

            console.log('✅ Form cleared successfully');

        } catch (error) {
            console.error('❌ Error clearing form:', error);
        }
    }

    /**
     * Get field value safely
     * @param {string} fieldName - Name of the field
     */
    getFieldValue(fieldName) {
        const field = this.fields[fieldName];
        return field ? field.value.trim() : '';
    }

    /**
     * Get radio button selection for a group
     * @param {string} groupName - Name of the radio group
     */
    getRadioSelection(groupName) {
        try {
            switch (groupName) {
                case 'animalType':
                    if (this.fields.dairyTypeRadio && this.fields.dairyTypeRadio.checked) {
                        return 'Dairy';
                    } else if (this.fields.dryingTypeRadio && this.fields.dryingTypeRadio.checked) {
                        return 'Drying';
                    }
                    return 'Dairy'; // Default

                case 'artificialInsemination':
                    if (this.fields.yesAIRadio && this.fields.yesAIRadio.checked) {
                        return 'Yes';
                    } else if (this.fields.noAIRadio && this.fields.noAIRadio.checked) {
                        return 'No';
                    }
                    return null;

                case 'inseminationStatus':
                    if (this.fields.pregnantRadio && this.fields.pregnantRadio.checked) {
                        return 'Pregnant';
                    } else if (this.fields.reinseminateRadio && this.fields.reinseminateRadio.checked) {
                        return 'Re-inseminate';
                    }
                    return null;

                default:
                    return null;
            }
        } catch (error) {
            console.error('❌ Error getting radio selection:', error);
            return null;
        }
    }
}

// ==================== MAIN CONTROLLER CLASS ====================

/**
 * Main controller for comprehensive dairy animal management
 * Orchestrates all components and handles business logic
 */
class AddNewDairyController {
    constructor() {
        this.dataManager = new DairyDataManager();
        this.validationManager = new DairyValidationManager();
        this.uiManager = new DairyUIManager();

        this.isInitialized = false;
        this.isSubmitting = false;
        this.realTimeSearchTimeout = null;

        console.log('🔧 AddNewDairyController initialized with OOP architecture');
    }

    /**
     * Initialize the comprehensive dairy management system
     */
    async initialize() {
        try {
            console.log('🔄 Initializing comprehensive dairy management system...');

            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // Setup event listeners
            this.setupEventListeners();

            // Setup real-time functionality
            this.setupRealTimeFeatures();

            // Setup conditional field logic
            this.setupConditionalLogic();

            // Set dairy defaults
            this.setDairyDefaults();

            // Setup UI enhancements
            this.setupUIEnhancements();

            this.isInitialized = true;
            console.log('✅ Comprehensive dairy management system initialized successfully');

        } catch (error) {
            console.error('❌ Failed to initialize dairy management system:', error);
            throw error;
        }
    }

    /**
     * Setup all event listeners
     */
    setupEventListeners() {
        try {
            // Form submission
            if (this.uiManager.fields.saveButton) {
                this.uiManager.fields.saveButton.addEventListener('click', async (e) => {
                    e.preventDefault();
                    await this.handleFormSubmission();
                });
            }

            // Real-time validation
            Object.keys(this.uiManager.fields).forEach(fieldName => {
                const field = this.uiManager.fields[fieldName];
                if (field && field.tagName && (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA')) {
                    // Validation on blur
                    field.addEventListener('blur', () => {
                        this.validateFieldRealTime(fieldName);
                    });

                    // Clear errors on input
                    field.addEventListener('input', () => {
                        this.uiManager.clearFieldError(fieldName);
                    });
                }
            });

            // AI conditional logic
            [this.uiManager.fields.yesAIRadio, this.uiManager.fields.noAIRadio].forEach(radio => {
                if (radio) {
                    radio.addEventListener('change', () => {
                        this.handleAIConditionalLogic();
                    });
                }
            });

            // Pregnancy status logic
            [this.uiManager.fields.pregnantRadio, this.uiManager.fields.reinseminateRadio].forEach(radio => {
                if (radio) {
                    radio.addEventListener('change', () => {
                        this.handlePregnancyStatusChange();
                    });
                }
            });

            // Auto-calculate expected calving date
            if (this.uiManager.fields.artificialInseminationDate) {
                this.uiManager.fields.artificialInseminationDate.addEventListener('change', () => {
                    this.autoCalculateCalvingDate();
                });
            }

            console.log('✅ Event listeners setup completed');

        } catch (error) {
            console.error('❌ Error setting up event listeners:', error);
        }
    }

    /**
     * Setup real-time features for new animal registration
     */
    setupRealTimeFeatures() {
        try {
            // For new animal registration, we only need real-time validation
            // No code lookup since we're adding new animals only
            console.log('✅ Real-time features setup completed - New animal registration mode');

        } catch (error) {
            console.error('❌ Error setting up real-time features:', error);
        }
    }

    // Code lookup methods removed - this is for NEW animal registration only

    /**
     * Handle form submission with comprehensive validation and processing
     */
    async handleFormSubmission() {
        try {
            if (this.isSubmitting) {
                console.log('⚠️ Form submission already in progress');
                return;
            }

            console.log('🔄 Starting comprehensive dairy animal registration...');
            this.isSubmitting = true;

            // Show loading state
            this.uiManager.showLoadingState(true);

            // Collect form data
            const formData = this.collectFormData();

            // Validate all fields
            const fieldStates = this.getFieldStates();
            const validation = this.validationManager.validateAllFields(formData, fieldStates);

            if (!validation.isValid) {
                this.displayValidationErrors(validation.errors);
                this.uiManager.showMessage('Please correct the errors before submitting', 'error');
                return;
            }

            // Validate business rules
            const businessErrors = this.validationManager.validateBusinessRules(formData);
            if (businessErrors.length > 0) {
                this.uiManager.showMessage(businessErrors.join(', '), 'error');
                return;
            }

            // Generate code if needed
            if (!formData.code) {
                formData.code = this.dataManager.generateUniqueCode();
                this.uiManager.fields.code.value = formData.code;
            }

            // Check for duplicate codes
            if (this.dataManager.isCodeExists(formData.code)) {
                this.uiManager.showFieldError('code', 'This animal code already exists');
                this.uiManager.showMessage('Animal code already exists', 'error');
                return;
            }

            // Create comprehensive animal data
            const animalData = this.createAnimalData(formData);

            // Save to both tables
            await this.dataManager.saveAnimal(animalData);

            // Show success message
            this.uiManager.showMessage('Dairy animal registered successfully! 🐄', 'success');

            // Clear form for next entry
            this.clearForm();

            // Close popup if in iframe
            this.closePopupIfInIframe();

            console.log('✅ Comprehensive dairy animal registration completed successfully');

        } catch (error) {
            console.error('❌ Error during form submission:', error);
            this.uiManager.showMessage('Failed to register dairy animal. Please try again.', 'error');
        } finally {
            this.isSubmitting = false;
            this.uiManager.showLoadingState(false);
        }
        
    }

    /**
     * Collect comprehensive form data ensuring all fields are captured
     */
    collectFormData() {
        console.log('🔄 Starting form data collection...');

        // Debug: Check each field value individually
        const fieldValues = {
            code: this.uiManager.getFieldValue('code'),
            birthDate: this.uiManager.getFieldValue('birthDate'),
            herdNumber: this.uiManager.getFieldValue('herdNumber'),
            weight: this.uiManager.getFieldValue('weight'),
            weightDate: this.uiManager.getFieldValue('weightDate'),
            artificialInseminationDate: this.uiManager.getFieldValue('artificialInseminationDate'),
            expectedCalvingDate: this.uiManager.getFieldValue('expectedCalvingDate'),
            healthCareNotes: this.uiManager.getFieldValue('healthCareNotes'),
            vaccination: this.uiManager.getFieldValue('vaccination')
        };

        console.log('🔍 Individual field values:', fieldValues);

        // Debug: Check radio button selections
        const radioSelections = {
            animalType: this.uiManager.getRadioSelection('animalType'),
            artificialInsemination: this.uiManager.getRadioSelection('artificialInsemination'),
            inseminationStatus: this.uiManager.getRadioSelection('inseminationStatus')
        };

        console.log('🔍 Radio button selections:', radioSelections);

        const formData = {
            // Basic information
            code: fieldValues.code,
            birthDate: fieldValues.birthDate,
            herdNumber: fieldValues.herdNumber,

            // Physical information
            weight: fieldValues.weight,
            weightDate: fieldValues.weightDate,

            // Breeding information (only collect if AI is selected and fields are enabled)
            artificialInsemination: radioSelections.artificialInsemination,
            artificialInseminationDate: null,
            expectedCalvingDate: null,
            inseminationStatus: null,

            // Health information
            healthCareNotes: fieldValues.healthCareNotes,
            vaccination: fieldValues.vaccination,

            // Animal type
            animalType: radioSelections.animalType
        };

        // Only collect AI-related data if AI is selected
        if (formData.artificialInsemination === 'Yes') {
            console.log('🔍 AI is selected, collecting AI-related data...');
            // Only get values from enabled fields
            if (this.uiManager.fields.artificialInseminationDate && !this.uiManager.fields.artificialInseminationDate.disabled) {
                formData.artificialInseminationDate = fieldValues.artificialInseminationDate;
                console.log('✅ AI date collected:', formData.artificialInseminationDate);
            }
            if (this.uiManager.fields.expectedCalvingDate && !this.uiManager.fields.expectedCalvingDate.disabled) {
                formData.expectedCalvingDate = fieldValues.expectedCalvingDate;
                console.log('✅ Expected calving date collected:', formData.expectedCalvingDate);
            }
            formData.inseminationStatus = radioSelections.inseminationStatus;
            console.log('✅ Insemination status collected:', formData.inseminationStatus);
        } else {
            console.log('🔍 AI is not selected, skipping AI-related data');
        }

        console.log('📊 Final form data collected:', formData);
        return formData;
    }

    /**
     * Get current field states (enabled/disabled)
     */
    getFieldStates() {
        return {
            artificialInseminationDate: !this.uiManager.fields.artificialInseminationDate?.disabled,
            expectedCalvingDate: !this.uiManager.fields.expectedCalvingDate?.disabled
        };
    }

    /**
     * Create comprehensive animal data object ensuring ALL ANIMAL TABLE COLUMNS are filled
     * @param {Object} formData - Form data
     */
    createAnimalData(formData) {
        const now = new Date();
        const registrationDate = now.toISOString().split('T')[0];
        const animalId = Date.now().toString();

        // Create comprehensive animal data with all required fields
        const animalData = {
            // Basic information (required for both tables)
            id: animalId,
            code: formData.code || this.dataManager.generateUniqueCode(),
            name: this.dataManager.generateIntelligentName(
                formData.code,
                formData.herdNumber,
                formData.birthDate
            ),
            breed: 'Dairy Cattle', // Default breed for dairy animals
            gender: 'female', // Always female for dairy animals (lowercase to match update system)
            type: 'dairy', // Always dairy type
            birthDate: formData.birthDate || '',
            herdNumber: formData.herdNumber || '',
            status: 'Active',
            registrationDate: registrationDate,

            // === ANIMAL TABLE COLUMN MAPPINGS ===
            // These fields map directly to animal table columns

            // Column: Code → code (already set above)
            // Column: Type → type (already set above)
            // Column: Herd Number → herdNumber (already set above)
            // Column: Gender → gender (already set above)
            // Column: Weight → weight
            weight: formData.weight || '',

            // Column: Date of Weight → dateOfWeight (animal table uses this field name)
            dateOfWeight: formData.weightDate || registrationDate,
            weightDate: formData.weightDate || registrationDate, // Keep both for compatibility

            // Column: Healthcare → healthcareNotes (animal table uses this field name)
            // Animal table only shows healthcare notes, NOT vaccination
            healthcareNotes: formData.healthCareNotes || '',
            healthCareNotes: formData.healthCareNotes || '', // Keep original for dairy table

            // Breeding information (conditional based on AI selection)
            artificialInsemination: formData.artificialInsemination || 'No',
            artificialInseminationDate: formData.artificialInseminationDate || null,
            inseminationStatus: formData.inseminationStatus || null,
            expectedCalvingDate: formData.expectedCalvingDate || null,
            breedingCycleStage: this.dataManager.calculateBreedingCycleStage(
                formData.artificialInsemination,
                formData.inseminationStatus
            ),

            // Health information (additional fields)
            vaccination: formData.vaccination || '',
            lastHealthCheckDate: registrationDate,
            nextVaccinationDue: this.dataManager.calculateNextVaccinationDate(),

            // Dairy-specific production data
            lactationNumber: 0, // New animal starts at 0
            totalMilkProduced: 0,
            averageDailyMilk: 0,
            lastMilkingDate: null,
            milkingSchedule: 'twice-daily', // Default milking schedule

            // Additional metadata
            createdAt: now.toISOString(),
            updatedAt: now.toISOString(),
            source: 'addnewdairy_form'
        };

        console.log('🐄 Animal data created with table column mappings:');
        console.log('  Code:', animalData.code);
        console.log('  Type:', animalData.type);
        console.log('  Herd Number:', animalData.herdNumber);
        console.log('  Gender:', animalData.gender);
        console.log('  Weight:', animalData.weight);
        console.log('  Date of Weight:', animalData.dateOfWeight);
        console.log('  Healthcare:', animalData.healthcareNotes);

        return animalData;
    }



    /**
     * Display validation errors on form fields
     * @param {Object} errors - Validation errors
     */
    displayValidationErrors(errors) {
        Object.keys(errors).forEach(fieldName => {
            this.uiManager.showFieldError(fieldName, errors[fieldName]);
        });
    }

    /**
     * Validate field in real-time
     * @param {string} fieldName - Name of field to validate
     */
    validateFieldRealTime(fieldName) {
        try {
            const value = this.uiManager.getFieldValue(fieldName);
            const formData = this.collectFormData();
            const validation = this.validationManager.validateField(fieldName, value, formData);

            if (!validation.isValid) {
                this.uiManager.showFieldError(fieldName, validation.message);
            } else {
                this.uiManager.clearFieldError(fieldName);
            }

        } catch (error) {
            console.error('❌ Error in real-time validation:', error);
        }
    }

    /**
     * Handle AI conditional logic
     */
    handleAIConditionalLogic() {
        try {
            const isAISelected = this.uiManager.fields.yesAIRadio && this.uiManager.fields.yesAIRadio.checked;
            this.uiManager.handleConditionalFields(isAISelected);

        } catch (error) {
            console.error('❌ Error handling AI conditional logic:', error);
        }
    }

    /**
     * Handle pregnancy status change
     */
    handlePregnancyStatusChange() {
        try {
            const isPregnant = this.uiManager.fields.pregnantRadio && this.uiManager.fields.pregnantRadio.checked;

            if (isPregnant) {
                // Auto-fill calving date if pregnant and AI date is available
                const aiDate = this.uiManager.getFieldValue('artificialInseminationDate');
                if (aiDate && this.uiManager.fields.expectedCalvingDate) {
                    const calvingDate = this.dataManager.calculateExpectedCalvingDate(aiDate);
                    if (calvingDate) {
                        this.uiManager.fields.expectedCalvingDate.value = calvingDate;
                        this.uiManager.showMessage('Expected calving date filled automatically', 'success');
                        console.log(`✅ Auto-filled calving date for pregnant dairy: ${calvingDate}`);
                    }
                }
            }

        } catch (error) {
            console.error('❌ Error handling pregnancy status change:', error);
        }
    }

    /**
     * Auto-calculate expected calving date from AI date
     */
    autoCalculateCalvingDate() {
        try {
            const aiDate = this.uiManager.getFieldValue('artificialInseminationDate');
            if (aiDate && this.uiManager.fields.expectedCalvingDate) {
                const expectedDate = this.dataManager.calculateExpectedCalvingDate(aiDate);
                if (expectedDate) {
                    this.uiManager.fields.expectedCalvingDate.value = expectedDate;
                    console.log(`✅ Expected calving date calculated: ${expectedDate}`);
                }
            }

        } catch (error) {
            console.error('❌ Error auto-calculating calving date:', error);
        }
    }

    /**
     * Setup conditional logic
     */
    setupConditionalLogic() {
        try {
            // Initialize conditional logic - start with AI fields disabled
            // Since no AI option is selected by default, disable all AI-related fields
            this.uiManager.handleConditionalFields(false);
            console.log('✅ Conditional logic setup completed - AI fields disabled by default');

        } catch (error) {
            console.error('❌ Error setting up conditional logic:', error);
        }
    }

    /**
     * Set dairy defaults
     */
    setDairyDefaults() {
        try {
            // Auto-select dairy type
            if (this.uiManager.fields.dairyTypeRadio) {
                this.uiManager.fields.dairyTypeRadio.checked = true;

                // Style to show it's selected
                const dairyLabel = document.querySelector('label[for="milch"]');
                if (dairyLabel) {
                    dairyLabel.style.fontWeight = 'bold';
                    dairyLabel.style.color = '#28a745';
                }
            }

            // Disable drying option
            if (this.uiManager.fields.dryingTypeRadio) {
                this.uiManager.fields.dryingTypeRadio.disabled = true;
                const dryingLabel = document.querySelector('label[for="drying"]');
                if (dryingLabel) {
                    dryingLabel.style.opacity = '0.5';
                    dryingLabel.style.cursor = 'not-allowed';
                }
            }

            console.log('✅ Dairy defaults set');

        } catch (error) {
            console.error('❌ Error setting dairy defaults:', error);
        }
    }

    /**
     * Setup UI enhancements
     */
    setupUIEnhancements() {
        try {
            // Focus on code field (first field) since no fields are required
            if (this.uiManager.fields.code) {
                setTimeout(() => this.uiManager.fields.code.focus(), 100);
            }

            console.log('✅ UI enhancements setup completed');

        } catch (error) {
            console.error('❌ Error setting up UI enhancements:', error);
        }
    }

    /**
     * Clear form and reset to defaults
     */
    clearForm() {
        try {
            this.uiManager.clearForm();
            this.setDairyDefaults();
            // Reset AI fields to disabled state since no AI option is selected after clearing
            this.uiManager.handleConditionalFields(false);

        } catch (error) {
            console.error('❌ Error clearing form:', error);
        }
    }

    // Code lookup loading methods removed - not needed for new animal registration

    /**
     * Close popup if running in iframe
     */
    closePopupIfInIframe() {
        try {
            if (window.self !== window.top) {
                console.log('🔄 Closing popup via postMessage...');

                setTimeout(() => {
                    window.parent.postMessage({
                        action: 'closePopup',
                        source: 'addnewdairy'
                    }, '*');

                    console.log('✅ Popup close signal sent');
                }, 1000);
            }

        } catch (error) {
            console.error('❌ Error closing popup:', error);
        }
    }
    
    
}

// ==================== INITIALIZATION ====================

/**
 * Global controller instance
 */
let addNewDairyController = null;

/**
 * Initialize the comprehensive dairy management system when DOM is ready
 */
document.addEventListener('DOMContentLoaded', async function() {
    try {
        console.log('🔄 DOM loaded - Initializing Comprehensive Dairy Management System...');

        // Create and initialize controller
        addNewDairyController = new AddNewDairyController();
        await addNewDairyController.initialize();

        // Make globally accessible
        window.addNewDairyController = addNewDairyController;

        console.log('✅ Comprehensive Dairy Management System fully initialized and ready');

    } catch (error) {
        console.error('❌ Failed to initialize Comprehensive Dairy Management System:', error);
    }
});

// ==================== GLOBAL FUNCTIONS ====================

/**
 * Global function to save dairy animal
 */
window.saveDairyAnimal = function() {
    if (window.addNewDairyController) {
        window.addNewDairyController.handleFormSubmission();
    } else {
        console.error('Dairy Controller not initialized');
    }
    
};

/**
 * Global function to clear dairy form
 */
window.clearDairyForm = function() {
    if (window.addNewDairyController) {
        window.addNewDairyController.clearForm();
    } else {
        console.error('Dairy Controller not initialized');
    }
};

/**
 * Debug function to check dairy table contents
 */
window.checkDairyTable = function() {
    const animals = JSON.parse(localStorage.getItem('animals') || '[]');
    // FIX: Use 'dairyAnimals' key to match main dairy page
    const dairy = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');

    console.log('🔍 DAIRY TABLE DEBUG:');
    console.log('📊 Animals table count:', animals.length);
    console.log('🥛 Dairy table count:', dairy.length);
    console.log('📋 Animals table:', animals);
    console.log('🥛 Dairy table:', dairy);

    // Check if any animals are dairy type
    const dairyAnimals = animals.filter(animal => animal.type === 'dairy');
    console.log('🐄 Dairy animals in animals table:', dairyAnimals.length);

    return { animals, dairy, dairyAnimals };
};

/**
 * Debug function to test dairy animal creation
 */
window.testDairyCreation = function() {
    if (window.addNewDairyController) {
        const testData = {
            code: 'TEST001',
            birthDate: '2023-01-01',
            herdNumber: 'H001',
            weight: '500',
            weightDate: '2023-01-01',
            artificialInsemination: 'No',
            healthCareNotes: 'Test animal',
            vaccination: 'Test vaccination',
            animalType: 'Dairy'
        };

        console.log('🧪 Testing dairy animal creation with data:', testData);
        const animalData = window.addNewDairyController.createAnimalData(testData);
        console.log('🐄 Created animal data:', animalData);

        // Test saving
        window.addNewDairyController.dataManager.saveAnimal(animalData)
            .then(() => {
                console.log('✅ Test dairy animal saved successfully');
                window.checkDairyTable();
            })
            .catch(error => {
                console.error('❌ Test dairy animal save failed:', error);
            });
    } else {
        console.error('Dairy Controller not initialized');
    }
};

/**
 * Debug function to clear all data and start fresh
 */
window.clearAllData = function() {
    localStorage.removeItem('animals');
    // FIX: Use 'dairyAnimals' key to match main dairy page
    localStorage.removeItem('dairyAnimals');
    console.log('🗑️ All data cleared from localStorage');
    window.checkDairyTable();
};

/**
 * Debug function to manually test form field collection
 */
window.testFormFields = function() {
    if (window.addNewDairyController) {
        console.log('🔍 Testing form field collection...');

        // Check if fields are found
        const fields = window.addNewDairyController.uiManager.fields;
        console.log('📋 Available fields:', Object.keys(fields));

        // Test field values
        Object.keys(fields).forEach(fieldName => {
            const field = fields[fieldName];
            if (field && (field.tagName === 'INPUT' || field.tagName === 'TEXTAREA')) {
                console.log(`  ${fieldName}: "${field.value}" (type: ${field.type || 'text'})`);
            } else if (field && field.type === 'radio') {
                console.log(`  ${fieldName}: ${field.checked ? 'checked' : 'unchecked'} (radio)`);
            }
        });

        // Test data collection
        const formData = window.addNewDairyController.collectFormData();
        console.log('📊 Collected form data:', formData);

    } else {
        console.error('Dairy Controller not initialized');
    }
};

// ==================== CONSOLE STARTUP MESSAGE ====================

console.log(`
🎉 ADD NEW DAIRY ANIMAL SYSTEM LOADED
📋 New Animal Registration Features:
   • Add NEW dairy animals only (no search/lookup)
   • All fields are optional (no required fields)
   • Flexible animal code validation (any length allowed)
   • Comprehensive validation with business rules
   • Dual table storage (animals + dairy)
   • Small header messages instead of alerts
   • Conditional field logic for AI-related fields
   • Auto-calculation of breeding dates and production metrics
   • Intelligent name generation from available data
   • Loading states and visual feedback
   • Popup integration with postMessage communication

🔧 Object-Oriented Architecture:
   • DairyDataManager - Handles all data operations
   • DairyValidationManager - Comprehensive validation
   • DairyUIManager - UI interactions and visual feedback
   • AddNewDairyController - Main orchestrator

🆕 Conditional Field Logic:
   • AI = "No" → Disables and clears AI date, calving date, status fields
   • AI = "Yes" → Enables all AI-related fields with full functionality
   • Pregnant status → Auto-fills calving date if AI date available
   • Real-time field enable/disable based on selections
   • Small header notifications (top-right corner)

🔍 DEBUG FUNCTIONS AVAILABLE:
   • checkDairyTable() - Check current dairy table contents
   • testDairyCreation() - Test dairy animal creation and saving
   • testFormFields() - Test form field collection and values
   • clearAllData() - Clear all localStorage data
   • testDairyDataFlow() - 🧪 COMPREHENSIVE test of entire data flow
   • checkCurrentFormData() - Check current form field values
   • ultimateDairyDebug() - 🔬 ULTIMATE step-by-step investigation
   • addManualDairyAnimal() - Manually add test dairy animal
   • testTableColumnMapping() - 📊 Test all table column mappings
   • checkAnimalTableTypes() - 🔍 Check how animals appear in animal table
   • testDairyAnimalInAnimalTable() - 🧪 Test dairy animal in animal table
   • checkGenderCaseIssue() - 👤 Check for gender case sensitivity issues

✅ Ready for flexible dairy animal registration!
`);

// Check initial state
setTimeout(() => {
    console.log('🔍 Initial dairy table check:');
    window.checkDairyTable();
}, 1000);

/**
 * COMPREHENSIVE TEST FUNCTION - Check if addnewdairy sends data to both tables
 */
window.testDairyDataFlow = function() {
    console.log('🧪 COMPREHENSIVE DAIRY DATA FLOW TEST');
    console.log('=====================================');

    // Step 1: Check initial state
    console.log('\n📊 STEP 1: Initial State Check');
    const initialAnimals = JSON.parse(localStorage.getItem('animals') || '[]');
    // FIX: Use 'dairyAnimals' key to match main dairy page
    const initialDairy = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
    console.log(`Initial animals count: ${initialAnimals.length}`);
    console.log(`Initial dairy count: ${initialDairy.length}`);

    // Step 2: Check if controller is initialized
    console.log('\n🔧 STEP 2: Controller Check');
    if (!window.addNewDairyController) {
        console.error('❌ addNewDairyController not initialized!');
        return;
    }
    console.log('✅ addNewDairyController is initialized');

    // Step 3: Test data creation
    console.log('\n📋 STEP 3: Test Data Creation');
    const testFormData = {
        code: 'TEST' + Date.now(),
        birthDate: '2023-01-15',
        herdNumber: 'H001',
        weight: '450',
        weightDate: '2023-01-15',
        artificialInsemination: 'Yes',
        artificialInseminationDate: '2023-06-01',
        expectedCalvingDate: '2024-03-08',
        inseminationStatus: 'Pregnant',
        healthCareNotes: 'Healthy cow, regular checkups',
        vaccination: 'Annual vaccination completed',
        animalType: 'dairy'
    };
    console.log('Test form data:', testFormData);

    // Step 4: Create animal data
    console.log('\n🐄 STEP 4: Animal Data Creation');
    const animalData = window.addNewDairyController.createAnimalData(testFormData);
    console.log('Created animal data:', animalData);
    console.log(`Animal type: ${animalData.type}`);
    console.log(`Animal gender: ${animalData.gender}`);
    console.log(`Animal ID: ${animalData.id}`);

    // Step 5: Test dairy record creation
    console.log('\n🥛 STEP 5: Dairy Record Creation');
    const dairyData = window.addNewDairyController.dataManager.createDairyRecord(animalData);
    console.log('Created dairy data:', dairyData);
    console.log(`Dairy ID matches animal ID: ${dairyData.id === animalData.id}`);

    // Step 6: Test saving to both tables
    console.log('\n💾 STEP 6: Saving to Both Tables');
    window.addNewDairyController.dataManager.saveAnimal(animalData)
        .then(() => {
            console.log('\n✅ STEP 7: Verification After Save');

            // Check final state
            const finalAnimals = JSON.parse(localStorage.getItem('animals') || '[]');
            // FIX: Use 'dairyAnimals' key to match main dairy page
            const finalDairy = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');

            console.log(`Final animals count: ${finalAnimals.length} (was ${initialAnimals.length})`);
            console.log(`Final dairy count: ${finalDairy.length} (was ${initialDairy.length})`);

            // Check if our test animal was saved
            const savedAnimal = finalAnimals.find(a => a.id === animalData.id);
            const savedDairy = finalDairy.find(d => d.id === animalData.id);

            console.log('\n🔍 DETAILED VERIFICATION:');
            console.log(`Animal saved to animals table: ${savedAnimal ? '✅ YES' : '❌ NO'}`);
            console.log(`Animal saved to dairy table: ${savedDairy ? '✅ YES' : '❌ NO'}`);

            if (savedAnimal) {
                console.log('Saved animal details:', {
                    id: savedAnimal.id,
                    code: savedAnimal.code,
                    type: savedAnimal.type,
                    gender: savedAnimal.gender,
                    birthDate: savedAnimal.birthDate
                });
            }

            if (savedDairy) {
                console.log('Saved dairy details:', {
                    id: savedDairy.id,
                    code: savedDairy.code,
                    type: savedDairy.type,
                    gender: savedDairy.gender,
                    birthDate: savedDairy.birthDate
                });
            }

            // Final summary
            console.log('\n📋 FINAL TEST SUMMARY:');
            const animalsIncreased = finalAnimals.length > initialAnimals.length;
            const dairyIncreased = finalDairy.length > initialDairy.length;
            const bothTablesUpdated = animalsIncreased && dairyIncreased;

            console.log(`Animals table updated: ${animalsIncreased ? '✅ YES' : '❌ NO'}`);
            console.log(`Dairy table updated: ${dairyIncreased ? '✅ YES' : '❌ NO'}`);
            console.log(`Both tables updated: ${bothTablesUpdated ? '✅ YES' : '❌ NO'}`);

            if (bothTablesUpdated) {
                console.log('🎉 SUCCESS: addnewdairy is correctly sending data to both tables!');
            } else {
                console.log('❌ FAILURE: addnewdairy is NOT sending data to both tables correctly!');
            }
        })
        .catch(error => {
            console.error('❌ Error during save test:', error);
        });
};

/**
 * Quick test function to verify current form field values
 */
window.checkCurrentFormData = function() {
    if (!window.addNewDairyController) {
        console.error('❌ Controller not initialized');
        return;
    }

    console.log('📋 CURRENT FORM DATA CHECK:');
    console.log('============================');

    // Test field collection
    const formData = window.addNewDairyController.collectFormData();
    console.log('Current form data:', formData);

    // Check which fields have values
    console.log('\n🔍 Field Status:');
    Object.keys(formData).forEach(key => {
        const value = formData[key];
        const hasValue = value && value !== '' && value !== null;
        console.log(`  ${key}: ${hasValue ? '✅ HAS VALUE' : '❌ EMPTY'} (${value})`);
    });

    return formData;
};

/**
 * ULTIMATE DEBUG FUNCTION - Step by step dairy table investigation
 */
window.ultimateDairyDebug = function() {
    console.log('🔬 ULTIMATE DAIRY TABLE DEBUG');
    console.log('==============================');

    // Step 1: Check localStorage keys
    console.log('\n📋 STEP 1: localStorage Keys Check');
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        const value = localStorage.getItem(key);
        console.log(`  ${key}: ${value ? `${value.length} chars` : 'null'}`);

        if (key === 'dairyAnimals' || key === 'dairy') {
            try {
                const parsed = JSON.parse(value);
                console.log(`    → Parsed ${key}:`, parsed.length, 'items');
                if (parsed.length > 0) {
                    console.log(`    → First item:`, parsed[0]);
                }
            } catch (e) {
                console.log(`    → Parse error for ${key}:`, e.message);
            }
        }
    }

    // Step 2: Test manual dairy animal creation
    console.log('\n🧪 STEP 2: Manual Dairy Animal Creation');
    const testAnimal = {
        id: 'TEST_' + Date.now(),
        code: 'MANUAL001',
        name: 'Manual Test Cow',
        type: 'dairy',
        gender: 'Female',
        breed: 'Holstein',
        birthDate: '2023-01-01',
        herdNumber: 'H001',
        weight: '500',
        status: 'Active',
        registrationDate: new Date().toISOString().split('T')[0]
    };

    console.log('Created test animal:', testAnimal);

    // Step 3: Test direct localStorage save
    console.log('\n💾 STEP 3: Direct localStorage Save Test');
    try {
        // Get existing dairy animals
        const existingDairy = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
        console.log('Existing dairy animals:', existingDairy.length);

        // Add test animal
        existingDairy.push(testAnimal);

        // Save back
        localStorage.setItem('dairyAnimals', JSON.stringify(existingDairy));
        console.log('✅ Saved test animal to dairyAnimals');

        // Verify save
        const verifyDairy = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
        console.log('Verification - dairy animals count:', verifyDairy.length);

        const foundTestAnimal = verifyDairy.find(a => a.id === testAnimal.id);
        console.log('Test animal found:', foundTestAnimal ? '✅ YES' : '❌ NO');

    } catch (error) {
        console.error('❌ Error in direct save test:', error);
    }

    // Step 4: Test form submission if controller exists
    console.log('\n📝 STEP 4: Form Submission Test');
    if (window.addNewDairyController) {
        console.log('Controller exists, testing form submission...');

        const testFormData = {
            code: 'FORM_' + Date.now(),
            birthDate: '2023-01-01',
            herdNumber: 'H002',
            weight: '450',
            artificialInsemination: 'No',
            healthCareNotes: 'Test notes',
            vaccination: 'Test vaccination',
            animalType: 'dairy'
        };

        console.log('Test form data:', testFormData);

        try {
            const animalData = window.addNewDairyController.createAnimalData(testFormData);
            console.log('Created animal data:', animalData);

            // Test save
            window.addNewDairyController.dataManager.saveAnimal(animalData)
                .then(() => {
                    console.log('✅ Form submission test completed');

                    // Final verification
                    const finalDairy = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');
                    console.log('Final dairy count:', finalDairy.length);

                    const foundFormAnimal = finalDairy.find(a => a.id === animalData.id);
                    console.log('Form animal found in dairy table:', foundFormAnimal ? '✅ YES' : '❌ NO');

                    if (foundFormAnimal) {
                        console.log('Form animal details:', foundFormAnimal);
                    }
                })
                .catch(error => {
                    console.error('❌ Form submission test failed:', error);
                });

        } catch (error) {
            console.error('❌ Error creating animal data:', error);
        }

    } else {
        console.log('❌ Controller not found');
    }

    // Step 5: Final summary
    console.log('\n📊 STEP 5: Final Summary');
    setTimeout(() => {
        const finalAnimals = JSON.parse(localStorage.getItem('animals') || '[]');
        const finalDairy = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');

        console.log('=== FINAL RESULTS ===');
        console.log(`Animals table: ${finalAnimals.length} items`);
        console.log(`Dairy table: ${finalDairy.length} items`);

        if (finalDairy.length > 0) {
            console.log('✅ Dairy table has data!');
            console.log('Latest dairy animal:', finalDairy[finalDairy.length - 1]);
        } else {
            console.log('❌ Dairy table is empty!');
        }
    }, 2000);
};

/**
 * Simple function to manually add dairy animal for testing
 */
window.addManualDairyAnimal = function() {
    const manualAnimal = {
        id: 'MANUAL_' + Date.now(),
        code: 'MAN001',
        name: 'Manual Dairy Cow',
        type: 'dairy',
        gender: 'Female',
        breed: 'Holstein',
        birthDate: '2023-01-01',
        herdNumber: 'H999',
        weight: '500',
        status: 'Active',
        registrationDate: new Date().toISOString().split('T')[0],
        // Dairy specific fields
        lactationNumber: 0,
        totalMilkProduced: 0,
        averageDailyMilk: 0,
        milkingSchedule: 'twice-daily'
    };

    // Add to both tables
    const animals = JSON.parse(localStorage.getItem('animals') || '[]');
    const dairyAnimals = JSON.parse(localStorage.getItem('dairyAnimals') || '[]');

    animals.push(manualAnimal);
    dairyAnimals.push(manualAnimal);

    localStorage.setItem('animals', JSON.stringify(animals));
    localStorage.setItem('dairyAnimals', JSON.stringify(dairyAnimals));

    console.log('✅ Manual dairy animal added:', manualAnimal);
    console.log('Animals table count:', animals.length);
    console.log('Dairy table count:', dairyAnimals.length);

    return manualAnimal;
};

/**
 * COMPREHENSIVE TABLE COLUMN MAPPING TEST - BOTH ANIMAL AND DAIRY TABLES
 */
window.testTableColumnMapping = function() {
    console.log('🔍 COMPREHENSIVE TABLE COLUMN MAPPING TEST');
    console.log('==========================================');

    // Test data with all form fields filled
    const completeFormData = {
        code: 'COL001',
        birthDate: '2023-01-15',
        herdNumber: 'H123',
        weight: '550',
        weightDate: '2023-12-01',
        artificialInsemination: 'Yes',
        artificialInseminationDate: '2023-06-15',
        expectedCalvingDate: '2024-03-22',
        inseminationStatus: 'Pregnant',
        healthCareNotes: 'Regular health checkups completed',
        vaccination: 'Annual vaccination done',
        animalType: 'dairy'
    };

    console.log('\n📋 Test Form Data:', completeFormData);

    if (!window.addNewDairyController) {
        console.error('❌ Controller not initialized');
        return;
    }

    // Create animal data
    const animalData = window.addNewDairyController.createAnimalData(completeFormData);
    console.log('\n🐄 Created Animal Data:', animalData);

    // Create dairy record
    const dairyRecord = window.addNewDairyController.dataManager.createDairyRecord(animalData);
    console.log('\n🥛 Created Dairy Record:', dairyRecord);

    // Test ANIMAL table column mappings
    console.log('\n📊 ANIMAL TABLE COLUMN MAPPINGS:');
    console.log('=================================');

    const animalColumnMappings = [
        { column: 'Code', field: 'code', value: animalData.code },
        { column: 'Type', field: 'type', value: animalData.type },
        { column: 'Herd Number', field: 'herdNumber', value: animalData.herdNumber },
        { column: 'Gender', field: 'gender', value: animalData.gender },
        { column: 'Weight', field: 'weight', value: animalData.weight },
        { column: 'Date of Weight', field: 'dateOfWeight', value: animalData.dateOfWeight },
        { column: 'Healthcare', field: 'healthcareNotes', value: animalData.healthcareNotes }
    ];

    let allAnimalColumnsFilled = true;

    animalColumnMappings.forEach(mapping => {
        const hasValue = mapping.value && mapping.value !== '' && mapping.value !== 'N/A' && mapping.value !== null;
        const status = hasValue ? '✅ FILLED' : '❌ EMPTY';
        console.log(`  ${mapping.column}: ${status} (${mapping.value})`);

        if (!hasValue) {
            allAnimalColumnsFilled = false;
        }
    });

    // Test DAIRY table column mappings
    console.log('\n📊 DAIRY TABLE COLUMN MAPPINGS:');
    console.log('================================');

    const dairyColumnMappings = [
        { column: 'Code', field: 'code', value: dairyRecord.code },
        { column: 'Type', field: 'type', value: dairyRecord.type },
        { column: 'HM (Herd Number)', field: 'herdNumber', value: dairyRecord.herdNumber },
        { column: 'MP (Milk Production)', field: 'lastMilkProduction', value: dairyRecord.lastMilkProduction },
        { column: 'FP (Fat Percentage)', field: 'fatPercentage', value: dairyRecord.fatPercentage },
        { column: 'Weight', field: 'weight', value: dairyRecord.weight },
        { column: 'DW (Date of Weight)', field: 'weightDate', value: dairyRecord.weightDate },
        { column: 'SAI (Status of AI)', field: 'artificialInseminationStatus', value: dairyRecord.artificialInseminationStatus },
        { column: 'DAI (Date of AI)', field: 'artificialInseminationDate', value: dairyRecord.artificialInseminationDate },
        { column: 'EDC (Expected Calving)', field: 'expectedCalfingDate', value: dairyRecord.expectedCalfingDate },
        { column: 'Healthcare', field: 'healthcareNotes', value: dairyRecord.healthcareNotes }
    ];

    let allDairyColumnsFilled = true;

    dairyColumnMappings.forEach(mapping => {
        const hasValue = mapping.value && mapping.value !== '' && mapping.value !== 'N/A' && mapping.value !== null;
        const status = hasValue ? '✅ FILLED' : '❌ EMPTY';
        console.log(`  ${mapping.column}: ${status} (${mapping.value})`);

        if (!hasValue) {
            allDairyColumnsFilled = false;
        }
    });

    // Overall summary
    console.log('\n📋 OVERALL SUMMARY:');
    console.log('===================');
    console.log(`Animal table columns filled: ${allAnimalColumnsFilled ? '✅ YES' : '❌ NO'}`);
    console.log(`Dairy table columns filled: ${allDairyColumnsFilled ? '✅ YES' : '❌ NO'}`);
    console.log(`Both tables complete: ${(allAnimalColumnsFilled && allDairyColumnsFilled) ? '✅ YES' : '❌ NO'}`);

    // Missing data analysis
    if (!allAnimalColumnsFilled || !allDairyColumnsFilled) {
        console.log('\n⚠️ MISSING DATA ANALYSIS:');

        if (!allAnimalColumnsFilled) {
            console.log('\n❌ ANIMAL TABLE ISSUES:');
            animalColumnMappings.forEach(mapping => {
                const hasValue = mapping.value && mapping.value !== '' && mapping.value !== 'N/A' && mapping.value !== null;
                if (!hasValue) {
                    console.log(`  ❌ ${mapping.column} (${mapping.field}) is empty`);
                }
            });
        }

        if (!allDairyColumnsFilled) {
            console.log('\n❌ DAIRY TABLE ISSUES:');
            dairyColumnMappings.forEach(mapping => {
                const hasValue = mapping.value && mapping.value !== '' && mapping.value !== 'N/A' && mapping.value !== null;
                if (!hasValue) {
                    console.log(`  ❌ ${mapping.column} (${mapping.field}) is empty`);

                    // Suggest form field mapping
                    const formFieldSuggestions = {
                        'lastMilkProduction': 'No form field for milk production - using default 0',
                        'fatPercentage': 'No form field for fat percentage - using default N/A',
                        'artificialInseminationStatus': 'Maps to inseminationStatus radio buttons',
                        'expectedCalfingDate': 'Maps to expectedCalvingDate field',
                        'healthcareNotes': 'Maps to healthCareNotes field only (no vaccination)'
                    };

                    if (formFieldSuggestions[mapping.field]) {
                        console.log(`    💡 ${formFieldSuggestions[mapping.field]}`);
                    }
                }
            });
        }
    }

    return {
        animalData,
        dairyRecord,
        animalColumnMappings,
        dairyColumnMappings,
        allAnimalColumnsFilled,
        allDairyColumnsFilled
    };
};

/**
 * DEBUG FUNCTION - Check how animals appear in animal table
 */
window.checkAnimalTableTypes = function() {
    console.log('🔍 CHECKING ANIMAL TABLE TYPE DISPLAY');
    console.log('====================================');

    // Get all animals from localStorage
    const animals = JSON.parse(localStorage.getItem('animals') || '[]');
    console.log(`📊 Total animals in storage: ${animals.length}`);

    if (animals.length === 0) {
        console.log('❌ No animals found in storage');
        return;
    }

    // Check each animal's type
    console.log('\n📋 ANIMAL TYPE ANALYSIS:');
    animals.forEach((animal, index) => {
        console.log(`${index + 1}. Code: ${animal.code || 'N/A'}`);
        console.log(`   Type: "${animal.type}" (${typeof animal.type})`);
        console.log(`   Source: ${animal.source || 'unknown'}`);
        console.log(`   Gender: ${animal.gender || 'N/A'}`);
        console.log(`   Created: ${animal.createdAt || 'N/A'}`);
        console.log('   ---');
    });

    // Count by type
    const typeCounts = {};
    animals.forEach(animal => {
        const type = animal.type || 'undefined';
        typeCounts[type] = (typeCounts[type] || 0) + 1;
    });

    console.log('\n📊 TYPE DISTRIBUTION:');
    Object.entries(typeCounts).forEach(([type, count]) => {
        console.log(`  ${type}: ${count} animals`);
    });

    // Check specifically for dairy animals from addnewdairy
    const dairyFromAddNew = animals.filter(animal =>
        animal.type === 'dairy' && animal.source === 'addnewdairy_form'
    );

    console.log(`\n🥛 DAIRY ANIMALS FROM ADDNEWDAIRY: ${dairyFromAddNew.length}`);
    dairyFromAddNew.forEach((animal, index) => {
        console.log(`${index + 1}. ${animal.code} - Type: "${animal.type}" - Gender: ${animal.gender}`);
    });

    // Check if animal table would display them correctly
    console.log('\n🔍 ANIMAL TABLE DISPLAY SIMULATION:');
    animals.forEach((animal, index) => {
        const displayType = animal.type || 'N/A';
        const isCorrectDairy = animal.type === 'dairy' && animal.source === 'addnewdairy_form';
        const status = isCorrectDairy ? '✅ CORRECT DAIRY' : (animal.type === 'dairy' ? '🥛 DAIRY' : '🐄 OTHER');
        console.log(`${index + 1}. ${animal.code} → Type Column: "${displayType}" ${status}`);
    });

    return { animals, typeCounts, dairyFromAddNew };
};

/**
 * COMPREHENSIVE TEST - Add dairy animal and verify animal table display
 */
window.testDairyAnimalInAnimalTable = function() {
    console.log('🧪 TESTING DAIRY ANIMAL IN ANIMAL TABLE');
    console.log('=======================================');

    if (!window.addNewDairyController) {
        console.error('❌ Controller not initialized');
        return;
    }

    // Step 1: Create test dairy animal
    console.log('\n📋 STEP 1: Creating Test Dairy Animal');
    const testFormData = {
        code: 'DAIRY_TEST_' + Date.now(),
        birthDate: '2023-01-15',
        herdNumber: 'H999',
        weight: '500',
        weightDate: '2023-12-01',
        artificialInsemination: 'No',
        healthCareNotes: 'Test dairy animal for animal table verification',
        vaccination: 'Test vaccination',
        animalType: 'dairy'
    };

    console.log('Test form data:', testFormData);

    // Step 2: Create and save animal
    console.log('\n💾 STEP 2: Creating and Saving Animal Data');
    const animalData = window.addNewDairyController.createAnimalData(testFormData);
    console.log('Created animal data:', animalData);
    console.log(`✅ Animal type set to: "${animalData.type}"`);
    console.log(`✅ Animal gender set to: "${animalData.gender}"`);
    console.log(`✅ Animal source set to: "${animalData.source}"`);

    // Step 3: Save to storage
    window.addNewDairyController.dataManager.saveAnimal(animalData)
        .then(() => {
            console.log('\n🔍 STEP 3: Verifying Storage');

            // Check animals table
            const animals = JSON.parse(localStorage.getItem('animals') || '[]');
            const savedAnimal = animals.find(a => a.id === animalData.id);

            if (savedAnimal) {
                console.log('✅ Animal found in animals table:');
                console.log(`  Code: ${savedAnimal.code}`);
                console.log(`  Type: "${savedAnimal.type}" (${typeof savedAnimal.type})`);
                console.log(`  Gender: "${savedAnimal.gender}"`);
                console.log(`  Source: "${savedAnimal.source}"`);

                // Step 4: Simulate animal table display
                console.log('\n📊 STEP 4: Animal Table Display Simulation');
                console.log('How this animal would appear in animal table:');
                console.log(`  Code Column: ${savedAnimal.code || 'N/A'}`);
                console.log(`  Type Column: ${savedAnimal.type || 'N/A'}`);
                console.log(`  Herd Number Column: ${savedAnimal.herdNumber || 'N/A'}`);
                console.log(`  Gender Column: ${savedAnimal.gender || 'N/A'}`);
                console.log(`  Weight Column: ${savedAnimal.weight ? savedAnimal.weight + 'kg' : 'N/A'}`);
                console.log(`  Date of Weight Column: ${savedAnimal.dateOfWeight || savedAnimal.dateOfBirth || 'N/A'}`);
                console.log(`  Healthcare Column: ${savedAnimal.healthcareNotes || 'N/A'}`);

                // Step 5: Check if animal table would recognize it as dairy
                const isDairyType = savedAnimal.type === 'dairy';
                console.log(`\n🥛 DAIRY TYPE VERIFICATION: ${isDairyType ? '✅ CORRECT' : '❌ INCORRECT'}`);

                if (isDairyType) {
                    console.log('🎉 SUCCESS: Animal will appear as "dairy" type in animal table!');
                } else {
                    console.log('❌ PROBLEM: Animal type is not "dairy"');
                    console.log(`   Expected: "dairy"`);
                    console.log(`   Actual: "${savedAnimal.type}"`);
                }

                // Step 6: Trigger animal table refresh if possible
                console.log('\n🔄 STEP 5: Triggering Animal Table Refresh');
                if (window.animalPageController && typeof window.animalPageController.refreshAnimalTable === 'function') {
                    window.animalPageController.refreshAnimalTable();
                    console.log('✅ Animal table refresh triggered');
                } else {
                    console.log('⚠️ Animal table controller not found - manual refresh needed');
                    console.log('💡 Go to animal.html to see the updated table');
                }

            } else {
                console.log('❌ Animal not found in animals table!');
            }

            // Final summary
            console.log('\n📋 FINAL SUMMARY:');
            console.log(`Test animal created: ${animalData.code}`);
            console.log(`Type in storage: "${savedAnimal?.type}"`);
            console.log(`Will display as dairy: ${savedAnimal?.type === 'dairy' ? 'YES' : 'NO'}`);

        })
        .catch(error => {
            console.error('❌ Error saving test animal:', error);
        });
};

/**
 * DEBUG FUNCTION - Check for gender case sensitivity issues
 */
window.checkGenderCaseIssue = function() {
    console.log('🔍 CHECKING GENDER CASE SENSITIVITY ISSUE');
    console.log('=========================================');

    // Get all animals from localStorage
    const animals = JSON.parse(localStorage.getItem('animals') || '[]');
    console.log(`📊 Total animals in storage: ${animals.length}`);

    if (animals.length === 0) {
        console.log('❌ No animals found in storage');
        return;
    }

    // Check gender values
    console.log('\n👤 GENDER VALUE ANALYSIS:');
    const genderCounts = {};
    animals.forEach((animal, index) => {
        const gender = animal.gender;
        genderCounts[gender] = (genderCounts[gender] || 0) + 1;

        console.log(`${index + 1}. Code: ${animal.code || 'N/A'}`);
        console.log(`   Gender: "${gender}" (${typeof gender})`);
        console.log(`   Type: "${animal.type}"`);
        console.log(`   Source: ${animal.source || 'unknown'}`);
        console.log('   ---');
    });

    console.log('\n📊 GENDER DISTRIBUTION:');
    Object.entries(genderCounts).forEach(([gender, count]) => {
        console.log(`  "${gender}": ${count} animals`);
    });

    // Check for case sensitivity issues
    const hasUppercaseFemale = genderCounts['Female'] > 0;
    const hasLowercaseFemale = genderCounts['female'] > 0;
    const hasUppercaseMale = genderCounts['Male'] > 0;
    const hasLowercaseMale = genderCounts['male'] > 0;

    console.log('\n⚠️ CASE SENSITIVITY ANALYSIS:');
    console.log(`Uppercase "Female": ${hasUppercaseFemale ? '✅ FOUND' : '❌ NOT FOUND'}`);
    console.log(`Lowercase "female": ${hasLowercaseFemale ? '✅ FOUND' : '❌ NOT FOUND'}`);
    console.log(`Uppercase "Male": ${hasUppercaseMale ? '✅ FOUND' : '❌ NOT FOUND'}`);
    console.log(`Lowercase "male": ${hasLowercaseMale ? '✅ FOUND' : '❌ NOT FOUND'}`);

    if (hasUppercaseFemale && hasLowercaseFemale) {
        console.log('🚨 ISSUE DETECTED: Mixed case for female gender!');
        console.log('   This can cause problems with update system validation');
    }

    if (hasUppercaseMale && hasLowercaseMale) {
        console.log('🚨 ISSUE DETECTED: Mixed case for male gender!');
        console.log('   This can cause problems with update system validation');
    }

    // Check dairy animals specifically
    const dairyAnimals = animals.filter(animal => animal.type === 'dairy');
    console.log(`\n🥛 DAIRY ANIMALS GENDER CHECK (${dairyAnimals.length} total):`);

    dairyAnimals.forEach((animal, index) => {
        const isCorrectGender = animal.gender === 'female';
        const status = isCorrectGender ? '✅ CORRECT' : '❌ INCORRECT';
        console.log(`${index + 1}. ${animal.code} - Gender: "${animal.gender}" ${status}`);

        if (!isCorrectGender) {
            console.log(`   ⚠️ Expected: "female", Got: "${animal.gender}"`);
        }
    });

    // Check update system compatibility
    console.log('\n🔄 UPDATE SYSTEM COMPATIBILITY:');
    const updateSystemExpects = 'female'; // lowercase
    const incompatibleAnimals = dairyAnimals.filter(animal => animal.gender !== updateSystemExpects);

    if (incompatibleAnimals.length > 0) {
        console.log(`❌ ${incompatibleAnimals.length} dairy animals have incompatible gender values:`);
        incompatibleAnimals.forEach(animal => {
            console.log(`   - ${animal.code}: "${animal.gender}" (should be "${updateSystemExpects}")`);
        });
        console.log('\n💡 SOLUTION: These animals need gender value updated to "female" (lowercase)');
    } else {
        console.log('✅ All dairy animals have compatible gender values');
    }

    return {
        animals,
        genderCounts,
        dairyAnimals,
        incompatibleAnimals,
        hasGenderCaseIssue: incompatibleAnimals.length > 0
    };
};
