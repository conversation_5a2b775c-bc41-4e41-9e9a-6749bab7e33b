
/* --------------------------------------------------------------------------------- */
.edit-profile {
  position: relative;
  font-weight: 500;
  font-size: 20px;
}
.content-header,
.navbar-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.navbar-wrapper {
  flex: 1;
  flex-direction: column;
  padding: var(--padding-2xs) 0 0;
  box-sizing: border-box;
  max-width: 100%;
}
.content-header {
  width: 1344px;
  margin: 0 !important;
  position: absolute;
  top: -127px;
  left: -299px;
  flex-direction: row;
  gap: 76px;
  text-align: center;
  font-size: var(--font-size-58xl);
  color: var(--color-floralwhite);
  font-family: var(--font-abeezee);
}
.services-icon1 {
  height: 45px;
  width: 40px;
  position: relative;
  object-fit:contain;
}
.services-parent {
  width:120px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding: 1%;
}
.profile-separator {
  align-self: stretch;
  height: 1px;
  position: relative;
  border-top: 1px solid rgba(0, 0, 0, 0.7);
  box-sizing: border-box;
  width: 80%;
  left:5%;
}
.edit-profile-wrapper {
  position:relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 10px;
  box-sizing: border-box;
  /* max-width: 100%; */
}
.avatar-background {
  position: absolute;
  height:86%;
  top: 12%;
  left: 12.5%;
  border-radius: 50%;
  border: 1px dashed #32cd32;
  box-sizing: border-box;
  width: 57%;
}
.photo-item {
  position: absolute;
  height: 84px;
  top: 14.5px;
  left: 25px;
  /* max-height: 100%;
    z-index: 1;*/
  width: 84px;
  object-fit: cover;
  border-radius: 50%;
}
.camera-icon {
  height: 26px;
  width: 26px;
  position: relative;
  object-fit: cover;
}
.camera-wrapper {
  position: absolute;
  top: 95%;
  left: 35%;
  border-radius:50%;
  background-color: #fff;
  width: 24px;
  height: 24px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.photo2 {
  position: absolute;
  top: 10%;
  left: 42.5%;
  width: 15%;
  height: 84%;
  z-index: 1;
}
.frame-div {
  width: 100%;
  height: 40%;
  position: relative;
  /* max-width: 102%; */
  flex-shrink: 0;
}
.email {
  position: relative;
  font-weight: 200;
}
.data-field-labels {
  height: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--padding-3xs);
  box-sizing: border-box;
}
.layla-hassan {
  width: 100%;
  border: 0;
  outline: 0;
  font-family: var(--highlights);
  font-size: 20px;
  background-color: transparent;
  position: relative;
  color: var(--color-black);
  text-align: left;
  display: inline-block;
  /* min-width: 115px; */
  padding: 0;
}
.data-filled {
  align-self: stretch;
  height: 30%;
  border-radius: 16px;
  background-color: #fff;
  border: 1px solid rgba(11,41,26,0.6);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 8px 28px;
}
.azzamaaexampleocm,
.password-input {
  width: 100%;
  border: 0;
  outline: 0;
  font-size:20px;
  background-color: transparent;
  color: var(--color-black);
  text-align: left;
  display: inline-block;
}
.azzamaaexampleocm {
  font-family: var(--highlights);
  position: relative;
  /* min-width: 213px; */
  padding: 0;
}
.password-input {
  font-family: var(--font-rem);
}
.change-password,
.password-input {
  position: relative;
}
.change-password-wrapper,
.frame-wrapper1 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.change-password-wrapper {
  justify-content: center;
  cursor: pointer;
}
.frame-wrapper1 {
  align-self: stretch;
  height: 55px;
  justify-content: flex-end;
  padding: var(--padding-3xs);
  box-sizing: border-box;
  color: #5cb338;
}

.data-filled-group {
  height: 100%;
  width: 65%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  gap:10%;
  /* max-width: 100%; */
}


.user-data-fields-wrapper {
   display: flex;
  /* align-items: flex-start; */
  box-sizing: border-box;
  width:100%;
  flex-direction: row;
  justify-content: center;
  padding: 1%;
  font-size:20px;
  color: #000;
}
.frame-container {
  /* align-self: stretch; */ 
  width: 100%;
  display: flex;
  /* height: 485px; */
  flex-direction: column;
  justify-content: flex-start;
   padding: 1%;
  /* align-items: flex-start; */
  box-sizing : border-box ;
}
.double-left-icon {
  height: 40px;
  width: 40px;
  position: relative;
  object-fit: cover;
}
.back-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;
}
.save-icon {
  width: 30px;
  position: relative;
  height: 30px;
  object-fit: cover;
}
.save1 {
  position: relative;
  font-size: 22px;
  font-weight: 500;
  font-family: var(--highlights);
  color: var(--color-white);
  text-align: left;
}

.save {
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
  border: 0;
  padding: var(--padding-3xs) 0;
  background-color: var(--color-limegreen-100);
  height: 55px;
  width: 200px;
  box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: var(--br-xl);
  justify-content: center;
  box-sizing: border-box;
  gap:10px;
}
.save:hover {
  background-color: #1ab31a;
}

.navigation-buttons {
  display: flex;
  flex-direction: row;
  align-items: center;
  position: relative;
  top:26%;
  align-self: stretch;
  justify-content: space-between;
  padding: 0 5%;
  /* gap: var(--gap-0); */
}

.frame-group {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
 align-self: stretch;
  justify-content: space-between;
}
.profile-separator-parent {
  height: 100%;
  width:100%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
 
}
.settings21 {
   display: flex;
   flex-direction: column;
    position: absolute;
    width: 75%;
    height: 82%;
    top:11%;
    left: 21%;
    background-color: #fff ;
    border-radius: 16px;
    align-items:space-evenly;
    padding: 2% 1% 1% 1%;
     justify-content: flex-start;
     align-items: flex-start;
  /* gap: 10px; */
}
.content-header-parent,
.edit-inner
 {
  display: flex;
  justify-content: flex-start;
}
.content-header-parent {
  align-self: stretch;
  flex: 1;
  flex-direction: column;
  align-items: center;
  gap: 3px;
  max-width: 100%;
  text-align: left;
  font-size:22px;
  color: var(--color-darkslategray-100);
  font-family: var(--highlights);
}

.edit-inner {
  height: 738px;
  flex: 1;
  flex-direction: column;
   padding: 4px 0 0;
  max-width: calc(100% - 332px);
  align-items: flex-start;
  box-sizing: border-box;
}
.edit1 {

    background-color: #e3e4e4;
    display: flex;
    flex-direction: row;
    justify-content: center;
    /* padding: 2%; */
    width: 100%;
    height: 100vh;
}
/* إضافة media queries للتوافق مع مختلف أحجام الشاشات */
@media screen and (max-width: 1350px) {
  .edit1 {
    flex-wrap: wrap;
  }
}

@media screen and (max-width: 1200px) {
  .frame-container,
  .settings21 {
    padding-top: var(--padding-xl);
    padding-bottom: 75px;
    box-sizing: border-box;
  }
  
  .edit-inner {
    max-width: 100%;
    padding: 4px 20px 0;
  }
  
  .user-data-fields-wrapper {
    width: 100%;
    max-width: 800px;
  }
}

@media screen and (max-width: 1050px) {
  .edit-profile,
  .ingredients,
  .layla-hassan,
  .azzamaaexampleocm {
    font-size: var(--font-size-7xl);
  }
  
  .data-filled-group {
    width: 100%;
    max-width: 570px;
  }
  
  .user-data-fields-wrapper {
    padding: 0 var(--padding-xl);
  }
  
  .frame-div {
    width: 100%;
    max-width: 800px;
  }
}

@media screen and (max-width: 800px) {
  .side-bar {
    width: 250px;
  }
  
  .navbar {
    width: 100%;
  }
  
  .frame-group {
    gap: 15px;
  }
  
  .photo2 {
    position: relative;
    top: 0;
    left: 0;
    margin: 0 auto 20px;
  }
  
  .frame-div {
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .data-filled {
    width: 100%;
  }
  
  .data-filled-group {
    height: auto;
    gap: 20px;
  }
}

@media screen and (max-width: 600px) {
  .edit1 {
    flex-direction: column;
  }
  
  .side-bar {
    width: 100%;
    border-radius: 0;
    margin-bottom: 10px;
  }
  
  .edit-inner {
    max-width: 100%;
    padding: 0 10px;
  }
  
  .navigation-buttons {
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
  }
  
  .save {
    width: 100%;
    max-width: 200px;
  }
  
  .content-header-parent {
    padding: 0 5px;
  }
}

@media screen and (max-width: 450px) {
  .edit-profile,
  .ingredients {
    font-size: var(--font-size-lgi);
  }
  
  .services-parent {
    flex-wrap: wrap;
  }
  
  .azzamaaexampleocm,
  .change-password,
  .email,
  .layla-hassan {
    font-size: var(--font-size-lgi);
  }
  
  .data-filled-group {
    height: auto;
    gap: 15px;
  }
  
  .data-filled {
    padding: var(--padding-5xs) var(--padding-3xs);
    height: 45px;
  }
  
  .frame-container {
    gap: 19px;
  }
  
  .camera-wrapper {
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
  }
  
  .back-left {
    padding-left: 5px;
  }
  
  .save {
    max-width: 150px;
    height: 45px;
  }
  
  .save1 {
    font-size: 18px;
  }
}

@media screen and (max-width: 350px) {
  .data-filled {
    padding: var(--padding-5xs) var(--padding-2xs);
    height: 40px;
  }
  
  .layla-hassan,
  .azzamaaexampleocm,
  .password-input {
    font-size: 16px;
  }
  
  .email,
  .change-password {
    font-size: 16px;
  }
  
  .save {
    max-width: 120px;
    height: 40px;
  }
  
  .save1 {
    font-size: 16px;
  }
  
  .photo2 {
    transform: scale(0.9);
  }
}
/* إعادة تصميم قائمة النافبار المنسدلة */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  z-index: 100;
}

.nav-profile1 {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 140px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 101;
}

.component-13, 
.component-131, 
.component-14 {
  align-self: stretch;
  height: 44px;
  border-radius: var(--br-7xs);
  background-color: var(--color-floralwhite);
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: var(--padding-2xs) var(--padding-3xs);
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.3s;
}

.component-13:hover, 
.component-131:hover, 
.component-14:hover {
  background-color: #f0f0f0;
}

.log-out, 
.log-out1, 
.log-out2 {
  width: auto;
  position: relative;
  font-weight: 500;
  display: inline-block;
}

.popup-overlay a {
  text-decoration: none;
  color: black;
}

/* إضافة مؤشر للعنصر القابل للنقر */
#navProfileContainer {
  cursor: pointer;
}





