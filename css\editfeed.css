@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0%;
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}

.makenewfeed {
   
    background-color: #e3e4e4;
    display: flex;
    flex-direction: row;
    justify-content: center;
    /* padding: 2%; */
    width: 100%;
    height: 100%;
}





 .frame-4 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 90%;
    align-items: center; 
    position: absolute;
    top: 5%;
    
   
}

 .frame-5 {
    display: flex;
    width: 50%;
    height: 15%;
    align-items: flex-start;
    justify-content: flex-start;
    gap: 10px;
    padding: 10px;
    position: relative;
    top: 5px;
    left: 1%;
 
}

.update {
    position: relative;
    width: 30px;
    height: 30px;
    
}
    

 .text-wrapper-5 {
    position: relative;
    width: fit-content;
   
    font-weight:bold;
    color: #0b291a;
    font-size: 20px;
    letter-spacing: var(--highlights-letter-spacing);
    line-height: var(--highlights-line-height);
    white-space: nowrap;
    font-style: var(--highlights-font-style);
}

.frame-7 {
    display: flex;
    align-items: center;
    justify-content: center;
   flex-direction: row;
   
    width: 100%;
    
}



 .text-wrapper-6 {
   width: auto;
    font-family: "Roboto-Medium", Helvetica;
   
    color: #000000;
    font-size: 16px;
    
   
    white-space: nowrap;
   
   
  
}


 .data-filled {
    position: relative;
    width:60%;
    height: 50px;
    background-color:#f1f1f1;
    border-radius: 12px;
   border:hidden;
   left: 2%;
   display: flex;
   justify-content: center;
   align-items: center;
   margin-left: 2%;
}

.data-filled1 {
    position: relative;
 width: 25%;
    height: 50px;
    background-color: #c1d8b9;
    border-radius: 12px;
  border: hidden;
   left: 2%;
  
   display: flex;
   align-items: center;
   justify-content: center;
}



.frame-11 {
    position: relative;
    align-self: stretch;
   background-color:#dfdddd66;
    height: 10px;
   width: 30%;
    font-size: 12px;
    font-weight: 150;
   
    display: flex;
    flex-direction: column;
    
   justify-content:center;
 left:15%
   
}

.raddiv{
    display: inline-flex;
align-items: center;
justify-content: center;
}
.radio{
  
   width: 15px;
   height: 15px;
   background: #e3e4e4;
   
   accent-color: #aedf31;
}


 .frame-6 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 85%;
    align-items: center; 
    position: absolute;
    top: 15%;
}


.textarea{
    position: relative;
    width: 40%;
    height: 90px;
    border-radius: 16px;
    border: 1px solid;
     border-color: #0b291a;
     right: -25%;
}






 .frame-9 {
    display: flex;
    position: relative;
    width: 24%;
    height: 45px;
    align-items: center;
    justify-content: center;
   border: hidden;
    position: relative;
    background-color: #aedf32;
    border-radius: 14px;
   
}

 

   

.btntext1{
    font-size: 25px;
    font-weight: 40px;
    color: #aedf32 ;
    border: none;
    background-color: transparent;
}

.parent1{
    position: absolute;
    width: 70%;
    height: 85%;
    top:10%;
    left: 24%;
    background-color:  #ffffff;
    border-radius: 16px;
    display: flex;
    direction: column;
    
    align-items: flex-start;
    
}


.row{
    width: 95%;
    padding-top: 1%;
    display: flex;
    justify-content: center;
}

/* Table with vertical scrollbar */
.tablecontainer {
    margin-top: 27%;
    width: 100%;
    height: 45%;
    position: absolute;
   
    overflow: hidden; /* Hide overflow on the container itself */
}

table {
    width: 100%;
    border-collapse: collapse;
    position: relative;
}

thead {
    display: table;
    width: calc(100% - 8px); /* Adjust for scrollbar width */
    table-layout: fixed;
    background-color: #ffffff; /* Keep header visible */
}

 

tbody {
    display: block;
    max-height: 200px; /* Set the height you want for the scrollable area */
    overflow-y: auto; /* Enable vertical scrolling */
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: #aedf32 #f1f1f1; /* For Firefox - thumb and track colors */
    width: 100%;
}

/* Styling the scrollbar for WebKit browsers */
tbody::-webkit-scrollbar {
    width: 8px;
}

tbody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

tbody::-webkit-scrollbar-thumb {
    background: #aedf32; /* Match your button color */
    border-radius: 4px;
}

tbody::-webkit-scrollbar-thumb:hover {
    background: #8fb728; /* Darker shade for hover */
}

tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

th, td {
    padding: 8px;
    text-align: center;
    font-size: 16px;
}

td {
    font-size: 16px;
  
}
.checkbox{
   width: 15px;
   height: 15px;
   accent-color: #dfdddd66;
}

.frame-8 {
    
    display: flex;
   justify-content: flex-start;
  align-items: flex-start;
    
   position: absolute;
    width: 75%;
    height: 8%;
    top: 90%;
}



 .double-left1 {
    position: relative;
    width: 30px;
    height: 30px;
    
   
}

 .frame-9 {
    display: flex;
    position: relative;
    width: 40%;
    height: 50px;
    align-items: center;
    justify-content: center;
   border: hidden;
    position: relative;
    background-color: #aedf32;
    border-radius: 12px;
    left: 30%;
   
   
}

.btntext{
    font-size: 25px;
    font-weight: 40px;
    color: #ffffff;
}

.img{
    position: absolute;
    width: 40px;
    height: 40px;
   left: 80%;
   margin-top: 1%;
}

.data-filled11 {
    position: relative;
 width: 25%;
    height: 50px;
   
    border-radius: 12px;
  border: hidden;
   left: 2%;
}

/* Dropdown styles */
.dropdown {
    position: relative;
    display: inline-block;
    width: 100%;
}

.dropbtn {
    width: 85%;
    padding: 8px;
    
    background-color: #f1f1f1;
    cursor: pointer;
    text-align: left;
}

.dropdown-content {
    display: none;
    position: absolute;
    background-color: #aedf31;
    min-width: 160px;
    width: 100%;
    box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
    border-radius: 16px;
}

.dropdown-content a {
    color: #fff;;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
}

.dropdown-content a:hover {
    background-color: #9ec834;
}

.show {
    display: block;
}

/* Notification styles */
#notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.notification {
    padding: 15px 25px;
    margin-bottom: 10px;
    border-radius: 12px;
    color: white;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
}

.notification.success {
    background-color: #4CAF50;
}

.notification.error {
    background-color: #f44336;
}

.notification.fade-out {
    opacity: 0;
}

/* Table styles enhancement */
.tablecontainer table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.tablecontainer th,
.tablecontainer td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.tablecontainer tr:hover {
    background-color: #f5f5f5;
}

/* Non-editable field styles */
.data-filled.gender-display,
.data-filled.animal-type-display,
.data-filled.feed-type-display,
.data-filled.season-display {
    background-color: #f5f5f5;
    padding: 8px 12px;
    border-radius: 12px;
    color: #666;
}
