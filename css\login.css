/* global*/
@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0%;
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}
   


/* style*/
.log-in-media {
    background-color: #e3e4e4;
    display: flex;
    flex-direction: row;
    justify-content: center;
    width: 100%;
    /**/
  
    
    height: 100%;
    /**/
  }
  
 


  .log-in-media .unsplash {
    position: absolute;
    /*
    transform: rotate(-5deg);
    flex-shrink: 0;
    */
    width: 55.24%;
    height: 100%;
    
    
   left: 44.76%;
   /* object-fit:cover ;*/
  }
  

  .writinghalf{
    width: 44.76%;
   align-items: flex-start;
  }
 /*_____________sra upleft___________*/
   .frame {
    display: flex;
    flex-direction: column;
    width: 139px;
    /**/height: 10%;
    align-items: center;
    gap: 8px;
    position: absolute;
    top: 15px;
    left: 29px;
  }
  
  
  .log-in-media .text-wrapper {
    position: relative;
    align-self: stretch;
    height: 70%;
    margin-top: -1.00px;
    font-family: "Fleur De Leah-Regular", Helvetica;
    font-weight: 400;
    color: #0b291a;
    font-size: 54px;
    letter-spacing: 0;
    line-height: normal;
  }
  
  .log-in-media .text-wrapper-2 {
    position: relative;
    width: 60%;
    margin-top: -1.00px;
    font-family: "Aboreto-Regular", Helvetica;
    font-weight: 400;
    color: #0b291a;
    font-size: 8px;
    text-align: center;
    letter-spacing: 0;
    line-height: 9px;
    left: -10%;
  }






  
   .log-in-pge {
    display: flex;
    flex-direction: column;
    width: 44.76%;
   /* */height: 90%;
    align-items: flex-start;
    gap: 10px;
    position: absolute;
    top: 10%;
    left: 2%;
    justify-content: center;
  }
  
  .log-in-media .log-IN {
    display: flex;
    flex-direction: column;
    width: 100%;
   /**/ height: 20%;
    align-items: flex-start;
    gap: 3px;
    padding: 5px;
    position: relative;
  }
  
  .log-in-media .frame-2 {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    position: relative;
    flex: 0 0 auto;
  }
  
  .log-in-media .text-wrapper-3 {
    position: relative;

   /* width: 227px;*/
   

    margin-top: -1.00px;
    font-family: "Roboto-Bold", Helvetica;
    font-weight: 700;
    color: #000000;
    font-size: 40px;
    letter-spacing: 0;
    line-height: normal;
  }
  
  .log-in-media .busts-in-silhouette {
    position: relative;
    width: 45px;
    height: 45px;
  }
  
  .log-in-media .frame-3 {
    display: flex;
    width: 80%;
 

   /* align-items: center;
    justify-content: center;*/
    gap: 10px;
    padding: 10px;
    position: relative;
    flex: 0 0 auto;
   /* margin-bottom: -28.00px;*/
  }
  
  .log-in-media .p {
    position: relative;
    
    margin-top: -1.00px;
    /*margin-left: -5.50px;
    margin-right: -29.50px;*/
     
    font-family: "Roboto-Regular", Helvetica;
    font-weight: 300;
    color: #00000099;
    font-size: 24px;
    letter-spacing: 0;
    line-height: normal;
  }
  
  .log-in-media .frame-4 {
    display: flex;
    flex-direction: column;
    height: 65%;
    align-items: center;
    gap: 15px;
    position: relative;
    /*align-self: stretch;*/
    width: 100%;
  }
  
  .log-in-media .dta-entered {
    display: flex;
    flex-direction: column;
    width: 80%;
    height: 78%;
    align-items: flex-start;
    justify-content: flex-start;
    position: relative;
    top: 6.5%;
    
  }
  
  .log-in-media .frame-5 {
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    width: 90%;
    height: 30%;
    gap: 8px;
    position: relative;
    flex: 0 0 auto;
  }
  .mainemail{
    display: inline-flex;
    flex-direction: column;
    align-items: flex-start;
    width: 90%;
    height: 30%;
    gap: 8px;
    position: relative;
    flex: 0 0 auto;
    
  }
  
  .log-in-media .email {
    display: flex;
    width: 100%;
    height: 30%;
    align-items: center;
    gap: 10px;
    position: relative;
  }
  
  .log-in-media .img {
    position: relative;
    width: 40px;
    height: 40px;
  }
  
  .log-in-media .input {
    position: relative;
    width: fit-content;
    font-family: "Albert Sans-Medium", Helvetica;
    font-weight: 500;
    color: #000000;
    font-size: 24px;
    letter-spacing: 0;
    line-height: normal;
    background: transparent;
    border: none;
    padding: 0;
  }
  
  .log-in-media .frame-6 {
    display: flex;
    width: 75%;
    height: 50%;
    align-items: center;
    gap: 4px;
    padding: 12px 80px 12px 16px;
    position: relative;
    background-color: #f5f5f5;
    border-radius: 12px;
    border: 1px solid;
    border-color: #1212121f;
  }
  
/*  .log-in-media .text-wrapper-4 {
    position: relative;
    width: 100%;
    height: 100%;
    font-family: "Roboto-Medium", Helvetica;
    font-weight: 500;
    color: var(--gray);
    font-size: 15px;
    letter-spacing: 0;
    line-height: 30px;
    white-space: nowrap;
  }*/
  
  .log-in-media .password {
    display: inline-flex;
    height: 44px;
    align-items: center;
    gap: 10px;
    position: relative;
  }
  
  .log-in-media .text-wrapper-5 {
    position: relative;
    width: fit-content;
    font-family: "Albert Sans-Medium", Helvetica;
    font-weight: 500;
    color: #000000;
    font-size: 24px;
    letter-spacing: 0;
    line-height: normal;
  }
  
  .log-in-media .password-2 {
    display: flex;
    width: 75%;
    height: 53%;
    align-items: center;
    gap: 4px;
   
    position: relative;
    background-color: #f5f5f5;
    border-radius: 12px;
    border: 1px solid;
    border-color: #1212121f;
  }
  
  .log-in-media .frame-7 {
    display: inline-flex;
    align-items: flex-start;
    gap: 14px;
    position: relative;
    flex: 0 0 auto;
    margin-top: -9.50px;
    margin-bottom: -9.50px;
  }
  
   .text-wrapper-6 {
    display: flex;
    width: 100%;
    height: 100%;
    align-items: center;
    gap: 4px;
    /*padding: 12px 80px 12px 16px;*/
    position: relative;
    background-color: #f5f5f5;
    border-radius: 12px;
    border: 1px solid;
    border-color: #1212121f;
  }
  
  .log-in-media .eye {
    position: relative;
    
    width: 20px;
    height: 20px;
  }
  
  .log-in-media .frame-8 {
    display: flex;
    width: 90%;
    height: 15%;
    align-items: center;
    justify-content:start;
    gap: 28%;
    position: relative;
  }
  
  .log-in-media .remember-me {
    display: inline-flex;
    align-items: flex-end;
    position: relative;
    flex: 0 0 auto;
    
  }
  
  .log-in-media .select-box {
    position: relative;
    width: 10%;
    height: 10%;
  }
  .checkbox{
    width: 150%;
    height: 150%;
  }
  
  .log-in-media .frame-9 {
    display: inline-flex;
    width: 50%;
    height: 20px;
    align-items: center;
    justify-content: center;
    
    padding: 10px;
    position: relative;
    flex: 0 0 auto;
  }
  
  .log-in-media .text-wrapper-7 {
    position: relative;
    width: 85%;
    margin-top: -5.50px;
    margin-bottom: -3.50px;
    font-family: "Roboto-Medium", Helvetica;
    font-weight: 500;
    color: #707070;
    font-size: 14px;
    letter-spacing: 0;
    line-height: 9px;
    text-wrap: nowrap;
  }
  
  .log-in-media .forget-password {
    display: flex;
    flex-direction: column;
    width: 112px;
    align-items: flex-start;
    justify-content: center;
    gap: 4px;
    padding: 10px;
    position: relative;
  }
  
  .log-in-media .text-wrapper-8 {
    position: relative;
    width: fit-content;
    margin-top: -1.00px;
    margin-right: -8.00px;
    font-family: "Roboto-Medium", Helvetica;
    font-weight: 500;
    color: #717171cc;
    font-size: 12px;
    letter-spacing: 0;
    line-height: normal;
    white-space: nowrap;
  }
  
 /* .log-in-media .line {
    position: relative;
    width: 100px;
    height: 1px;
    margin-right: -8.00px;
    object-fit: cover;
  }*/
  
  .log-in-media .cancel-buttom {
    display: flex;
    width:54%;
    height: 12%;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 0px;
    position: relative;
    background-color: #32cd32;
    border-radius: 12px;
    left: -13%;
    top:-15%;

    
  }
  
  
   .cancel {
    position: relative;
    width: fit-content;
    font-family: "Roboto-Medium", Helvetica;
    font-weight: 500;
    color: #ffffff;
    font-size: 24px;
    text-align: center;
    border: none;
    letter-spacing: 0;
    line-height: normal;
    white-space: nowrap;
    width: 100%;
    height: 100%;
    background-color:transparent;
  }

  /*guide*/ 
  :root {
    --gray: rgba(179, 179, 179, 1);
    --highlights-font-family: "Roboto", Helvetica;
    --highlights-font-weight: 500;
    --highlights-font-size: 32px;
    --highlights-letter-spacing: 0px;
    --highlights-line-height: normal;
    --highlights-font-style: normal;
    --bottom-font-family: "Roboto", Helvetica;
    --bottom-font-weight: 500;
    --bottom-font-size: 24px;
    --bottom-letter-spacing: 0px;
    --bottom-line-height: normal;
    --bottom-font-style: normal;
    --topic-chart-behind-topic-font-family: "Roboto", Helvetica;
    --topic-chart-behind-topic-font-weight: 400;
    --topic-chart-behind-topic-font-size: 36px;
    --topic-chart-behind-topic-letter-spacing: 0px;
    --topic-chart-behind-topic-line-height: normal;
    --topic-chart-behind-topic-font-style: normal;
    --content-table-font-family: "Roboto", Helvetica;
    --content-table-font-weight: 500;
    --content-table-font-size: 22px;
    --content-table-letter-spacing: 0px;
    --content-table-line-height: normal;
    --content-table-font-style: normal;
    --para-20-roboto-font-family: "Roboto", Helvetica;
    --para-20-roboto-font-weight: 400;
    --para-20-roboto-font-size: 20px;
    --para-20-roboto-letter-spacing: 0px;
    --para-20-roboto-line-height: 55px;
    --para-20-roboto-font-style: normal;
  }
  
  