<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1, width=device-width" />

  <link rel="stylesheet" href="css/general.css" />
  <link rel="stylesheet" href="css/main.css" />
  <link rel="stylesheet" href="css/navigation-components.css" />

  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=ABeeZee:wght@400&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Source Code Pro:wght@400&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@200;300;400;500&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto Serif:wght@400&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=REM:wght@400&display=swap" />
</head>

<body>
  <div class="dashboard">
    <div class="side-bar">
      <div class="s-r-a-parent-wrapper">
        <div class="sra-parent">
          <h1 class="sra">SRA</h1>
          <div class="smart-raising-animal-wrapper">
            <div class="smart-raising-animal">Smart Raising Animal</div>
          </div>
        </div>
      </div>
      <div class="side-bar-bottom">
        <div class="side-bar-bottom-inner">
          <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
        </div>
        <div class="side-bar-elements">
          <div class="side-bar-options">
            <div class="side-bar-option-parent">
              <div class="vuesaxlinearcategory-2-parent">
                <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt=""
                  src="./public/vuesaxlinearcategory21.svg" />
                <a class="animals" href="index.html">Dashboard</a>
              </div>
            </div>
            <div class="side-bar-option-parent-inner">
              <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
            </div>
            <div class="side-bar-option-parent1">
              <div class="side-bar-element">
                <div class="side-animal">
                  <img class="bull-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                  <div class="animals-wrapper">
                    <a class="animals" href="animal.html">Animals</a>
                  </div>
                </div>
                <div class="side-animal">
                  <img class="bull-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                  <a class="animals" href="dairy.html">Dairy</a>
                </div>
                <div class="side-animal">
                  <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                  <a class="animals" href="newborn.html">New Born</a>
                </div>
                <div class="side-animal">
                  <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                  <a class="animals" href="feed.html">Feed</a>
                </div>
                <div class="side-animal">
                  <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                  <a class="animals" href="ingrediants.html">Ingredients</a>
                </div>
                <div class="side-animal">
                  <div class="side-animal">
                    <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="vaccination.html">Vaccination</a>
                  </div>
                </div>
                <div class="side-animal">
                  <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                  <a class="animals" href="reports.html">Reports</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <section class="navbar-and-main">
        <header class="navbar">
          <img class="notification-bell-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
          <div class="nav-profile" id="navProfileContainer">
            <img class="male-avatar-portrait-of-a-youn-icon" loading="lazy" alt=""
              src="./public/<EMAIL>" />
            <div class="side-pregnant">
              <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt="" src="./public/vuesaxlineararrowdown.svg" />
            </div>
          </div>
        </header>

        <div class="content-info">
          <div class="content-metrics">
            <div class="num-of-animal">
              <div class="animals-number-label-parent">
                <h1 class="quartiers">Animal’s Number</h1>
              </div>
              <div class="animal-chart">
                <div class="piechart">
                  <div class="barblock">
                    <div class="pie-layer-parent">
                      <img class="pielayer-icon" loading="lazy" alt="" src="./public/pielayer.svg" />

                      <div class="div">470</div>
                    </div>
                    <div class="configanddata-pie-dont-remove"></div>
                  </div>
                  <div class="legends">
                    <div class="legend">
                      <img class="legendnode-icon" loading="lazy" alt="" src="./public/legendnode.svg" />

                      <div class="pregnant">Pregnant</div>
                    </div>
                    <div class="legend">
                      <img class="legendnode-icon" loading="lazy" alt="" src="./public/legendnode-1.svg" />

                      <div class="pregnant">New born</div>
                    </div>
                    <div class="legend">
                      <img class="legendnode-icon" alt="" src="./public/legendnode-2.svg" />

                      <div class="pregnant">Fattening</div>
                    </div>
                    <div class="legend">
                      <img class="legendnode-icon" alt="" src="./public/legendnode-3.svg" />

                      <div class="pregnant">Milch</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="quarters">
              <div class="quarties">
                <div class="quartiers-milk-production-wrapper">
                  <h1 class="quartiers-milk-production">
                    4 Quartiers Milk Production
                  </h1>
                </div>
                <div class="quarters-chart">
                  <div class="quarters-chart-parent">
                    <div class="quarters-chart-bars">
                      <div class="quarters-names-parent">
                        <h1 class="quartiers">Quartiers</h1>
                      </div>
                      <div class="quarties-inner">
                        <div class="summer-parent">
                          <h1 class="summer ">
                            1. Summer
                            <!-- <ol class="summer1">
                                <li>Summer</li>
                              </ol> -->
                          </h1>
                          <h1 class="summer">
                            2. Autumn
                          </h1>
                          <h1 class="summer">
                            3. Winter
                          </h1>
                          <h1 class="summer">
                            4. Spring
                          </h1>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="milk-production-chart">
                    <h1 class="quartiers">Milk production</h1>
                    <div class="milk-production-values-parent">
                      <div class="ton-parent">
                        <div class="sun">100 Ton</div>
                        <div class="sun">140 Ton</div>
                        <div class="sun">180 Ton</div>
                        <div class="sun">120 Ton</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="weight1">
          <div class="weight-chart">
            <div class="weight">
              <div class="weight-per-week-wrapper">
                <h1 class="quartiers">Weight per week</h1>
              </div>
              <div class="barlinechart">
                <div class="chartaxis">
                  <div class="mainchart1">
                    <div class="yaxisleft">
                      <div class="pregnant">20</div>
                      <div class="pregnant">15</div>
                      <div class="pregnant">10</div>
                      <div class="pregnant">5</div>
                      <div class="pregnant">0</div>
                    </div>
                    <div class="graphigrid">
                      <div class="xlines">
                        <div class="line"></div>
                        <div class="line"></div>
                        <div class="line"></div>
                        <div class="line"></div>
                        <div class="line4"></div>
                      </div>
                      <div class="ylines">
                        <div class="line5"></div>
                        <div class="line5"></div>
                        <div class="line5"></div>
                        <div class="line5"></div>
                        <div class="line5"></div>
                        <div class="line5"></div>
                        <div class="line5"></div>
                        <div class="line5"></div>
                      </div>
                      <div class="bararea">
                        <div class="bargroup">
                          <div class="mainchart">
                            <div class="singlebar">
                              <div class="barstrip"></div>
                            </div>
                          </div>
                        </div>
                        <div class="bargroup1">
                          <div class="mainchart">
                            <div class="singlebar1">
                              <div class="barstrip1"></div>
                            </div>
                          </div>
                        </div>
                        <div class="bargroup2">
                          <div class="mainchart">
                            <div class="singlebar2">
                              <div class="barstrip2"></div>
                            </div>
                          </div>
                        </div>
                        <div class="bargroup3">
                          <div class="mainchart">
                            <div class="singlebar3">
                              <div class="barstrip3"></div>
                            </div>
                          </div>
                        </div>
                        <div class="bargroup4">
                          <div class="mainchart">
                            <!-- <div class="line10"></div> -->
                            <div class="singlebar4">
                              <div class="barstrip2"></div>
                            </div>
                          </div>
                        </div>
                        <div class="bargroup5">
                          <div class="mainchart">
                            <!-- <div class="line11"></div> -->
                            <div class="singlebar5">
                              <div class="barstrip5"></div>
                            </div>
                          </div>
                        </div>
                        <div class="bargroup6">
                          <div class="mainchart">
                            <!-- <div class="line12"></div> -->
                            <div class="singlebar6">
                              <div class="barstrip6"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="configanddata-bar-dont-remove"></div>
                    </div>
                  </div>
                  <div class="xaxis">
                    <div class="xlabelbox">
                      <div class="ton">Sun</div>
                    </div>
                    <div class="xlabelbox">
                      <div class="ton">Mon</div>
                    </div>
                    <div class="xlabelbox">
                      <div class="ton">Tue</div>
                    </div>
                    <div class="xlabelbox3">
                      <div class="ton">Wed</div>
                    </div>
                    <div class="xlabelbox3">
                      <div class="ton">Thu</div>
                    </div>
                    <div class="xlabelbox">
                      <div class="ton">Fri</div>
                    </div>
                    <div class="xlabelbox">
                      <div class="ton">Sat</div>
                    </div>
                  </div>
                </div>
                <div class="legends1">
                  <div class="filllegends">
                    <div class="legend">
                      <div class="legendnode">
                        <div class="basicnode">
                          <div class="squarefill"></div>
                        </div>
                      </div>
                      <div class="pregnant">Weight</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="schedule-container">
              <div class="schedule-wrapper">
                <div class="upcomingschedules">
                  <div class="schedule-title-area">
                    <div class="schedule-header">
                      <h1 class="quartiers">Up Coming Schedules</h1>
                    </div>
                    <div class="separator-wrapper">
                      <div class="upcomingschedules-item"></div>
                    </div>
                    <div class="schedule-list">
                      <div class="time-container-parent">
                        <div class="time-container">
                          <div class="am1">9: 00 AM</div>
                        </div>
                        <div class="description-area">
                          <div class="upcomingschedules-inner">
                            <div class="frame-child"></div>
                          </div>
                        </div>
                        <div class="animals-new-born-have-fortific-wrapper">
                          <h2 class="animals-new-born">
                            Animals New born have fortification
                          </h2>
                        </div>
                      </div>
                      <div class="time-parent">
                        <div class="time">
                          <div class="am1">10:00 AM</div>
                        </div>
                        <div class="morning-feed-separator">
                          <div class="upcomingschedules-inner">
                            <div class="frame-child"></div>
                          </div>
                        </div>
                        <h2 class="health-care-check">
                          Animals feed in the morning
                        </h2>
                      </div>
                    </div>
                  </div>
                  <div class="time-list-parent">
                    <div class="time-list">
                      <div class="am1">11: 00 AM</div>
                      <div class="am1">12: 00 AM</div>
                      <div class="am1">1: 00 PM</div>
                      <div class="am1">2: 00 PM</div>
                    </div>
                    <div class="schedule-content">
                      <div class="vertical-separator-area">
                        <div class="upcomingschedules-inner1">
                          <div class="frame-child"></div>
                        </div>
                        <div class="separator-container">
                          <div class="upcomingschedules-inner1">
                            <div class="frame-child"></div>
                          </div>
                          <div class="separator-container-child">
                            <div class="line-parent">
                              <div class="upcomingschedules-child1"></div>
                              <div class="upcomingschedules-child2"></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="task-wrapper-wrapper">
                      <div class="task-wrapper">
                        <div class="schedule-header">
                          <div class="task-title">
                            <h2 class="health-care-check">
                              Health care check
                            </h2>
                            <h2 class="check-feed-s">
                              Check pregnant ‘s Date of give birth
                            </h2>
                          </div>
                        </div>
                        <div class="feed-check-parent">
                          <div class="feed-check">
                            <h2 class="check-feed-s">
                              Check Feed ‘s ingredients
                            </h2>
                          </div>
                          <h2 class="health-care-check">
                            Animals Fattening weight bigger than 450 KG sale
                            them better
                          </h2>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="milk-graph-wrapper">
                  <div class="milk-graph">
                    <div class="graph-header">
                      <h1 class="milk-production2">Milk production</h1>
                    </div>
                    <div class="graph">
                      <div class="chartaxis1">
                        <div class="mainchart2">
                          <div class="yaxisleft">
                            <div class="pregnant">1500</div>
                            <div class="pregnant">1200</div>
                            <div class="pregnant">900</div>
                            <div class="pregnant">600</div>
                            <div class="pregnant">300</div>
                            <div class="pregnant">0</div>
                          </div>
                          <div class="graphigrid1">
                            <div class="xlines1">
                              <div class="line13"></div>
                              <div class="line13"></div>
                              <div class="line13"></div>
                              <div class="line13"></div>
                              <div class="line13"></div>
                              <div class="line6"></div>
                            </div>
                            <div class="ylines1">
                              <div class="line17"></div>
                              <div class="line18"></div>
                              <div class="line19"></div>
                              <div class="line20"></div>
                              <div class="line21"></div>
                              <div class="line21"></div>
                              <div class="line21"></div>
                              <div class="line21"></div>
                            </div>
                            <div class="linearea">
                              <div class="linegroup0">
                                <!-- <div class="production-data"></div>
                                  <div class="production-data1"></div> -->
                                <div class="wrapper-singleline0">
                                  <img class="singleline0-icon" alt="" src="./public/singleline0.svg" />
                                </div>
                                <div class="linenodes0">
                                  <div class="linenodes0-inner">
                                    <div class="frame-parent">
                                      <div class="frame-group">
                                        <div class="datalabel-wrapper">
                                          <div class="datalabel">
                                            <div class="basicnode-wrapper">
                                              <img class="basicnode-icon2" loading="lazy" alt=""
                                                src="./public/basicnode.svg" />
                                            </div>
                                            <div class="div12">1124</div>
                                          </div>
                                        </div>
                                        <div class="datalabel-parent">
                                          <div class="datalabel3">
                                            <div class="basicnode-container">
                                              <img class="basicnode-icon2" loading="lazy" alt=""
                                                src="./public/basicnode-1.svg" />
                                            </div>
                                            <div class="div14">1073</div>
                                          </div>
                                          <div class="datalabel-container">
                                            <div class="datalabel4">
                                              <div class="basicnode-frame">
                                                <img class="basicnode-icon2" loading="lazy" alt=""
                                                  src="./public/basicnode-2.svg" />
                                              </div>
                                              <div class="div12">1189</div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                      <div class="frame-wrapper">
                                        <div class="datalabel-group">
                                          <div class="datalabel1">
                                            <div class="vertical-separator-area-inner">
                                              <img class="basicnode-icon2" loading="lazy" alt=""
                                                src="./public/basicnode-3.svg" />
                                            </div>
                                            <div class="div13">1300</div>
                                          </div>
                                          <div class="datalabel-frame">
                                            <div class="datalabel3">
                                              <div class="basicnode-container">
                                                <img class="basicnode-icon2" loading="lazy" alt=""
                                                  src="./public/basicnode-4.svg" />
                                              </div>
                                              <div class="div13">1356</div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="data-points">
                                    <div class="point-pair">
                                      <div class="datalabel5">
                                        <div class="basicnode-container">
                                          <img class="basicnode-icon2" loading="lazy" alt=""
                                            src="./public/basicnode-5.svg" />
                                        </div>
                                        <div class="div13">1245</div>
                                      </div>
                                    </div>
                                    <div class="datalabel6">
                                      <div class="basicnode-container">
                                        <img class="basicnode-icon2" loading="lazy" alt=""
                                          src="./public/basicnode-6.svg" />
                                      </div>
                                      <div class="div14">1037</div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div class="configanddata-line-dont-remov"></div>
                          </div>
                        </div>
                        <div class="xaxis1">
                          <div class="xlabelbox7">
                            <div class="ton">Sun</div>
                          </div>
                          <div class="xlabelbox7">
                            <div class="ton">Mon</div>
                          </div>
                          <div class="xlabelbox7">
                            <div class="ton">Tue</div>
                          </div>
                          <div class="xlabelbox7">
                            <div class="ton">Wed</div>
                          </div>
                          <div class="xlabelbox7">
                            <div class="ton">Thu</div>
                          </div>
                          <div class="xlabelbox7">
                            <div class="ton">Fri</div>
                          </div>
                          <div class="xlabelbox7">
                            <div class="ton">Sat</div>
                          </div>
                        </div>
                      </div>
                      <div class="legends2">
                        <div class="filllegends">
                          <div class="legend5">
                            <div class="legendnode-icon">
                              <div class="line27"></div>
                              <img class="basicnode-icon7" loading="lazy" alt="" src="./public/basicnode-7.svg" />
                            </div>
                            <div class="pregnant">
                              <span>Milk</span>
                              <span class="span"> </span>
                              <span>Production</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>

  <div id="navProfilePopup" class="popup-overlay" style="display: none">
    <div class="nav-profile1">
      <div class="component-13">
        <a class="log-out" href="logout.html">Log Out</a>
      </div>
      <div class="component-131">
        <a class="log-out1" href="setting.html">Settings</a>
      </div>
      <div class="component-14">
        <a class="log-out2" href="about-us.html">About</a>
      </div>
    </div>
  </div>

  <script src="js/api-client.js"></script>
  <script src="js/navigation-components.js"></script>
  <script src="js/main.js"></script>
</body>

</html>
