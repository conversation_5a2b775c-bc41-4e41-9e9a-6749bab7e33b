<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Search Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 800px;
            margin: 0 auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Animal Search Functionality Test</h1>
        
        <h2>Test Controls</h2>
        <button class="test-button" onclick="testSearchButton()">Test Search Button</button>
        <button class="test-button" onclick="testSearchFunctions()">Test Search Functions</button>
        <button class="test-button" onclick="testAnimalController()">Test Animal Controller</button>
        <button class="test-button" onclick="createTestData()">Create Test Data</button>
        <button class="test-button" onclick="clearResults()">Clear Results</button>
        
        <h2>Test Results</h2>
        <div id="testResults"></div>
        
        <h2>Console Output</h2>
        <div id="consoleOutput" style="background: #000; color: #0f0; padding: 10px; border-radius: 4px; font-family: monospace; height: 200px; overflow-y: auto;"></div>
    </div>

    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToConsole(message, type = 'log') {
            const consoleDiv = document.getElementById('consoleOutput');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#f00' : type === 'warn' ? '#ff0' : '#0f0';
            consoleDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToConsole(args.join(' '), 'warn');
        };
        
        function addResult(message, isError = false) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isError ? 'error' : 'success'}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('consoleOutput').innerHTML = '';
        }
        
        function testSearchButton() {
            console.log('🔍 Testing search button...');
            
            try {
                // Check if search button exists
                const searchButton = document.querySelector('#searchButton') || 
                                   document.querySelector('.search') ||
                                   document.querySelector('button[id*="search"]');
                
                if (searchButton) {
                    addResult(`✅ Search button found: ${searchButton.id || searchButton.className}`);
                    console.log('Search button element:', searchButton);
                    
                    // Try to click it
                    searchButton.click();
                    addResult('✅ Search button clicked successfully');
                } else {
                    addResult('❌ Search button not found', true);
                }
            } catch (error) {
                addResult(`❌ Error testing search button: ${error.message}`, true);
                console.error('Search button test error:', error);
            }
        }
        
        function testSearchFunctions() {
            console.log('🔍 Testing search functions...');
            
            try {
                // Test global search functions
                if (typeof window.performAnimalSearch === 'function') {
                    addResult('✅ performAnimalSearch function exists');
                    console.log('Testing performAnimalSearch...');
                    window.performAnimalSearch('code');
                } else {
                    addResult('❌ performAnimalSearch function not found', true);
                }
                
                if (typeof window.cancelAnimalSearch === 'function') {
                    addResult('✅ cancelAnimalSearch function exists');
                    console.log('Testing cancelAnimalSearch...');
                    window.cancelAnimalSearch();
                } else {
                    addResult('❌ cancelAnimalSearch function not found', true);
                }
                
                if (typeof window.searchAnimals === 'function') {
                    addResult('✅ searchAnimals function exists');
                    console.log('Testing searchAnimals...');
                    const results = window.searchAnimals('test');
                    addResult(`✅ searchAnimals returned ${results.length} results`);
                } else {
                    addResult('❌ searchAnimals function not found', true);
                }
            } catch (error) {
                addResult(`❌ Error testing search functions: ${error.message}`, true);
                console.error('Search functions test error:', error);
            }
        }
        
        function testAnimalController() {
            console.log('🔍 Testing animal controller...');
            
            try {
                if (window.animalPageController) {
                    addResult('✅ animalPageController exists');
                    console.log('Animal controller:', window.animalPageController);
                    
                    if (window.animalPageController.searchDropdownManager) {
                        addResult('✅ searchDropdownManager exists');
                    } else {
                        addResult('❌ searchDropdownManager not found', true);
                    }
                    
                    // Test statistics
                    const stats = window.animalPageController.getPageStatistics();
                    addResult(`✅ Page statistics: ${JSON.stringify(stats)}`);
                } else {
                    addResult('❌ animalPageController not found', true);
                }
            } catch (error) {
                addResult(`❌ Error testing animal controller: ${error.message}`, true);
                console.error('Animal controller test error:', error);
            }
        }
        
        function createTestData() {
            console.log('🔍 Creating test data...');
            
            try {
                if (window.createSampleAnimals) {
                    window.createSampleAnimals();
                    addResult('✅ Test data created successfully');
                } else {
                    // Create test data manually
                    const testAnimals = [
                        {
                            code: 'A001',
                            type: 'dairy',
                            herdNumber: 'H001',
                            gender: 'female',
                            weight: 450,
                            dateOfWeight: '2024-01-15',
                            healthcareNotes: 'Healthy'
                        },
                        {
                            code: 'A002',
                            type: 'newborn',
                            herdNumber: 'H002',
                            gender: 'male',
                            weight: 35,
                            dateOfWeight: '2024-01-20',
                            healthcareNotes: 'Good condition'
                        }
                    ];
                    
                    localStorage.setItem('animals', JSON.stringify(testAnimals));
                    addResult('✅ Manual test data created');
                }
                
                // Refresh table if possible
                if (window.animalPageController && window.animalPageController.refreshAnimalTable) {
                    window.animalPageController.refreshAnimalTable();
                    addResult('✅ Animal table refreshed');
                }
            } catch (error) {
                addResult(`❌ Error creating test data: ${error.message}`, true);
                console.error('Test data creation error:', error);
            }
        }
        
        // Load the animal page scripts
        function loadAnimalScripts() {
            console.log('📜 Loading animal page scripts...');
            
            const scripts = [
                'js/animal.js',
                'js/navigation-components.js'
            ];
            
            scripts.forEach(src => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => console.log(`✅ Loaded: ${src}`);
                script.onerror = () => console.error(`❌ Failed to load: ${src}`);
                document.head.appendChild(script);
            });
        }
        
        // Auto-load scripts when page loads
        window.addEventListener('load', () => {
            console.log('🚀 Test page loaded');
            loadAnimalScripts();
        });
    </script>
</body>
</html>
