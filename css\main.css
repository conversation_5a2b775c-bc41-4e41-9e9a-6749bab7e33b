/* -------------- */
.quartiers {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
}
.animals-number-label-parent {
  font-size: 25px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-10xs);
}
.pielayer-icon {
  height: 90%;
  width: 90%;
  position: absolute;
  margin: 0 !important;
  /* top: 0; */
  right:27px;
  bottom: 8;
  left: 0;
  max-width: 100%;
  overflow: hidden;
  max-height: 100%;
}
.div {
  width: 46px;
  position: relative;
  font-weight: 600;
  display: inline-block;
  z-index: 1;
  top: 88px;
  left: 71px;
}
.pie-layer-parent {
  width: 200px;
  height: 200px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  /* padding: 123px var(--padding-71xl); */
  position: relative;
}
.configanddata-pie-dont-remove {
  width: 8px;
  position: relative;
  height: 8px;
  display: none;
}
.barblock {
  align-self: stretch;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  /* padding: var(--padding-5xs); */
}
.legendnode-icon {
  height: 16px;
  width: 16px;
}
.legendnode-icon,
.pregnant {
  position: relative;
}
.legend,
.legends {
  overflow: hidden;
  display: flex;
}
.legend {
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: var(--padding-9xs);
  gap: var(--gap-9xs);
}
.legends {
  width: 138px;
  height: 207px;
  align-self: stretch;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
}
.piechart {
  margin-top: -4px;
  flex: 1;
  background-color: var(--color-white);
  flex-direction: row;
  /* padding: var(--padding-5xl) var(--padding-5xs) var(--padding-5xs);
  gap: var(--gap-5xs); */
}
.animal-chart,
.num-of-animal,
.piechart {
  box-sizing: border-box;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  max-width: 100%;
}
.animal-chart {
  align-self: stretch;
  flex-direction: row;
  padding: 0;
  font-size: var(--font-size-5xl);
  color: var(--color-gray-400);
  font-family: var(--font-inter);
}
.num-of-animal {
  height: 215px;
  border-radius: var(--br-base);
  background-color: var(--color-white);
  border: 1px solid var(--color-lightgray);
  overflow: hidden;
  flex-direction: column;
  /* padding: var(--padding-6xs) var(--padding-xs) var(--padding-19xl); */
  padding: 2px 12px 0px;
  min-width: 379px;
}
/* ------- -----------------------------------------------------------------------------*/
.quartiers-milk-production {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
  /* white-space: pre-wrap; */
}
.quartiers-milk-production-wrapper {
  position: absolute;
  /* top: 14px; */
  left: 41px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  /* padding: var(--padding-3xs); */
}
.quarters-names-parent {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-3xs);
}
.summer,
.summer1 {
  margin: 0;
  font-size: inherit;
  font-family: inherit;
}

.summer {
  align-self: stretch;
  position: relative;
  font-weight: 400;
  padding-left: 16px;
}
.summer-parent {
  width: 135px;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  /* gap: var(--gap-5xl); */
  gap:11px;
}
.quarties-inner {
  flex: 1;
  z-index: 1;
  /* font-size: var(--highlights-size); */
  font-size: 19px;
}
.quarters-chart-bars,
.quarters-chart-parent,
.quarties-inner {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.quarters-chart-bars {
  flex: 1;
  /* gap: var(--gap-mid); */
  gap: 3px;
}
.quarters-chart-parent {
  /* padding: var(--padding-9xs) 0 0; */
}
.sun {
  align-self: stretch;
  position: relative;
}
.ton-parent {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  /* gap: var(--gap-5xl); */
  gap: 11px;
}
.milk-production-values-parent {
  align-self: stretch;
  flex-direction: row;
  padding: 0 60px 0 59px;
  /* font-size: var(--highlights-size); */
  font-size: 19px;
}
.milk-production-chart,
.milk-production-values-parent,
.quarters-chart {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.milk-production-chart {
  flex: 1;
  flex-direction: column;
  /* gap: var(--gap-mid); */
  gap: 5px;
  min-width: 162px;
}
.quarters-chart {
  position: absolute;
  top: 45px;
  left: 52px;
  width: 498px;
  flex-direction: row;
  gap: 59px;
  max-width: 100%;
}
.quarties {
  align-self: stretch;
  height: 210px;
  position: relative;
  border-radius: var(--br-base);
  background-color: var(--color-darkslategray-100);
  border: 1px solid var(--color-lightgray);
  box-sizing: border-box;
  overflow: hidden;
  flex-shrink: 0;
  max-width: 100%;
}
/* ------------------------------------------------------------------------------------- */
.content-info,
.content-metrics,
.quarters {
  display: flex;
  align-items: flex-start;
  max-width: 100%;
}
.quarters {
  flex: 1;
  flex-direction: column;
  justify-content: flex-start;
  /* padding: var(--padding-9xs) 0 0; */
  box-sizing: border-box;
  min-width: 416px;
  color: var(--color-white);
}
.content-info,
.content-metrics {
  flex-direction: row;
}
.content-metrics {
  flex: 1;
  justify-content: flex-start;
  gap: 29px;
}
.content-info {
  height: 213px;
  align-self: stretch;
  justify-content: flex-end;
  padding: 0 var(--padding-3xs) 0 var(--padding-smi);
  box-sizing: border-box;
}
.weight-per-week-wrapper,
.yaxisleft {
  align-self: stretch;
  display: flex;
  justify-content: space-between;
}
.weight-per-week-wrapper {
  flex-direction: row;
  align-items: center;
  padding: 0 var(--padding-13xl);
  gap: var(--gap-xl);
  z-index: 2;
}
.yaxisleft {
  flex-direction: column;
  align-items: flex-end;
  padding: 0 var(--padding-9xs);
  gap: var(--gap-0);
}
.line,
.line4 {
  align-self: stretch;
  position: relative;
  border-top: 1px dashed var(--color-gray-500);
  box-sizing: border-box;
  height: 1px;
}
.line4 {
  border-top: 1px solid var(--color-gray-800);
}
.xlines {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--padding-7xs) var(--padding-12xs);
  gap: var(--gap-0);
  max-width: 100%;
}
.line5,
.xlines,
.ylines {
  box-sizing: border-box;
}
.line5 {
  width: 1px;
  position: relative;
  border-right: 1px dashed var(--color-gray-500);
}
.ylines {
  height: 100%;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: row;
  /* align-items: flex-start; */
  justify-content: space-between;
  padding: var(--padding-7xs) var(--padding-12xs);
  gap: var(--gap-xl);
  max-width: 100%;
}
.barstrip,
.singlebar,
.ylines {
  position: absolute;
  width: 100%;
  right: 0;
  left: 0;
}
.barstrip {
  height: 100%;
  top: 0;
  bottom: 0.2px;
  background-color: var(--color-yellowgreen-100);
}
.singlebar {
  height: 20%;
  top: 80%;
  bottom: 0;
}
.mainchart {
  align-self: stretch;
  flex: 1;
  position: relative;
}
.bargroup {
  align-self: stretch;
  width: 130px;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-9xl);
  box-sizing: border-box;
  z-index: 4;
}
.barstrip1,
.singlebar1 {
  position: absolute;
  width: 100%;
  right: 0;
  left: 0;
}
.barstrip1 {
  height: 100%;
  top: 0;
  bottom: 0.3px;
  background-color: var(--color-yellowgreen-100);
}
.singlebar1 {
  height: 5%;
  top: 95%;
  bottom: 0;
}
.bargroup1 {
  align-self: stretch;
  width: 130px;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-9xl);
  box-sizing: border-box;
  z-index: 5;
}
.barstrip2,
.singlebar2 {
  position: absolute;
  width: 100%;
  right: 0;
  left: 0;
}
.barstrip2 {
  height: 100%;
  top: 0;
  bottom: -0.2px;
  background-color: var(--color-yellowgreen-100);
}
.singlebar2 {
  height: 30%;
  top: 70%;
  bottom: 0;
}
.bargroup2 {
  align-self: stretch;
  width: 130px;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-9xl);
  box-sizing: border-box;
  z-index: 6;
}
.barstrip3,
.singlebar3 {
  position: absolute;
  width: 100%;
  right: 0;
  bottom: 0;
  left: 0;
}
.barstrip3 {
  height: 100%;
  top: 0;
  background-color: var(--color-yellowgreen-100);
}
.singlebar3 {
  height: 50%;
  top: 50%;
}
.bargroup3,
.line10 {
  box-sizing: border-box;
}
.bargroup3 {
  align-self: stretch;
  width: 130px;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-9xl);
  z-index: 7;
}

.singlebar4 {
  position: absolute;
  height: 80%;
  width: 100%;
  top: 20%;
  right: 0;
  bottom: 0;
  left: 0;
}
.bargroup4,
.line11 {
  box-sizing: border-box;
}
.bargroup4 {
  align-self: stretch;
  width: 130px;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-9xl);
  z-index: 8;
}

.barstrip5,
.singlebar5 {
  position: absolute;
  width: 100%;
  right: 0;
  left: 0;
}
.barstrip5 {
  height: 100%;
  top: 0;
  bottom: 0.9px;
  background-color: var(--color-yellowgreen-100);
}
.singlebar5 {
  height: 15%;
  top: 85%;
  bottom: 0;
}
.bargroup5,
.line12 {
  box-sizing: border-box;
}
.bargroup5 {
  align-self: stretch;
  width: 130px;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-9xl);
  z-index: 9;
}

.barstrip6,
.singlebar6 {
  position: absolute;
  width: 100%;
  right: 0;
  left: 0;
}
.barstrip6 {
  height: 100%;
  top: 0;
  bottom: 0.4px;
  background-color: var(--color-yellowgreen-100);
}
.singlebar6 {
  height: 90%;
  top: 10%;
  bottom: 0;
}
.bararea,
.bargroup6 {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.bargroup6 {
  align-self: stretch;
  width: 130px;
  flex-shrink: 0;
  padding: 0 var(--padding-9xl);
  z-index: 10;
}
.bararea {
  position: absolute;
  height: calc(100% - 12px);
  width: 100%;
  top: 6px;
  right: 0;
  bottom: 6px;
  left: 0;
  border-bottom: 1px solid var(--color-gray-800);
  overflow-x: auto;
  max-width: 100%;
}
.configanddata-bar-dont-remove {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
  display: none;
}
.graphigrid {
  flex: 1;
  position: relative;
  min-width: 630px;
  max-width: 100%;
}
.graphigrid,
.mainchart1,
.ton {
  align-self: stretch;
}
.mainchart1 {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  max-width: 100%;
  row-gap: var(--gap-xl);
  z-index: 1;
}
.ton {
  position: relative;
}
.xlabelbox {
  flex: 1;
  flex-direction: column;
  align-items: flex-end;
  max-width: 138px;
}
.chartaxis,
.xaxis,
.xlabelbox,
.xlabelbox3 {
  display: flex;
  justify-content: flex-start;
}
.xlabelbox3 {
  flex: 1;
  flex-direction: column;
  align-items: flex-end;
  max-width: 138px;
  flex-shrink: 0;
}
.chartaxis,
.xaxis {
  align-self: stretch;
  align-items: flex-start;
}
.xaxis {
  flex-direction: row;
  padding: 0 0 var(--padding-5xs) var(--padding-4xl);
  row-gap: var(--gap-xl);
  z-index: 2;
  margin-top: -2px;
  position: relative;
  text-align: center;
}
.chartaxis {
  flex: 1;
  flex-direction: column;
  max-width: 100%;
}
.squarefill {
  height: 8px;
  width: 8px;
  position: absolute;
  margin: 0 !important;
  top: -4px;
  left: -4px;
  background-color: #ffd98d;
  border: 1px solid var(--color-white);
  box-sizing: border-box;
  z-index: 1;
}
.basicnode,
.legendnode {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
}
.basicnode {
  height: 1px;
  width: 1px;
  position: relative;
}
.legendnode {
  height: 16px;
  width: 16px;
  padding: var(--padding-5xs) var(--padding-6xs) var(--padding-6xs)
    var(--padding-5xs);
  box-sizing: border-box;
}
.filllegends,
.legends1 {
  overflow: hidden;
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap;
}
.filllegends {
  align-items: center;
  align-content: center;
  padding: 0 var(--padding-5xs);
}
.legends1 {
  align-self: stretch;
  align-items: flex-start;
  align-content: flex-start;
  color: var(--color-gray-600);
}
.barlinechart {
  align-self: stretch;
  height: 244px;
  background-color: var(--color-whitesmoke);
  flex-direction: column;
  /* padding: var(--padding-5xl) var(--padding-5xs) var(--padding-5xs); */
  padding: 0px 11px 21px;
  flex-shrink: 0;
  z-index: 1;
  /* font-size: var(--font-size-xs); */
  font-size: 10px;
  color: var(--color-whitesmoke);
  font-family: var(--font-inter);
}
.barlinechart,
.schedule-header,
.weight {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  box-sizing: border-box;
  max-width: 100%;
}
.weight {
  align-self: stretch;
  height: 258px;
  border-radius: var(--br-base);
  background-color: var(--color-whitesmoke);
  border: 1px solid var(--color-lightgray);
  overflow: hidden;
  flex-shrink: 0;
  flex-direction: column;
  /* padding: var(--padding-6xl) var(--padding-9xs) 473px var(--padding-7xs); */
  padding: 3px 2px;
  /* gap: var(--gap-xs); */
}
/* -------------------------------------------------------------------------------- */
.schedule-header {
  flex-direction: row;
  padding: 0 var(--padding-12xs);
}
.upcomingschedules-item {
  align-self: stretch;
  flex: 1;
  position: relative;
  border-top: 1px solid rgba(88, 111, 107, 0.2);
  box-sizing: border-box;
  max-width: 100%;
}
.separator-wrapper {
  width: 361px;
  height: 1px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-6xs) 0 var(--padding-5xs);
  box-sizing: border-box;
  max-width: 100%;
}
.am1 {
  position: relative;
}
.time-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  /* padding: var(--padding-2xs) var(--padding-5xs) 0 0; */
  padding: 6px 4px 0 0;
}
.frame-child {
  /* align-self: stretch; */
  position: relative;
  border-right: 1px solid var(--color-white);
  box-sizing: border-box;
  height: 16.1px;
}
.description-area,
.upcomingschedules-inner {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.upcomingschedules-inner {
  width: 23.1px;
  align-items: center;
}
.description-area {
  align-items: flex-start;
  /* padding: var(--padding-base) 0 0; */
  padding: 8px 0 0;
}

.animals-new-born {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
}
.animals-new-born-have-fortific-wrapper {
  flex: 1;
  flex-direction: column;
  padding: 0;
  box-sizing: border-box;
  min-width: 212px;
  max-width: 100%;
}
.animals-new-born-have-fortific-wrapper,
.morning-feed-separator,
.time,
.time-container-parent {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.time-container-parent {
  align-self: stretch;
  flex-direction: row;
  max-width: 100%;
  row-gap: var(--gap-xl);
}
.morning-feed-separator,
.time {
  flex-direction: column;
  padding: var(--padding-10xs) 0 0;
}
.morning-feed-separator {
  /* padding: var(--padding-7xs) 0 0; */
  padding: 4px 0 0 ;
}
.health-care-check {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
}
.schedule-list,
.time-parent {
  align-items: flex-start;
  max-width: 100%;
}
.time-parent {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  /* gap: var(--gap-11xs); */
}
.schedule-list {
  align-self: stretch;
  gap: 3px;
  /* font-size: var(--font-size-xl); */
  font-size: 16px;
}
.schedule-list,
.schedule-title-area,
.time-list,
.upcomingschedules-inner1 {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.schedule-title-area {
  width: 431px;
  align-items: flex-start;
  gap: 6px;
  max-width: 100%;
}
.time-list,
.upcomingschedules-inner1 {
  flex-shrink: 0;
}
.time-list {
  align-items: flex-start;
  /* gap: var(--gap-5xl); */
  gap: 8px;
}
.upcomingschedules-inner1 {
  width: 10.1px;
  align-items: center;
}
.upcomingschedules-child1,
.upcomingschedules-child2 {
  position: absolute;
  top: -4px;
  left: 0;
  border-right: 1px solid var(--color-white);
  box-sizing: border-box;
  width: 1px;
  height: 16.1px;
}
.upcomingschedules-child2 {
  top: 22px;
}
.line-parent {
  height: 53.1px;
  width: 1px;
  position: relative;
  flex-shrink: 0;
}
.separator-container-child {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  /* padding: 0 var(--padding-4xs) 0 var(--padding-6xs); */
  padding: 0 7px 0 5px;
}
.separator-container {
  gap: 12.9px;
  flex-shrink: 0;
}
.schedule-content,
.separator-container,
.vertical-separator-area {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.vertical-separator-area {
  gap: 13.9px;
  flex-shrink: 0;
}
.schedule-content {
  /* padding: var(--padding-10xs) 0 0; */
  padding: 2px 0 0;
  margin-left: -0.5px;
  position: relative;
}
.check-feed-s {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
  /* white-space: pre-wrap; */
}
.task-title {
  flex-direction: column;
  /* gap: var(--gap-xs); */
  gap: 5px;
  max-width: 100%;
}
.feed-check,
.feed-check-parent,
.task-title,
.task-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.feed-check {
  flex-direction: row;
  padding: 0 var(--padding-12xs);
}
.feed-check-parent,
.task-wrapper {
  align-self: stretch;
  flex-direction: column;
  /* gap: var(--gap-5xs); */
  gap: 5px;
}
.task-wrapper {
  /* gap: var(--gap-14xl); */
  gap: 6px;
  flex-shrink: 0;
  max-width: 100%;
}
.task-wrapper-wrapper {
  flex: 1;
  flex-direction: column;
  /* padding: var(--padding-10xs) 0 0; */
  box-sizing: border-box;
  min-width: 226px;
  margin-left: -0.5px;
  position: relative;
}
.task-wrapper-wrapper,
.time-list-parent,
.upcomingschedules {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  max-width: 100%;
}
.time-list-parent {
  align-self: stretch;
  flex-direction: row;
  row-gap: var(--gap-xl);
  /* font-size: var(--font-size-xl); */
  font-size: 16px;
}
.upcomingschedules {
  flex: 1;
  border-radius: var(--br-base);
  background-color: var(--color-darkslategray-100);
  border: 1px solid var(--color-lightgray);
  box-sizing: border-box;
  overflow: hidden;
  flex-direction: column;
  /* padding: var(--padding-base) 0 var(--padding-13xl) 27px; */
  padding: 0px 0 2px 14px;
  /* gap: var(--gap-5xl); */
  gap: 7px;
  min-width: 315px;
}
/* ------------------------------------------------------------------------ */
.milk-production2 {
  margin: 0;
  position: relative;
  font-size: inherit;
  font-weight: 400;
  font-family: inherit;
  z-index: 1;
}
.graph-header {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-mid);
}
.line13 {
  align-self: stretch;
  position: relative;
  border-top: 1px dashed var(--color-gray-700);
  box-sizing: border-box;
  height: 1px;
}
.line6{
  /* --- */
  align-self: stretch;
    position: relative;
    border-top: 1px solid rgba(0, 0, 26, 0.3);
    box-sizing: border-box;
    height: 1px;
}
.xlines1 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--padding-7xs) var(--padding-12xs);
  gap: var(--gap-0);
}
.line17,
.line18,
.xlines1 {
  box-sizing: border-box;
}
.line17 {
  align-self: stretch;
  width: 1px;
  position: relative;
  border-right: 1px dashed var(--color-gray-700);
  z-index: 3;
}
.line18 {
  z-index: 4;
}
.line18,
.line19,
.line20 {
  align-self: stretch;
  width: 1px;
  position: relative;
  border-right: 1px dashed var(--color-gray-700);
}
.line19 {
  box-sizing: border-box;
  z-index: 5;
}
.line20 {
  z-index: 6;
}
.line20,
.line21,
.ylines1 {
  box-sizing: border-box;
}
.line21 {
  align-self: stretch;
  width: 1px;
  position: relative;
  border-right: 1px dashed var(--color-gray-700);
}
.ylines1 {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--padding-7xs) var(--padding-12xs);
  gap: var(--gap-xl);
}
/* .production-data,
.production-data1 {
  position: absolute;
  top: 16.5px;
  left: 1px;
  border-top: 1px dashed var(--color-gray-700);
  box-sizing: border-box;
  width: 377px;
  height: 0;
}
.production-data1 {
  top: 54.1px;
  border-top: 1px solid var(--color-gray-800);
} */
.singleline0-icon {
  height: 100%;
  width: 100%;
  overflow: hidden;
  object-fit: contain;
  position: absolute;
  left: 0;
  top: 9px;
  transform: scale(1.82);
}
.wrapper-singleline0 {
  position: absolute;
  height: 79.54%;
  width: 85.73%;
  top: 11%;
  /* top: 1.23%; */
  right: 7.12%;
  bottom: 19.22%;
  left: 7.15%;
  max-width: 100%;
  max-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.basicnode-icon2 {
  /* height: 0.7px;
  width: 0.7px; */
  position: relative;
  flex-shrink: 0;
}
.basicnode-wrapper {
  margin-top: -2.6px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 0 var(--padding-3xs) 0 var(--padding-2xs);
}
.div12 {
  width: 22px;
  position: relative;
  display: inline-block;
  flex-shrink: 0;
}
.datalabel {
  height: 11px;
  flex-direction: column;
  padding: var(--padding-12xs) var(--padding-base) var(--padding-4xs);
  box-sizing: border-box;
  gap: var(--gap-11xs-9);
  flex-shrink: 0;
}
.basicnode-container,
.datalabel,
.datalabel-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.datalabel-wrapper {
  flex-direction: column;
  padding: 7.2px 0 0;
}
.basicnode-container {
  margin-top: -2.6px;
  flex-direction: row;
  padding: 0 var(--padding-xs) 0 var(--padding-2xs);
}
.div14 {
  width: 23px;
  position: relative;
  display: inline-block;
  flex-shrink: 0;
}
.basicnode-frame,
.datalabel-container,
.datalabel4 {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.basicnode-frame {
  margin-top: -2.6px;
  flex-direction: row;
  padding: 0 var(--padding-3xs);
}
.datalabel-container,
.datalabel4 {
  flex-direction: column;
}
.datalabel4 {
  height: 11px;
  padding: var(--padding-12xs) var(--padding-base) var(--padding-4xs);
  box-sizing: border-box;
  gap: var(--gap-11xs-9);
  flex-shrink: 0;
  z-index: 1;
}
.datalabel-container {
  padding: 16.3px 0 0;
}
.datalabel-parent,
.frame-group {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
.datalabel-parent {
  width: 162.4px;
  justify-content: flex-start;
  gap: 54.2px;
  flex-shrink: 0;
}
.frame-group {
  width: 270.7px;
  justify-content: space-between;
  gap: var(--gap-xl);
}
.datalabel1,
.vertical-separator-area-inner {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.vertical-separator-area-inner {
  margin-top: -2.7px;
  flex-direction: row;
  padding: 0 var(--padding-xs) 0 var(--padding-2xs);
}
.datalabel1 {
  height: 11px;
  flex-direction: column;
  padding: var(--padding-12xs) var(--padding-sm) var(--padding-4xs)
    var(--padding-mini);
  box-sizing: border-box;
  gap: var(--gap-11xs);
  flex-shrink: 0;
}
.div13 {
  width: 24px;
  position: relative;
  display: inline-block;
  flex-shrink: 0;
}
.datalabel-frame,
.datalabel3 {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.datalabel3 {
  height: 11px;
  padding: var(--padding-12xs) var(--padding-sm) var(--padding-4xs)
    var(--padding-mini);
  box-sizing: border-box;
  gap: var(--gap-11xs-9);
  flex-shrink: 0;
  z-index: 1;
}
.datalabel-frame {
  padding: 7.9px 0 0;
}
.datalabel-group,
.frame-wrapper {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
}
.datalabel-group {
  width: 162.4px;
  justify-content: space-between;
  gap: var(--gap-xl);
}
.frame-wrapper {
  justify-content: flex-start;
  padding: 0 var(--padding-35xl);
}
.frame-parent {
  gap: 4.7px;
}
.datalabel5,
.frame-parent,
.linenodes0-inner {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.linenodes0-inner {
  padding: 5.1px 0 0;
}
.datalabel5 {
  height: 11px;
  padding: var(--padding-12xs) var(--padding-sm) var(--padding-4xs)
    var(--padding-mini);
  box-sizing: border-box;
  gap: var(--gap-11xs-9);
  flex-shrink: 0;
  z-index: 2;
}
.point-pair {
  flex-direction: column;
  padding: 29.4px 0 0;
}
.data-points,
.datalabel6,
.linenodes0,
.point-pair {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
}
.datalabel6 {
  height: 11px;
  flex-direction: column;
  padding: var(--padding-12xs) var(--padding-mini) var(--padding-4xs);
  box-sizing: border-box;
  gap: var(--gap-11xs-9);
  flex-shrink: 0;
  z-index: 1;
}
.data-points,
.linenodes0 {
  flex-direction: row;
}
.data-points {
  gap: 0.1px;
}
.linenodes0 {
  height: 100%;
  top:4px;
  right: -11px;
  
  padding: 0.7px 0 0;
  box-sizing: border-box;
  row-gap: var(--gap-xl);
  z-index: 1;
}
.linearea,
.linegroup0,
.linenodes0 {
  position: absolute;
  width: 100%;
  
}
.linegroup0 {
  height: 26.75%;
  top: 68.82%;
  bottom: 4.43%;
}
.linearea {
  margin-top: -6px;
  height: calc(100% + 40px);
  top: -36px;
  bottom: -6px;
  z-index: 2;
}
.configanddata-line-dont-remov {
  position: absolute;
  top: 0;
  left: 0;
  width: 8px;
  height: 8px;
}
.graphigrid1 {
  align-self: stretch;
  flex: 1;
  position: relative;
  min-width: 246px;
  font-size: var(--font-size-3xs);
  color: var(--color-white);
}
.mainchart2 {
  align-self: stretch;
  height: 165px;
  flex-direction: row;
  align-items: center;
  row-gap: var(--gap-xl);
}
.chartaxis1,
.mainchart2,
.xaxis1,
.xlabelbox7 {
  display: flex;
  justify-content: flex-start;
}
.xlabelbox7 {
  flex: 1;
  flex-direction: column;
  align-items: flex-end;
  max-width: 54px;
}
.chartaxis1,
.xaxis1 {
  align-self: stretch;
  align-items: flex-start;
}
.xaxis1 {
  flex-direction: row;
  padding: 0 0 var(--padding-5xs) 36px;
  row-gap: var(--gap-xl);
  margin-top: -2px;
  position: relative;
  text-align: center;
}
.chartaxis1 {
  flex: 1;
  flex-direction: column;
}
.line27 {
  position: absolute;
  top: 7px;
  left: 0;
  background-color: var(--color-darkslategray-100);
  width: 16px;
  height: 2px;
}
.basicnode-icon7 {
  position: absolute;
  top: 3px;
  left: 3px;
  width: 10px;
  height: 10px;
  z-index: 1;
}
.span {
  color: var(--color-white);
}
.legend5 {
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: var(--padding-9xs);
  gap: var(--gap-10xs);
}
.graph,
.legends2 {
  align-self: stretch;
}
.legends2 {
  overflow: hidden;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  flex-wrap: wrap;
  align-content: flex-start;
  color: var(--color-black);
}
.graph {
  flex: 1;
  border-radius: var(--br-7xs);
  background-color: var(--color-white);
  z-index: 1;
  margin-top: -2px;
  position: relative;
  font-size: var(--font-size-xs);
  color: var(--color-darkslategray-100);
  font-family: var(--font-inter);
}
.graph,
.milk-graph,
.milk-graph-wrapper {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
}
.milk-graph {
  align-self: stretch;
  flex: 1;
  border-radius: var(--br-base);
  background-color: var(--color-white);
  border: 1px solid var(--color-lightgray);
  overflow: hidden;
}
.milk-graph-wrapper {
  padding: 2px 0 0;
  box-sizing: border-box;
  min-width: 434px;
  max-width: 100%;
  color: var(--color-gray-100);
}
/* --------------------------------------------------------------------- */
.schedule-container,
.schedule-wrapper {
  flex-direction: row;
  align-items: flex-start;
}
.schedule-wrapper {
  flex: 1;
  display: flex;
  justify-content: flex-start;
  gap: var(--gap-29xl);
  max-width: 100%;
}
.schedule-container {
  width: 1008px;
  justify-content: flex-end;
  padding: 0 var(--padding-2xl);
  box-sizing: border-box;
  color: var(--color-floralwhite);
  font-family: var(--highlights);
}
.schedule-container,
.weight-chart,
.weight1 {
  display: flex;
  max-width: 100%;
}
.weight-chart {
  flex: 1;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  gap: 4px;
}
.weight1 {
  align-self: stretch;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 0 var(--padding-7xl) 0 var(--padding-6xl);
  box-sizing: border-box;
  font-family: var(--font-roboto-serif);
}
.content,
.dashboard,
.navbar-and-main {
  display: flex;
  justify-content: flex-start;
}
.navbar-and-main {
  align-self: stretch;
  flex-direction: column;
  align-items: flex-end;
  gap: 2.5px;
  max-width: 100%;
  text-align: left;
  font-size: 24px;
  color: var(--color-gray-100);
  font-family: var(--highlights);
}
.content,
.dashboard {
  align-items: flex-start;
  box-sizing: border-box;
}
.content {
  position: absolute;
  height: 88%;
  width: 76%;
  top: 10%;
  left: 20%;
  flex-direction: column;
  /* padding: var(--padding-7xs) 0 0; */
  max-width: 100%;
}
.dashboard {
  width: 100%;
  height: 100vh;
  position: relative;
  background-color: var(--color-gainsboro);
  overflow: hidden;
  flex-direction: row;
  padding: 0px 3px;
  gap: 11px;
  line-height: normal;
  letter-spacing: normal;
  text-align: center;
  font-size: var(--font-size-58xl);
  color: var(--color-floralwhite);
  font-family: var(--font-abeezee);
}
/* ----------------------------------------------------------------------------- */
@media screen and (max-width: 1350px) {
  .dashboard {
    flex-wrap: wrap;
  }
  
  .side-bar {
    height: auto;
  }
  
  .content {
    max-width: calc(100% - 320px);
  }
}

@media screen and (max-width: 1150px) {
  .num-of-animal {
    flex: 1;
  }
  
  .content-metrics,
  .mainchart1,
  .xaxis {
    flex-wrap: wrap;
  }
  
  .weight {
    padding-top: var(--padding-xl);
    padding-bottom: 307px;
    box-sizing: border-box;
  }
  
  .milk-graph-wrapper {
    flex: 1;
  }
  
  .schedule-wrapper {
    flex-wrap: wrap;
  }
  
  .navbar {
    width: 100%;
    max-width: 100%;
  }
  
  .content-info {
    height: auto;
    flex-wrap: wrap;
  }
}

@media screen and (max-width: 950px) {
  .content {
    max-width: 100%;
  }
  
  .side-bar {
    width: 250px;
  }
  
  .weight1 {
    padding: 0 var(--padding-3xs);
  }
  
  .schedule-container {
    width: 100%;
    padding: 0 var(--padding-xs);
  }
  
  .upcomingschedules,
  .milk-graph {
    min-width: 280px;
  }
}

@media screen and (max-width: 800px) {
  .quartiers,
  .quartiers-milk-production {
    font-size: var(--font-size-10xl);
  }
  
  .summer,
  .sun {
    font-size: var(--font-size-7xl);
  }
  
  .graphigrid,
  .quarters {
    min-width: 100%;
  }
  
  .weight {
    padding-bottom: 200px;
    box-sizing: border-box;
  }
  
  .time-container-parent,
  .time-list-parent,
  .time-parent {
    flex-wrap: wrap;
  }
  
  .milk-production2 {
    font-size: var(--font-size-10xl);
  }
  
  .linenodes0 {
    flex-wrap: wrap;
  }
  
  .linearea,
  .linegroup0 {
    height: auto;
    min-height: 56.7px;
  }
  
  .linearea {
    min-height: 212px;
  }
  
  .milk-graph-wrapper {
    min-width: 100%;
  }
  
  .schedule-wrapper {
    gap: var(--gap-5xl);
  }
  
  .dashboard {
    flex-direction: column;
  }
  
  .side-bar {
    width: 100%;
    border-radius: var(--br-base);
    margin-bottom: 10px;
    height: auto;
  }
  
  .side-bar-bottom {
    height: auto;
    padding: var(--padding-base) 0;
  }
  
  .side-bar-element {
    height: auto;
    padding: var(--padding-xs) 0;
    gap: var(--gap-5xl);
  }
}

@media screen and (max-width: 600px) {
  .navbar {
    height: auto;
    padding: var(--padding-5xs) var(--padding-3xs);
  }
  
  .nav-profile {
    gap: var(--gap-5xs);
  }
  
  .content-metrics {
    flex-direction: column;
    gap: var(--gap-xs);
  }
  
  .weight-chart {
    width: 100%;
  }
  
  .schedule-wrapper {
    flex-direction: column;
    gap: var(--gap-base);
  }
  
  .upcomingschedules {
    width: 100%;
    padding-left: var(--padding-xs);
  }
  
  .milk-graph-wrapper {
    width: 100%;
  }
  
  .barlinechart {
    padding: 0 var(--padding-3xs) var(--padding-xs);
  }
  
  .yaxisleft {
    padding-right: var(--padding-3xs);
  }
}

@media screen and (max-width: 450px) {
  .side-bar-bottom {
    height: auto;
    font-size: var(--font-size-xl);
  }
  
  .quartiers {
    font-size: var(--font-size-3xl);
  }
  
  .div {
    font-size: var(--font-size-lgi);
  }
  
  .piechart {
    flex-wrap: wrap;
  }
  
  .num-of-animal {
    padding-top: var(--padding-xl);
    padding-bottom: var(--padding-6xl);
    box-sizing: border-box;
    min-width: 100%;
  }
  
  .quartiers-milk-production {
    font-size: var(--font-size-3xl);
  }
  
  .summer {
    font-size: var(--font-size-lgi);
  }
  
  .quarters-chart-parent {
    flex: 1;
  }
  
  .sun {
    font-size: var(--font-size-lgi);
  }
  
  .quarters-chart {
    flex-wrap: wrap;
  }
  
  .quarties {
    height: auto;
    min-height: 393px;
  }
  
  .line5 {
    width: 100%;
    height: 1px;
    border-top: 1px solid var(--color-gray-500);
    box-sizing: border-box;
  }
  
  .ylines {
    flex-wrap: wrap;
  }
  
  .am1,
  .animals-new-born,
  .check-feed-s,
  .health-care-check {
    font-size: var(--font-size-base);
  }
  
  .task-wrapper {
    gap: var(--gap-base);
  }
  
  .upcomingschedules {
    padding-top: var(--padding-xs);
    padding-bottom: var(--padding-xs);
    box-sizing: border-box;
  }
  
  .milk-production2 {
    font-size: var(--font-size-3xl);
  }
  
  .frame-wrapper {
    padding-left: var(--padding-xs);
    padding-right: var(--padding-xs);
    box-sizing: border-box;
  }
  
  .mainchart2 {
    height: auto;
  }
  
  .mainchart2,
  .xaxis1 {
    flex-wrap: wrap;
  }
  
  .notification-bell-icon,
  .male-avatar-portrait-of-a-youn-icon {
    height: 30px;
    width: 30px;
  }
}

@media screen and (max-width: 350px) {
  .quartiers,
  .quartiers-milk-production,
  .milk-production2 {
    font-size: var(--font-size-xl);
  }
  
  .side-bar-bottom {
    font-size: var(--font-size-lg);
    gap: 3px;
  }
  
  .side-animal,
  .side-feed,
  .side-report {
    width: auto;
    gap: var(--gap-9xs);
  }
  
  .cow-1-icon {
    height: 35px;
    width: 35px;
  }
  
  .animals-new-born,
  .health-care-check,
  .check-feed-s {
    font-size: 14px;
  }
  
  .am1 {
    font-size: 14px;
  }
  
  .time-container-parent {
    gap: var(--gap-9xs);
  }
  
  .barlinechart {
    height: 200px;
  }
  
  .graphigrid {
    min-width: 100%;
  }
  
  .pregnant {
    font-size: 9px;
  }
}

/* إضافة media queries أساسية للتوافق مع مختلف أحجام الشاشات */
@media screen and (max-width: 1200px) {
  .dashboard {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .content {
    max-width: calc(100% - 320px);
  }
  
  .navbar {
    width: 100%;
    max-width: 100%;
  }
}

@media screen and (max-width: 900px) {
  .content {
    max-width: 100%;
  }
  
  .side-bar {
    width: 250px;
  }
  
  .weight1, 
  .schedule-container {
    width: 100%;
    padding: 0 10px;
  }
  
  .schedule-wrapper {
    flex-wrap: wrap;
    gap: 15px;
  }
}

@media screen and (max-width: 768px) {
  .dashboard {
    flex-direction: column;
  }
  
  .side-bar {
    width: 100%;
    border-radius: 10px;
    margin-bottom: 10px;
  }
  
  .content-metrics {
    flex-wrap: wrap;
  }
  
  .num-of-animal,
  .quarties {
    width: 100%;
  }
  
  .weight-chart,
  .milk-graph-wrapper {
    width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .navbar {
    height: auto;
    padding: 5px;
  }
  
  .nav-profile {
    gap: 10px;
  }
  
  .notification-bell-icon,
  .male-avatar-portrait-of-a-youn-icon {
    height: 30px;
    width: 30px;
  }
  
  .quartiers,
  .milk-production2 {
    font-size: 20px;
  }
  
  .side-bar-bottom {
    font-size: 20px;
  }
  
  .animals {
    font-size: 18px;
  }
}

/* إعادة تصميم قائمة النافبار المنسدلة */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  z-index: 100;
}

.nav-profile1 {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 140px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 101;
}

.component-13, 
.component-131, 
.component-14 {
  align-self: stretch;
  height: 44px;
  border-radius: var(--br-7xs);
  background-color: var(--color-floralwhite);
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: var(--padding-2xs) var(--padding-3xs);
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.3s;
}

.component-13:hover, 
.component-131:hover, 
.component-14:hover {
  background-color: #f0f0f0;
}

.log-out, 
.log-out1, 
.log-out2 {
  width: auto;
  position: relative;
  font-weight: 500;
  display: inline-block;
}

.popup-overlay a {
  text-decoration: none;
  color: black;
}

/* إضافة مؤشر للعنصر القابل للنقر */
#navProfileContainer {
  cursor: pointer;
}

