/**
 * Newborn Page Management System with Popup Integration
 * Preserves graphs/charts and adds popup system identical to animal.js
 */

// ==================== POPUP MANAGER CLASS ====================

class NewbornPopupManager {
    constructor() {
        this.popupContainer = null;
        this.isPopupVisible = false;
        this.externalCloseButton = null;
        this.currentIframe = null;
        console.log('🔧 NewbornPopupManager initialized');
    }

    async showPopup(popupType, options = {}) {
        try {
            console.log(`🔄 Loading ${popupType} popup for newborn management...`);
            this.hidePopup();

            // Prevent body scrolling and scroll to top
            document.body.classList.add('popup-open');
            window.scrollTo(0, 0);

            this.createPopupContainer();
            await this.loadPopupWithIframe(popupType, options);

            // Show popup with animation
            this.popupContainer.classList.add('visible');
            this.isPopupVisible = true;

            // Ensure popup is at the top of viewport
            this.popupContainer.scrollTop = 0;

            console.log(`✅ Newborn popup "${popupType}" displayed successfully`);
        } catch (error) {
            console.error(`❌ Failed to show newborn popup "${popupType}":`, error);
            this.hidePopup();
        }
    }

    async loadPopupWithIframe(popupType, options = {}) {
        const htmlFiles = {
            'addnewanimal': 'addnewanimal.html',
            'updateanimal': 'updateanimal.html',
            'search': 'search.html',
            'filter': 'filter.html',
            'delete': 'deletepop.html'
        };

        const htmlFile = htmlFiles[popupType];
        if (!htmlFile) {
            throw new Error(`Unknown popup type: ${popupType}`);
        }

        return new Promise((resolve, reject) => {
            const popupBody = this.popupContainer.querySelector('.newborn-popup-body');
            if (!popupBody) {
                reject(new Error('Popup body container not found'));
                return;
            }

            const iframe = document.createElement('iframe');
            iframe.src = htmlFile;
            iframe.style.cssText = `
                width: 100% !important;
                height: 100% !important;
                border: none !important;
                background: transparent !important;
                overflow: hidden !important;
                display: block !important;
            `;

            iframe.setAttribute('frameborder', '0');
            iframe.setAttribute('scrolling', 'no');
            iframe.setAttribute('allowtransparency', 'true');

            iframe.addEventListener('load', () => {
                try {
                    console.log(`✅ Iframe loaded: ${htmlFile}`);
                    this.setupNewbornIframe(iframe, popupType, options);
                    resolve();
                } catch (error) {
                    console.error('Error setting up iframe:', error);
                    reject(error);
                }
            });

            iframe.addEventListener('error', (error) => {
                console.error(`❌ Failed to load iframe: ${htmlFile}`, error);
                reject(new Error(`Failed to load ${htmlFile}`));
            });

            popupBody.innerHTML = '';
            popupBody.appendChild(iframe);
            this.currentIframe = iframe;
        });
    }

    setupNewbornIframe(iframe, popupType, options) {
        try {
            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
            if (!iframeDoc) {
                console.warn('Cannot access iframe document');
                return;
            }

            // Pre-select newborn type in forms
            if (popupType === 'addnewanimal') {
                setTimeout(() => {
                    const newbornRadio = iframeDoc.querySelector('input[type="radio"][id="newborn"], input[type="radio"][value="newborn"]');
                    if (newbornRadio) {
                        newbornRadio.checked = true;
                        newbornRadio.dispatchEvent(new Event('change'));
                        console.log('✅ Pre-selected newborn type in form');
                    }
                }, 100);
            }

            console.log(`✅ Iframe setup completed for ${popupType}`);
        } catch (error) {
            console.warn('Error setting up iframe:', error);
        }
    }

    createPopupContainer() {
        this.removePopupContainer();

        this.popupContainer = document.createElement('div');
        this.popupContainer.className = 'newborn-popup-overlay';
        this.popupContainer.innerHTML = `
            <div class="newborn-popup-container">
                <div class="newborn-popup-body">
                    <!-- Popup content will be loaded here -->
                </div>
            </div>
        `;

        this.addPopupStyles();
        this.setupPopupEventListeners();
        this.addExternalCloseButton();
        document.body.appendChild(this.popupContainer);
    }

    addExternalCloseButton() {
        const externalCloseButton = document.createElement('button');
        externalCloseButton.className = 'external-close-button';
        externalCloseButton.innerHTML = '×';
        externalCloseButton.setAttribute('type', 'button');

        externalCloseButton.style.cssText = `
            position: absolute !important;
            top: -15px !important;
            right: -15px !important;
            width: 50px !important;
            height: 50px !important;
            background-color: #aedf32 !important;
            color: #ffffff !important;
            border: 3px solid #ffffff !important;
            border-radius: 50% !important;
            font-size: 30px !important;
            font-weight: bold !important;
            cursor: pointer !important;
            z-index: 999999 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            font-family: Arial, sans-serif !important;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5) !important;
            transition: all 0.2s ease !important;
        `;

        externalCloseButton.addEventListener('mouseenter', () => {
            externalCloseButton.style.backgroundColor = '#8bc220';
            externalCloseButton.style.transform = 'scale(1.1)';
        });

        externalCloseButton.addEventListener('mouseleave', () => {
            externalCloseButton.style.backgroundColor = '#aedf32';
            externalCloseButton.style.transform = 'scale(1)';
        });

        externalCloseButton.addEventListener('click', (e) => {
            console.log('🔴 EXTERNAL CLOSE BUTTON CLICKED');
            e.preventDefault();
            e.stopPropagation();
            this.hidePopup();
        });

        this.popupContainer.style.position = 'relative';
        this.popupContainer.appendChild(externalCloseButton);
        this.externalCloseButton = externalCloseButton;
        console.log('✅ External close button added');
    }

    hidePopup() {
        if (this.popupContainer && this.isPopupVisible) {
            this.popupContainer.classList.remove('visible');

            // Restore body scrolling
            document.body.classList.remove('popup-open');

            setTimeout(() => {
                this.removePopupContainer();
            }, 300);

            this.isPopupVisible = false;
            this.removeExternalCloseButton();
        }
    }

    removePopupContainer() {
        if (this.popupContainer && this.popupContainer.parentNode) {
            this.popupContainer.parentNode.removeChild(this.popupContainer);
            this.popupContainer = null;
            this.isPopupVisible = false;

            // Ensure body scrolling is restored
            document.body.classList.remove('popup-open');

            this.removeExternalCloseButton();
        }
    }

    removeExternalCloseButton() {
        if (this.externalCloseButton && this.externalCloseButton.parentNode) {
            this.externalCloseButton.parentNode.removeChild(this.externalCloseButton);
            this.externalCloseButton = null;
        }
    }

    addPopupStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .newborn-popup-overlay {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100vw !important;
                height: 100vh !important;
                background: transparent !important;
                z-index: 999998 !important;
                display: flex !important;
                justify-content: center !important;
                align-items: flex-start !important;
                padding-top: 5vh !important;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                overflow-y: auto !important;
                box-sizing: border-box !important;
                pointer-events: none !important;
            }

            .newborn-popup-overlay.visible {
                opacity: 1 !important;
                visibility: visible !important;
                pointer-events: auto !important;
            }

            .newborn-popup-container {
                background: white !important;
                border-radius: 16px !important;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
                max-width: 90vw !important;
                max-height: 90vh !important;
                width: 85vw !important;
                height: 90vh !important;
                overflow: hidden !important;
                position: relative !important;
                margin: 0 auto !important;
                transform: translateY(0) !important;
                pointer-events: auto !important;
            }

            .newborn-popup-header {
                display: none !important;
            }

            .newborn-popup-body {
                width: 100% !important;
                height: 100% !important;
                overflow: hidden !important;
                background: white !important;
                border-radius: 16px !important;
            }

            /* Ensure popup appears above everything */
            body.popup-open {
                overflow: hidden !important;
            }
        `;

        if (!document.querySelector('style[data-newborn-popup-styles]')) {
            style.setAttribute('data-newborn-popup-styles', 'true');
            document.head.appendChild(style);
        }
    }

    setupPopupEventListeners() {
        if (!this.popupContainer) return;

        // Only keep Escape key to close (no click outside since no background)
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isPopupVisible) {
                this.hidePopup();
            }
        });
    }
}

// Create global popup manager instance
window.newbornPopupManager = new NewbornPopupManager();

// Global functions
window.showNewbornPopup = function(popupType, options = {}) {
    if (window.newbornPopupManager) {
        window.newbornPopupManager.showPopup(popupType, options);
    } else {
        console.error('Newborn Popup Manager not initialized');
    }
};

window.closeNewbornPopup = function() {
    if (window.newbornPopupManager) {
        window.newbornPopupManager.hidePopup();
    }
};

// PostMessage listener
window.addEventListener('message', function(event) {
    if (event.data && event.data.action === 'closePopup') {
        if (window.newbornPopupManager) {
            window.newbornPopupManager.hidePopup();
        }
    }
});

// ==================== SEARCH DROPDOWN MANAGER ====================

/**
 * Newborn Search Dropdown Manager
 * Handles the dropdown menu for the Search button with inline input fields
 * Same style and functionality as animal page
 */
class NewbornSearchDropdownManager {
    constructor() {
        this.dropdownContainer = null;
        this.isVisible = false;
        this.currentSearchField = null;
        this.currentSearchTerm = '';
        this.hideTimeout = null;
    }

    /**
     * Show search dropdown menu
     * @param {HTMLElement} buttonElement - Search button element
     */
    showDropdown(buttonElement) {
        // If dropdown is already visible, don't do anything
        if (this.isVisible && this.dropdownContainer) {
            console.log('🔽 Dropdown already visible, ignoring');
            return;
        }

        // Clear any pending hide timeouts
        if (this.hideTimeout) {
            clearTimeout(this.hideTimeout);
            this.hideTimeout = null;
        }

        // Remove any existing dropdown immediately (no animation)
        if (this.dropdownContainer && this.dropdownContainer.parentNode) {
            this.dropdownContainer.parentNode.removeChild(this.dropdownContainer);
            this.dropdownContainer = null;
        }

        // Add active class to search container
        const searchContainer = buttonElement.closest('.search-dropdown-container');
        if (searchContainer) {
            searchContainer.classList.add('active');
        }

        // Create dropdown
        this.createDropdown(buttonElement);
        this.showDropdownWithAnimation();
    }

    /**
     * Create search dropdown menu
     * @param {HTMLElement} buttonElement - Search button element
     */
    createDropdown(buttonElement) {
        this.dropdownContainer = document.createElement('div');
        this.dropdownContainer.className = 'search-dropdown-container-dynamic';

        const buttonRect = buttonElement.getBoundingClientRect();

        this.dropdownContainer.innerHTML = `
            <div class="search-dropdown-menu-dynamic">
                <div class="search-option-dynamic" data-search="code">
                    <span class="search-icon-dynamic">🏷️</span>
                    <span>By Code</span>
                </div>
                <div class="search-input-container-dynamic" id="codeSearchContainerDynamic" style="display: none;">
                    <input type="text" class="search-input-dynamic" id="codeSearchInputDynamic" placeholder="Enter newborn code...">
                    <div class="search-buttons-dynamic">
                        <button class="search-btn-small-dynamic" data-search-type="code">Search</button>
                        <button class="search-btn-cancel-dynamic">Cancel</button>
                    </div>
                </div>

                <div class="search-option-dynamic" data-search="herdNumber">
                    <span class="search-icon-dynamic">🏠</span>
                    <span>By Herd Number</span>
                </div>
                <div class="search-input-container-dynamic" id="herdNumberSearchContainerDynamic" style="display: none;">
                    <input type="text" class="search-input-dynamic" id="herdNumberSearchInputDynamic" placeholder="Enter herd number...">
                    <div class="search-buttons-dynamic">
                        <button class="search-btn-small-dynamic" data-search-type="herdNumber">Search</button>
                        <button class="search-btn-cancel-dynamic">Cancel</button>
                    </div>
                </div>

                <div class="search-option-dynamic" data-search="gender">
                    <span class="search-icon-dynamic">⚥</span>
                    <span>By Gender</span>
                </div>
                <div class="search-input-container-dynamic" id="genderSearchContainerDynamic" style="display: none;">
                    <input type="text" class="search-input-dynamic" id="genderSearchInputDynamic" placeholder="Enter gender (Male/Female)...">
                    <div class="search-buttons-dynamic">
                        <button class="search-btn-small-dynamic" data-search-type="gender">Search</button>
                        <button class="search-btn-cancel-dynamic">Cancel</button>
                    </div>
                </div>

                <div class="search-option-dynamic" data-search="clear">
                    <span class="search-icon-dynamic">🔄</span>
                    <span>Clear Search</span>
                </div>
            </div>
        `;

        // Position dropdown
        this.dropdownContainer.style.cssText = `
            position: fixed;
            top: ${buttonRect.bottom + 5}px;
            left: ${buttonRect.left}px;
            z-index: 9999;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        `;

        // Add styles
        this.addSearchDropdownStyles();

        // Add event listeners
        this.setupSearchDropdownEventListeners();

        // Add to document
        document.body.appendChild(this.dropdownContainer);
    }

    /**
     * Add CSS styles for search dropdown (matching animal page style)
     */
    addSearchDropdownStyles() {
        if (!document.querySelector('#search-dropdown-styles-newborn')) {
            const style = document.createElement('style');
            style.id = 'search-dropdown-styles-newborn';
            style.textContent = `
                .search-dropdown-menu-dynamic {
                    background-color: #c1d8b9;
                    border: 2px solid #a8c49a;
                    border-radius: 14px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    padding: 0;
                    min-width: 280px;
                    overflow: hidden;
                }



                .search-option-dynamic {
                    display: flex;
                    align-items: center;
                    padding: 12px 16px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    gap: 10px;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                    color: #a9a9a9;
                }

                .search-option-dynamic:last-child {
                    border-bottom: none;
                }

                .search-option-dynamic:hover {
                    background-color: #a8c49a;
                    color: #ffffff;
                }

                .search-option-dynamic.active {
                    background-color: #a8c49a;
                    color: #ffffff;
                    font-weight: bold;
                }

                .search-icon-dynamic {
                    font-size: 18px;
                    display: inline-block;
                    transition: all 0.3s ease;
                }

                .search-option-dynamic span {
                    font-size: 16px;
                    font-weight: 500;
                    color: inherit;
                }

                .search-input-container-dynamic {
                    padding: 12px 16px;
                    background-color: #f8f9fa;
                    border-top: 1px solid rgba(255, 255, 255, 0.2);
                }

                .search-input-dynamic {
                    width: 100%;
                    padding: 8px 12px;
                    border: 2px solid #c1d8b9;
                    border-radius: 8px;
                    font-size: 14px;
                    outline: none;
                    transition: border-color 0.3s ease;
                    margin-bottom: 8px;
                }

                .search-input-dynamic:focus {
                    border-color: #a8c49a;
                }

                .search-buttons-dynamic {
                    display: flex;
                    gap: 8px;
                    justify-content: flex-end;
                }

                .search-btn-small-dynamic {
                    padding: 6px 12px;
                    background-color: #c1d8b9;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .search-btn-small-dynamic:hover {
                    background-color: #a8c49a;
                    transform: translateY(-1px);
                }

                .search-btn-cancel-dynamic {
                    padding: 6px 12px;
                    background-color: #e0e0e0;
                    color: #666;
                    border: none;
                    border-radius: 6px;
                    font-size: 12px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .search-btn-cancel-dynamic:hover {
                    background-color: #d0d0d0;
                    transform: translateY(-1px);
                }

                /* Expanded dropdown when input is shown */
                .search-dropdown-menu-dynamic.expanded {
                    min-height: auto;
                    max-height: 400px;
                }

                .search-dropdown-container-dynamic {
                    z-index: 10000;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Setup event listeners for search dropdown
     */
    setupSearchDropdownEventListeners() {
        if (!this.dropdownContainer) return;

        // Search option clicks
        const searchOptions = this.dropdownContainer.querySelectorAll('.search-option-dynamic');
        searchOptions.forEach(option => {
            option.addEventListener('click', () => {
                const searchType = option.getAttribute('data-search');
                this.selectSearchOption(option, searchType);
            });
        });

        // Search buttons
        const searchButtons = this.dropdownContainer.querySelectorAll('.search-btn-small-dynamic');
        searchButtons.forEach(button => {
            button.addEventListener('click', () => {
                const searchType = button.getAttribute('data-search-type');
                this.performSearch(searchType);
            });
        });

        // Cancel buttons
        const cancelButtons = this.dropdownContainer.querySelectorAll('.search-btn-cancel-dynamic');
        cancelButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.cancelSearch();
            });
        });

        // Enter key in input fields
        const inputFields = this.dropdownContainer.querySelectorAll('.search-input-dynamic');
        inputFields.forEach(input => {
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    const searchType = input.id.replace('SearchInputDynamic', '').replace('code', 'code').replace('herdNumber', 'herdNumber').replace('gender', 'gender');
                    this.performSearch(searchType);
                }
            });
        });

        // Click outside to close (add delay to prevent immediate closure)
        setTimeout(() => {
            document.addEventListener('click', this.handleSearchOutsideClick.bind(this));
        }, 100);
    }

    /**
     * Select search option and show input field
     * @param {HTMLElement} optionElement - Selected option element
     * @param {string} searchType - Type of search
     */
    selectSearchOption(optionElement, searchType) {
        // Remove active class from all options
        this.dropdownContainer.querySelectorAll('.search-option-dynamic').forEach(opt => {
            opt.classList.remove('active');
        });

        // Hide all input containers
        this.dropdownContainer.querySelectorAll('.search-input-container-dynamic').forEach(container => {
            container.style.display = 'none';
        });

        // Add active class to selected option
        optionElement.classList.add('active');

        // Execute search based on type
        if (searchType === 'clear') {
            this.clearSearch();
            this.hideDropdown();
        } else {
            // Show the corresponding input container
            const inputContainer = this.dropdownContainer.querySelector(`#${searchType}SearchContainerDynamic`);
            if (inputContainer) {
                inputContainer.style.display = 'block';
                this.dropdownContainer.querySelector('.search-dropdown-menu-dynamic').classList.add('expanded');

                // Focus on the input field
                const inputField = this.dropdownContainer.querySelector(`#${searchType}SearchInputDynamic`);
                if (inputField) {
                    setTimeout(() => inputField.focus(), 100);
                }
            }
        }
    }

    /**
     * Perform search with input value
     * @param {string} searchType - Type of search
     */
    performSearch(searchType) {
        const inputField = this.dropdownContainer.querySelector(`#${searchType}SearchInputDynamic`);
        if (inputField) {
            const searchTerm = inputField.value.trim();
            if (searchTerm) {
                console.log(`🔍 Newborn search: ${searchType} = "${searchTerm}"`);
                this.searchNewbornAnimals(searchTerm, searchType);
                this.hideDropdown();
                // Clear the input field
                inputField.value = '';
            } else {
                // Show error or highlight empty field
                inputField.style.borderColor = '#ff6b6b';
                setTimeout(() => {
                    inputField.style.borderColor = '#c1d8b9';
                }, 1000);
            }
        }
    }

    /**
     * Cancel search and hide dropdown
     */
    cancelSearch() {
        this.hideDropdown();
    }

    /**
     * Clear search and show all animals
     */
    clearSearch() {
        console.log('🔄 Clearing newborn search');
        // Reload all newborn animals
        const newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
        this.updateTableWithResults(newbornAnimals);
        this.currentSearchTerm = '';
        this.currentSearchField = null;
    }

    /**
     * Update table with search results
     * @param {Array} animals - Filtered animals array
     */
    updateTableWithResults(animals) {
        const tableBody = document.querySelector('tbody');
        if (!tableBody) return;

        // Clear and populate table
        tableBody.innerHTML = '';

        if (animals.length === 0) {
            // Show no results message
            const row = document.createElement('tr');
            row.innerHTML = `
                <td colspan="7" style="text-align: center; padding: 20px; color: #666;">
                    No matching newborn animals found
                </td>
            `;
            tableBody.appendChild(row);
            return;
        }

        animals.forEach(animal => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input class="checkbox" type="checkbox">${animal.code}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${animal.herdNumber || 'N/A'}</td>
                <td>${animal.gender || 'N/A'}</td>
                <td>${this.formatDate(animal.birthDate)}</td>
                <td>${animal.weight || 'N/A'}</td>
                <td>${this.formatDate(animal.weightDate)}</td>
                <td>${animal.healthcare || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });

        // Re-setup event listeners for new checkboxes
        this.setupCheckboxEventListeners();
    }

    /**
     * Format date for display
     * @param {string} dateString - Date string to format
     * @returns {string} - Formatted date
     */
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? dateString :
               date.toLocaleDateString('en-US', {year: 'numeric', month: '2-digit', day: '2-digit'});
    }

    /**
     * Setup checkbox event listeners
     */
    setupCheckboxEventListeners() {
        const checkboxes = document.querySelectorAll('.checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    checkboxes.forEach(cb => { if (cb !== this) cb.checked = false; });
                }

                // Enable/disable delete button based on selection
                const anyChecked = document.querySelector('.checkbox:checked');
                const deleteAnimalBtn = document.querySelector('.frame-9:nth-child(5)');
                if (deleteAnimalBtn) {
                    deleteAnimalBtn.disabled = !anyChecked;
                    deleteAnimalBtn.style.opacity = anyChecked ? 1 : 0.5;
                }
            });
        });
    }

    /**
     * Search newborn animals
     * @param {string} searchTerm - Search term
     * @param {string} searchField - Field to search in
     */
    searchNewbornAnimals(searchTerm, searchField) {
        const newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
        const filteredAnimals = newbornAnimals.filter(animal => {
            const fieldValue = animal[searchField];
            if (fieldValue) {
                return fieldValue.toString().toLowerCase().includes(searchTerm.toLowerCase());
            }
            return false;
        });

        // Update table with filtered results
        this.updateTableWithResults(filteredAnimals);

        // Store current search
        this.currentSearchTerm = searchTerm;
        this.currentSearchField = searchField;

        // Show notification
        const resultCount = filteredAnimals.length;
        console.log(`🔍 Found ${resultCount} newborn animals matching "${searchTerm}" in ${searchField}`);
    }

    /**
     * Hide dropdown
     */
    hideDropdown() {
        if (this.dropdownContainer) {
            this.dropdownContainer.style.opacity = '0';
            this.dropdownContainer.style.transform = 'translateY(-10px)';

            // Store timeout so it can be cleared if needed
            this.hideTimeout = setTimeout(() => {
                if (this.dropdownContainer && this.dropdownContainer.parentNode) {
                    this.dropdownContainer.parentNode.removeChild(this.dropdownContainer);
                }
                this.dropdownContainer = null;
                this.isVisible = false;
                this.hideTimeout = null;

                // Remove active class from search container
                const searchContainer = document.querySelector('.search-dropdown-container.active');
                if (searchContainer) {
                    searchContainer.classList.remove('active');
                }
            }, 300);
        }

        // Remove click outside listener
        document.removeEventListener('click', this.handleSearchOutsideClick.bind(this));
    }

    /**
     * Handle click outside search dropdown
     * @param {Event} e - Click event
     */
    handleSearchOutsideClick(e) {
        if (!this.isVisible || !this.dropdownContainer) return;

        const searchButton = document.querySelector('#searchButton');
        const searchContainer = document.querySelector('.search-dropdown-container');

        // Check if click is outside both dropdown and search button
        const isOutsideDropdown = !this.dropdownContainer.contains(e.target);
        const isOutsideButton = searchButton && !searchButton.contains(e.target);
        const isOutsideContainer = searchContainer && !searchContainer.contains(e.target);

        if (isOutsideDropdown && isOutsideButton && isOutsideContainer) {
            console.log('🔽 Dropdown hidden by outside click');
            this.hideDropdown();
        }
    }



    /**
     * Show dropdown with animation
     */
    showDropdownWithAnimation() {
        if (this.dropdownContainer) {
            this.isVisible = true;
            setTimeout(() => {
                this.dropdownContainer.style.opacity = '1';
                this.dropdownContainer.style.transform = 'translateY(0)';
            }, 10);
        }
    }
}

/**
 * Newborn Filter Dropdown Manager
 * Handles the dropdown menu for the Filter button on newborn page
 */
class NewbornFilterDropdownManager {
    constructor() {
        this.dropdownContainer = null;
        this.isVisible = false;
        this.currentFilters = {};
        this.hideTimeout = null;
    }

    /**
     * Show filter dropdown menu
     * @param {HTMLElement} buttonElement - Filter button element
     */
    showDropdown(buttonElement) {
        // If dropdown is already visible, don't do anything
        if (this.isVisible && this.dropdownContainer) {
            console.log('🔽 Filter dropdown already visible, ignoring');
            return;
        }

        // Clear any pending hide timeouts
        if (this.hideTimeout) {
            clearTimeout(this.hideTimeout);
            this.hideTimeout = null;
        }

        // Remove any existing dropdown immediately (no animation)
        if (this.dropdownContainer && this.dropdownContainer.parentNode) {
            this.dropdownContainer.parentNode.removeChild(this.dropdownContainer);
            this.dropdownContainer = null;
        }

        // Create dropdown
        this.createDropdown(buttonElement);
        this.showDropdownWithAnimation();
    }

    /**
     * Create filter dropdown menu
     * @param {HTMLElement} buttonElement - Filter button element
     */
    createDropdown(buttonElement) {
        this.dropdownContainer = document.createElement('div');
        this.dropdownContainer.className = 'filter-dropdown-container-dynamic';

        const buttonRect = buttonElement.getBoundingClientRect();

        this.dropdownContainer.innerHTML = `
            <div class="filter-dropdown-menu-dynamic">
                <div class="filter-section-dynamic">
                    <label>Gender:</label>
                    <select class="filter-select-dynamic" data-field="gender">
                        <option value="all">All Genders</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                    </select>
                </div>
                <div class="filter-section-dynamic">
                    <label>Weight Range:</label>
                    <select class="filter-select-dynamic" data-field="weight">
                        <option value="all">All Weights</option>
                        <option value="light">Light (&lt; 50kg)</option>
                        <option value="medium">Medium (50-100kg)</option>
                        <option value="heavy">Heavy (&gt; 100kg)</option>
                    </select>
                </div>
                <div class="filter-section-dynamic">
                    <label>Healthcare Status:</label>
                    <select class="filter-select-dynamic" data-field="healthcare">
                        <option value="all">All Status</option>
                        <option value="healthy">Healthy</option>
                        <option value="vaccinated">Vaccinated</option>
                        <option value="treatment">Under Treatment</option>
                    </select>
                </div>
                <div class="filter-actions-dynamic">
                    <button class="filter-apply-btn-dynamic">Apply Filters</button>
                    <button class="filter-clear-btn-dynamic">Clear All</button>
                </div>
            </div>
        `;

        // Position dropdown
        this.dropdownContainer.style.cssText = `
            position: fixed;
            top: ${buttonRect.bottom + 5}px;
            left: ${buttonRect.left}px;
            z-index: 9999;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        `;

        // Add styles
        this.addFilterDropdownStyles();

        // Add event listeners
        this.setupFilterDropdownEventListeners();

        // Set current filter values
        this.setCurrentFilterValues();

        // Add to document
        document.body.appendChild(this.dropdownContainer);
    }

    /**
     * Add CSS styles for filter dropdown
     */
    addFilterDropdownStyles() {
        if (!document.querySelector('#newborn-filter-dropdown-styles')) {
            const style = document.createElement('style');
            style.id = 'newborn-filter-dropdown-styles';
            style.textContent = `
                .filter-dropdown-menu-dynamic {
                    background: #aedf32;
                    border-radius: 14px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    border: 2px solid #8fb728;
                    padding: 16px;
                    min-width: 280px;
                }

                .filter-section-dynamic {
                    margin-bottom: 14px;
                }

                .filter-section-dynamic label {
                    display: block;
                    font-size: 14px;
                    font-weight: bold;
                    color: #ffffff;
                    margin-bottom: 6px;
                }

                .filter-select-dynamic {
                    width: 100%;
                    padding: 8px 12px;
                    border: 2px solid #8fb728;
                    border-radius: 8px;
                    font-size: 14px;
                    background-color: #ffffff;
                    color: #333;
                    outline: none;
                    transition: border-color 0.3s ease;
                }

                .filter-select-dynamic:focus {
                    outline: none;
                    border-color: #8fb728;
                    box-shadow: 0 0 0 2px rgba(174, 223, 50, 0.2);
                }

                .filter-actions-dynamic {
                    display: flex;
                    gap: 10px;
                    margin-top: 18px;
                }

                .filter-apply-btn-dynamic, .filter-clear-btn-dynamic {
                    flex: 1;
                    padding: 10px 14px;
                    border: none;
                    border-radius: 8px;
                    font-size: 14px;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.2s ease;
                }

                .filter-apply-btn-dynamic {
                    background: #aedf32;
                    color: #ffffff;
                    border: 2px solid #8fb728;
                }

                .filter-apply-btn-dynamic:hover {
                    background: #8fb728;
                    transform: translateY(-1px);
                }

                .filter-clear-btn-dynamic {
                    background: #ffffff;
                    color: #8fb728;
                    border: 2px solid #8fb728;
                }

                .filter-clear-btn-dynamic:hover {
                    background: #f0f0f0;
                    transform: translateY(-1px);
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * Setup event listeners for filter dropdown
     */
    setupFilterDropdownEventListeners() {
        if (!this.dropdownContainer) return;

        // Apply filters button
        const applyBtn = this.dropdownContainer.querySelector('.filter-apply-btn-dynamic');
        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.applyFilters();
                this.hideDropdown();
            });
        }

        // Clear filters button
        const clearBtn = this.dropdownContainer.querySelector('.filter-clear-btn-dynamic');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearFilters();
                this.hideDropdown();
            });
        }

        // Click outside to close (add delay to prevent immediate closure)
        setTimeout(() => {
            document.addEventListener('click', this.handleFilterOutsideClick.bind(this));
        }, 100);
    }

    /**
     * Set current filter values in dropdown
     */
    setCurrentFilterValues() {
        if (!this.dropdownContainer) return;

        const selects = this.dropdownContainer.querySelectorAll('.filter-select-dynamic');
        selects.forEach(select => {
            const field = select.getAttribute('data-field');
            if (this.currentFilters[field]) {
                select.value = this.currentFilters[field];
            }
        });
    }

    /**
     * Apply filters to newborn animals
     */
    applyFilters() {
        const selects = this.dropdownContainer.querySelectorAll('.filter-select-dynamic');
        const filters = {};

        selects.forEach(select => {
            const field = select.getAttribute('data-field');
            const value = select.value;
            if (value !== 'all') {
                filters[field] = value;
            }
        });

        this.currentFilters = filters;
        this.filterNewbornAnimals(filters);
        console.log('🔍 Applied filters:', filters);
    }

    /**
     * Filter newborn animals based on selected criteria
     * @param {Object} filters - Filter criteria
     */
    filterNewbornAnimals(filters) {
        const newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');

        const filteredAnimals = newbornAnimals.filter(animal => {
            // Gender filter
            if (filters.gender && animal.gender && animal.gender.toLowerCase() !== filters.gender.toLowerCase()) {
                return false;
            }

            // Weight filter
            if (filters.weight && animal.weight) {
                const weight = parseFloat(animal.weight);
                if (!isNaN(weight)) {
                    switch (filters.weight) {
                        case 'light':
                            if (weight >= 50) return false;
                            break;
                        case 'medium':
                            if (weight < 50 || weight > 100) return false;
                            break;
                        case 'heavy':
                            if (weight <= 100) return false;
                            break;
                    }
                }
            }

            // Healthcare filter
            if (filters.healthcare && animal.healthcare) {
                const healthcare = animal.healthcare.toLowerCase();
                switch (filters.healthcare) {
                    case 'healthy':
                        if (!healthcare.includes('healthy')) return false;
                        break;
                    case 'vaccinated':
                        if (!healthcare.includes('vaccinated')) return false;
                        break;
                    case 'treatment':
                        if (!healthcare.includes('treatment') && !healthcare.includes('antibiotics')) return false;
                        break;
                }
            }

            return true;
        });

        // Update table with filtered results
        updateTableWithResults(filteredAnimals);

        // Show notification
        const resultCount = filteredAnimals.length;
        console.log(`🔍 Found ${resultCount} newborn animals matching filters`);
    }

    /**
     * Clear all filters and show all newborn animals
     */
    clearFilters() {
        this.currentFilters = {};

        // Load all newborn animals
        const newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
        updateTableWithResults(newbornAnimals);

        console.log('🔄 Cleared all filters, showing all newborn animals');
    }

    /**
     * Handle click outside filter dropdown
     * @param {Event} e - Click event
     */
    handleFilterOutsideClick(e) {
        if (!this.isVisible || !this.dropdownContainer) return;

        const filterButton = document.querySelector('.frame-9:nth-child(4)');

        // Check if click is outside both dropdown and filter button
        const isOutsideDropdown = !this.dropdownContainer.contains(e.target);
        const isOutsideButton = filterButton && !filterButton.contains(e.target);

        if (isOutsideDropdown && isOutsideButton) {
            console.log('🔽 Filter dropdown hidden by outside click');
            this.hideDropdown();
        }
    }

    /**
     * Show dropdown with animation
     */
    showDropdownWithAnimation() {
        if (this.dropdownContainer) {
            this.isVisible = true;
            setTimeout(() => {
                this.dropdownContainer.style.opacity = '1';
                this.dropdownContainer.style.transform = 'translateY(0)';
            }, 10);
        }
    }

    /**
     * Hide dropdown with animation
     */
    hideDropdown() {
        if (this.dropdownContainer) {
            this.dropdownContainer.style.opacity = '0';
            this.dropdownContainer.style.transform = 'translateY(-10px)';

            // Store timeout so it can be cleared if needed
            this.hideTimeout = setTimeout(() => {
                if (this.dropdownContainer && this.dropdownContainer.parentNode) {
                    this.dropdownContainer.parentNode.removeChild(this.dropdownContainer);
                }
                this.dropdownContainer = null;
                this.isVisible = false;
                this.hideTimeout = null;
            }, 300);
        }

        // Remove click outside listener
        document.removeEventListener('click', this.handleFilterOutsideClick.bind(this));
    }
}

// ==================== ORIGINAL NEWBORN PAGE FUNCTIONALITY ====================

// Global variables
let newbornSearchDropdownManager;
let newbornFilterDropdownManager;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize dropdown managers
    newbornSearchDropdownManager = new NewbornSearchDropdownManager();
    newbornFilterDropdownManager = new NewbornFilterDropdownManager();
    console.log('🔽 Newborn Search & Filter Dropdown Managers initialized');

    // Get UI elements
    const addNewAnimalBtn = document.querySelector('.frame-9:nth-child(1)');
    const updateDataBtn = document.querySelector('.frame-9:nth-child(2)');
    const searchBtn = document.querySelector('#searchButton');
    const filterBtn = document.querySelector('.frame-9:nth-child(4)');
    const deleteAnimalBtn = document.querySelector('.frame-9:nth-child(5)');
    const tableBody = document.querySelector('tbody');

    // Initialize data and UI
    loadNewbornAnimals();
    initializeCharts();
    setupEventListeners();

    // Setup event listeners for buttons with popup integration
    function setupEventListeners() {
        if (addNewAnimalBtn) {
            addNewAnimalBtn.addEventListener('click', () => {
                // Show addnewanimal.html popup with newborn type pre-selected
                console.log('🔄 Add New Newborn button clicked');
                window.newbornPopupManager.showPopup('addnewanimal', {
                    prefilledData: { type: 'newborn' }
                });
            });
        }

        if (updateDataBtn) {
            updateDataBtn.addEventListener('click', () => {
                // Show updateanimal.html popup filtered to display only newborn animals
                console.log('🔄 Update Data button clicked');
                window.newbornPopupManager.showPopup('updateanimal', {
                    filterType: 'newborn'
                });
            });
        }

        if (searchBtn) {
            searchBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔍 Search button clicked - showing dropdown');
                newbornSearchDropdownManager.showDropdown(searchBtn);
            });
        }

        if (filterBtn) {
            filterBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🔍 Filter button clicked - showing dropdown');
                newbornFilterDropdownManager.showDropdown(filterBtn);
            });
        }

        if (deleteAnimalBtn) {
            deleteAnimalBtn.addEventListener('click', () => {
                // Show deletepop.html confirmation popup for selected newborn animals
                console.log('🔄 Delete button clicked');
                window.newbornPopupManager.showPopup('delete', {
                    filterType: 'newborn'
                });
            });
        }

        // Add checkbox event listeners
        const checkboxes = document.querySelectorAll('.checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    checkboxes.forEach(cb => { if (cb !== this) cb.checked = false; });
                }

                // Enable/disable delete button based on selection
                const anyChecked = document.querySelector('.checkbox:checked');
                if (deleteAnimalBtn) {
                    deleteAnimalBtn.disabled = !anyChecked;
                    deleteAnimalBtn.style.opacity = anyChecked ? 1 : 0.5;
                }
            });
        });
    }

    // Load newborn animals data
    function loadNewbornAnimals() {
        let newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');

        // Add sample data if none exists
        if (newbornAnimals.length === 0) {
            newbornAnimals = [
                {code: 'NB001', herdNumber: 'H123', gender: 'Male', birthDate: '2023-01-15',
                 weight: '35 kg', weightDate: '2023-01-16', healthcare: 'Vaccinated'},
                {code: 'NB002', herdNumber: 'H123', gender: 'Female', birthDate: '2023-02-20',
                 weight: '32 kg', weightDate: '2023-02-21', healthcare: 'Healthy'},
                {code: 'NB003', herdNumber: 'H124', gender: 'Male', birthDate: '2023-03-10',
                 weight: '38 kg', weightDate: '2023-03-11', healthcare: 'Antibiotics'},
                {code: 'NB004', herdNumber: 'H125', gender: 'Female', birthDate: '2023-04-05',
                 weight: '30 kg', weightDate: '2023-04-06', healthcare: 'Healthy'}
            ];
            localStorage.setItem('newbornAnimals', JSON.stringify(newbornAnimals));
        }

        // Clear and populate table
        tableBody.innerHTML = '';
        newbornAnimals.forEach(animal => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input class="checkbox" type="checkbox">${animal.code}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${animal.herdNumber || 'N/A'}</td>
                <td>${animal.gender || 'N/A'}</td>
                <td>${formatDate(animal.birthDate)}</td>
                <td>${animal.weight || 'N/A'}</td>
                <td>${formatDate(animal.weightDate)}</td>
                <td>${animal.healthcare || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });

        setupEventListeners();
    }

    // Format date for display
    function formatDate(dateString) {
        if (!dateString) return 'N/A';
        const date = new Date(dateString);
        return isNaN(date.getTime()) ? dateString :
               date.toLocaleDateString('en-US', {year: 'numeric', month: '2-digit', day: '2-digit'});
    }

    // Show search dialog
    function showSearchDialog() {
        const dialog = createDialog('Search Newborn Animals');

        dialog.content.innerHTML = `
            <div class="form-group">
                <label for="searchField">Search by:</label>
                <select id="searchField">
                    <option value="code">Code</option>
                    <option value="herdNumber">Herd Number</option>
                    <option value="gender">Gender</option>
                    <option value="healthcare">Healthcare</option>
                </select>
            </div>
            <div class="form-group">
                <label for="searchValue">Search value:</label>
                <input type="text" id="searchValue">
            </div>
        `;

        dialog.confirmBtn.textContent = 'Search';
        dialog.confirmBtn.addEventListener('click', function() {
            const field = document.getElementById('searchField').value;
            const value = document.getElementById('searchValue').value.trim().toLowerCase();

            if (!value) return alert('Please enter a search value');

            const animals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
            const filtered = animals.filter(animal =>
                String(animal[field] || '').toLowerCase().includes(value)
            );

            updateTableWithResults(filtered);
            dialog.close();
        });

        dialog.show();
    }



    // Create a reusable dialog
    function createDialog(title) {
        // Create elements
        const overlay = document.createElement('div');
        const dialogEl = document.createElement('div');
        const content = document.createElement('div');
        const header = document.createElement('h3');
        const buttonContainer = document.createElement('div');
        const confirmBtn = document.createElement('button');
        const cancelBtn = document.createElement('button');

        // Set styles
        overlay.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:999;';
        dialogEl.style.cssText = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:white;padding:20px;border-radius:5px;box-shadow:0 0 10px rgba(0,0,0,0.3);z-index:1000;min-width:300px;';
        header.style.cssText = 'margin-top:0;margin-bottom:15px;';
        buttonContainer.style.cssText = 'display:flex;justify-content:space-between;margin-top:20px;';
        confirmBtn.style.cssText = 'padding:8px 15px;background-color:#32cd32;color:white;border:none;border-radius:4px;cursor:pointer;';
        cancelBtn.style.cssText = 'padding:8px 15px;background-color:#f1f1f1;border:1px solid #ddd;border-radius:4px;cursor:pointer;';

        // Set content
        header.textContent = title;
        cancelBtn.textContent = 'Cancel';

        // Build structure
        buttonContainer.appendChild(confirmBtn);
        buttonContainer.appendChild(cancelBtn);
        dialogEl.appendChild(header);
        dialogEl.appendChild(content);
        dialogEl.appendChild(buttonContainer);

        // Add CSS for form elements
        const style = document.createElement('style');
        style.textContent = `
            .form-group { margin-bottom: 15px; }
            .form-group label { display: block; margin-bottom: 5px; }
            .form-group select, .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        `;
        dialogEl.appendChild(style);

        // Close function
        function close() {
            document.body.removeChild(overlay);
            document.body.removeChild(dialogEl);
        }

        // Event listeners
        cancelBtn.addEventListener('click', close);

        // Return dialog object
        return {
            content,
            confirmBtn,
            cancelBtn,
            show: function() {
                document.body.appendChild(overlay);
                document.body.appendChild(dialogEl);
            },
            close
        };
    }

    // Update table with filtered results
    function updateTableWithResults(animals) {
        tableBody.innerHTML = '';

        if (animals.length === 0) {
            tableBody.innerHTML = `<tr><td colspan="7" style="text-align:center;padding:20px;">No matching records found</td></tr>`;
            return;
        }

        animals.forEach(animal => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <div>
                        <form>
                            <label class="selectbox">
                                <input class="checkbox" type="checkbox">${animal.code}
                            </label>
                        </form>
                    </div>
                </td>
                <td>${animal.herdNumber || 'N/A'}</td>
                <td>${animal.gender || 'N/A'}</td>
                <td>${formatDate(animal.birthDate)}</td>
                <td>${animal.weight || 'N/A'}</td>
                <td>${formatDate(animal.weightDate)}</td>
                <td>${animal.healthcare || 'N/A'}</td>
            `;
            tableBody.appendChild(row);
        });

        setupEventListeners();
    }

    // Delete selected animals
    function deleteSelectedAnimals() {
        const selected = document.querySelectorAll('.checkbox:checked');

        if (selected.length === 0) return alert('Please select at least one animal to delete');
        if (!confirm(`Are you sure you want to delete ${selected.length} selected animal(s)?`)) return;

        const selectedCodes = Array.from(selected).map(cb => cb.nextSibling.textContent.trim());
        let animals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
        animals = animals.filter(animal => !selectedCodes.includes(animal.code));

        localStorage.setItem('newbornAnimals', JSON.stringify(animals));
        loadNewbornAnimals();
        updateCharts();

        alert('Selected animal(s) deleted successfully');
    }

    // Initialize charts
    function initializeCharts() {
        try {
            // Monthly chart
            const monthlyCanvas = document.getElementById('newBornMonthlyChart');
            if (monthlyCanvas) {
                const monthlyCtx = monthlyCanvas.getContext('2d');
                window.monthlyChart = new Chart(monthlyCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                        datasets: [{
                            label: 'Number of Newborns',
                            data: getMonthlyData(),
                            backgroundColor: '#aedf32',
                            borderColor: '#aedf32',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        indexAxis: 'y',
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: { x: { beginAtZero: true, max: 100 } }
                    }
                });
            }

            // Types chart
            const typesCanvas = document.getElementById('newbornTypesChart');
            if (typesCanvas) {
                const typesCtx = typesCanvas.getContext('2d');
                window.typesChart = new Chart(typesCtx, {
                    type: 'bar',
                    data: {
                        labels: ['Fattening', 'Dairy'],
                        datasets: [
                            { label: '2022', data: [30, 60], backgroundColor: '#8fb728' },
                            { label: '2023', data: [45, 75], backgroundColor: '#4a90e2' },
                            { label: '2024', data: [70, 50], backgroundColor: '#f5a623' },
                            { label: '2025', data: [20, 40], backgroundColor: '#d0021b' }
                        ]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: { y: { beginAtZero: true, max: 100 } }
                    }
                });
            }
        } catch (error) {
            console.error("Error initializing charts:", error);
        }
    }

    // Update charts with current data
    function updateCharts() {
        if (window.monthlyChart) {
            window.monthlyChart.data.datasets[0].data = getMonthlyData();
            window.monthlyChart.update();
        }
    }

    // Get monthly data from stored newborns
    function getMonthlyData() {
        const animals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
        const monthlyData = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0];

        animals.forEach(animal => {
            if (animal.birthDate) {
                const birthDate = new Date(animal.birthDate);
                if (!isNaN(birthDate.getTime())) {
                    const month = birthDate.getMonth();
                    monthlyData[month]++;
                }
            }
        });

        return monthlyData;
    }

    // Simple function to hide all search inputs
    function hideAllSearchInputs() {
        const containers = ['codeSearchContainer', 'herdNumberSearchContainer', 'genderSearchContainer'];
        containers.forEach(containerId => {
            const container = document.getElementById(containerId);
            if (container) {
                container.style.display = 'none';
            }
        });

        // Remove expanded class from dropdown
        const searchDropdown = document.getElementById('searchDropdown');
        if (searchDropdown) {
            searchDropdown.classList.remove('expanded');
        }
    }
});

// ==================== GLOBAL FUNCTIONS FOR HTML ONCLICK HANDLERS ====================

/**
 * Global function for newborn search (called from HTML onclick)
 * @param {string} searchType - Type of search
 */
window.performNewbornSearch = function(searchType) {
    console.log(`🔍 performNewbornSearch called with type: ${searchType}`);

    const inputField = document.getElementById(`${searchType}SearchInput`);
    if (!inputField) {
        console.error(`❌ Input field not found for search type: ${searchType}`);
        return;
    }

    const searchTerm = inputField.value.trim();
    if (!searchTerm) {
        // Show error by highlighting the input field
        inputField.style.borderColor = '#ff6b6b';
        setTimeout(() => {
            inputField.style.borderColor = '#c1d8b9';
        }, 1000);
        return;
    }

    // Perform the search
    searchNewbornAnimals(searchTerm, searchType);

    // Hide dropdown completely
    const searchDropdown = document.getElementById('searchDropdown');
    const searchContainer = document.querySelector('.search-dropdown-container');
    if (searchDropdown && searchContainer) {
        searchDropdown.classList.remove('show');
        searchContainer.classList.remove('active');
    }
    hideAllSearchInputs();

    // Clear input
    inputField.value = '';
};

/**
 * Global function to cancel newborn search (called from HTML onclick)
 */
window.cancelNewbornSearch = function() {
    console.log('🔄 cancelNewbornSearch called');

    // Hide dropdown completely
    const searchDropdown = document.getElementById('searchDropdown');
    const searchContainer = document.querySelector('.search-dropdown-container');
    if (searchDropdown && searchContainer) {
        searchDropdown.classList.remove('show');
        searchContainer.classList.remove('active');
    }
    hideAllSearchInputs();

    // Clear all inputs
    clearAllSearchInputs();
};

/**
 * Search newborn animals by field
 * @param {string} searchTerm - Search term
 * @param {string} searchField - Field to search in
 */
function searchNewbornAnimals(searchTerm, searchField) {
    const newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
    const filteredAnimals = newbornAnimals.filter(animal => {
        const fieldValue = animal[searchField];
        if (fieldValue) {
            return fieldValue.toString().toLowerCase().includes(searchTerm.toLowerCase());
        }
        return false;
    });

    // Update table with filtered results
    updateTableWithResults(filteredAnimals);

    // Show notification
    const resultCount = filteredAnimals.length;
    console.log(`🔍 Found ${resultCount} newborn animals matching "${searchTerm}" in ${searchField}`);
}

/**
 * Update table with search results
 * @param {Array} animals - Filtered animals array
 */
function updateTableWithResults(animals) {
    const tableBody = document.querySelector('tbody');
    if (!tableBody) return;

    // Clear and populate table
    tableBody.innerHTML = '';

    if (animals.length === 0) {
        // Show no results message
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="7" style="text-align: center; padding: 20px; color: #666;">
                No matching newborn animals found
            </td>
        `;
        tableBody.appendChild(row);
        return;
    }

    animals.forEach(animal => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div>
                    <form>
                        <label class="selectbox">
                            <input class="checkbox" type="checkbox">${animal.code}
                        </label>
                    </form>
                </div>
            </td>
            <td>${animal.herdNumber || 'N/A'}</td>
            <td>${animal.gender || 'N/A'}</td>
            <td>${formatDate(animal.birthDate)}</td>
            <td>${animal.weight || 'N/A'}</td>
            <td>${formatDate(animal.weightDate)}</td>
            <td>${animal.healthcare || 'N/A'}</td>
        `;
        tableBody.appendChild(row);
    });

    // Re-setup event listeners for new checkboxes
    setupCheckboxEventListeners();
}

/**
 * Format date for display
 * @param {string} dateString - Date string to format
 * @returns {string} - Formatted date
 */
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return isNaN(date.getTime()) ? dateString :
           date.toLocaleDateString('en-US', {year: 'numeric', month: '2-digit', day: '2-digit'});
}

/**
 * Setup checkbox event listeners
 */
function setupCheckboxEventListeners() {
    const checkboxes = document.querySelectorAll('.checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            if (this.checked) {
                checkboxes.forEach(cb => { if (cb !== this) cb.checked = false; });
            }

            // Enable/disable delete button based on selection
            const anyChecked = document.querySelector('.checkbox:checked');
            const deleteAnimalBtn = document.querySelector('.frame-9:nth-child(5)');
            if (deleteAnimalBtn) {
                deleteAnimalBtn.disabled = !anyChecked;
                deleteAnimalBtn.style.opacity = anyChecked ? 1 : 0.5;
            }
        });
    });
}

/**
 * Hide all search input containers
 */
function hideAllSearchInputs() {
    const containers = ['codeSearchContainer', 'herdNumberSearchContainer', 'genderSearchContainer'];
    containers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container) {
            container.style.display = 'none';
        }
    });
}

/**
 * Clear all search input fields
 */
function clearAllSearchInputs() {
    const inputs = ['codeSearchInput', 'herdNumberSearchInput', 'genderSearchInput'];
    inputs.forEach(inputId => {
        const input = document.getElementById(inputId);
        if (input) {
            input.value = '';
        }
    });
}

/**
 * Show search input for specific type (called from HTML onclick)
 * @param {string} searchType - Type of search input to show
 * @param {Event} event - Click event
 */
window.showSearchInput = function(searchType, event) {
    console.log(`🔍 showSearchInput called with type: ${searchType}`);

    // Prevent dropdown from closing when clicking inside
    if (event) {
        event.stopPropagation();
    }

    // Hide all input containers first
    hideAllSearchInputs();

    // Remove active class from all options
    const searchOptions = document.querySelectorAll('.search-option');
    searchOptions.forEach(option => option.classList.remove('active'));

    // Show the selected input container
    const inputContainer = document.getElementById(`${searchType}SearchContainer`);
    if (inputContainer) {
        inputContainer.style.display = 'block';

        // Add expanded class to dropdown
        const searchDropdown = document.getElementById('searchDropdown');
        if (searchDropdown) {
            searchDropdown.classList.add('expanded');
        }

        // Focus on the input field
        const inputField = document.getElementById(`${searchType}SearchInput`);
        if (inputField) {
            setTimeout(() => inputField.focus(), 100);
        }

        // Find and activate the corresponding option
        const searchOption = document.querySelector(`.search-option[onclick*="${searchType}"]`);
        if (searchOption) {
            searchOption.classList.add('active');
        }
    }
};

/**
 * Clear newborn search and show all animals (called from HTML onclick)
 */
window.clearNewbornSearch = function() {
    console.log('🔄 clearNewbornSearch called');

    // Load all newborn animals
    const newbornAnimals = JSON.parse(localStorage.getItem('newbornAnimals') || '[]');
    updateTableWithResults(newbornAnimals);

    // Hide dropdown completely
    const searchDropdown = document.getElementById('searchDropdown');
    const searchContainer = document.querySelector('.search-dropdown-container');
    if (searchDropdown && searchContainer) {
        searchDropdown.classList.remove('show');
        searchContainer.classList.remove('active');
    }
    hideAllSearchInputs();

    // Clear all inputs
    clearAllSearchInputs();
};

/**
 * Global function to hide all search inputs (accessible from anywhere)
 */
function hideAllSearchInputs() {
    const containers = ['codeSearchContainer', 'herdNumberSearchContainer', 'genderSearchContainer'];
    containers.forEach(containerId => {
        const container = document.getElementById(containerId);
        if (container) {
            container.style.display = 'none';
        }
    });

    // Remove expanded class from dropdown
    const searchDropdown = document.getElementById('searchDropdown');
    if (searchDropdown) {
        searchDropdown.classList.remove('expanded');
    }
}

// ==================== CONSOLE STARTUP MESSAGE ====================

console.log(`
🎉 NEWBORN MANAGEMENT SYSTEM WITH POPUP INTEGRATION LOADED
📋 Features Available:
   • Add New Newborn Animals (popup-based with pre-selected type)
   • Update Newborn Data (filtered for newborn animals only)
   • Search Newborn Animals (newborn-specific filtering)
   • Filter Newborn Data (configured for newborn data)
   • Delete Newborn Animals (confirmation popup)
   • External Green Close Button (#aedf32)
   • PostMessage Communication (cross-origin safe)
   • Responsive Popup Design (85vw × 90vh)
   • Preserved Charts and Graphs Functionality

🔧 Architecture:
   • NewbornPopupManager (popup management - identical to AnimalPopupManager)
   • Same iframe-based popup system
   • Same external green close button
   • Same postMessage communication
   • Same responsive design (no visible frames/borders)
   • Popups appear above page content (like animal.js)
   • Only popup and close button visible during popup display

✅ Ready for newborn animal management with popup functionality!
`);

// ==================== GLOBAL FUNCTIONS AND POSTMESSAGE COMMUNICATION ====================

/**
 * Global function to show newborn popup (utility function)
 * Can be called from anywhere in the application (identical to animal.js)
 * @param {string} popupType - Type of popup to show
 * @param {Object} options - Popup options
 */
window.showNewbornPopup = function(popupType, options = {}) {
    if (window.newbornPopupManager) {
        window.newbornPopupManager.showPopup(popupType, options);
    } else {
        console.error('Newborn Popup Manager not initialized');
    }
};

/**
 * Close newborn popup (utility function) - Can be called from iframe content
 * This is the main method that iframe popups should use to close themselves (identical to animal.js)
 */
window.closeNewbornPopup = function() {
    console.log('🔴 Global close newborn popup called');
    if (window.newbornPopupManager) {
        window.newbornPopupManager.hidePopup();
        console.log('✅ Newborn popup closed via global method');
    } else {
        console.error('Newborn Popup Manager not initialized');
    }
};

/**
 * Listen for postMessage from iframe to close popup (identical to animal.js)
 * This solves cross-origin restrictions with file:// URLs
 */
window.addEventListener('message', function(event) {
    console.log('📨 PostMessage received in newborn page:', event.data);

    if (event.data && event.data.action === 'closePopup') {
        console.log('🔴 PostMessage close newborn popup request received');

        try {
            // Try to close via popup manager
            if (window.newbornPopupManager) {
                window.newbornPopupManager.hidePopup();
                console.log('✅ PostMessage: Newborn popup closed via manager');
            } else {
                // Fallback: Direct DOM manipulation
                const popupOverlay = document.querySelector('.newborn-popup-overlay');
                if (popupOverlay) {
                    popupOverlay.style.display = 'none';
                    console.log('✅ PostMessage: Newborn popup closed via direct DOM');
                } else {
                    console.warn('⚠️ PostMessage: No newborn popup overlay found');
                }
            }
        } catch (error) {
            console.error('❌ PostMessage close error:', error);
        }
    }
});

// ==================== CONSOLE STARTUP MESSAGE ====================

console.log(`
🎉 NEWBORN MANAGEMENT SYSTEM WITH POPUP INTEGRATION LOADED
📋 Features Available:
   • Add New Newborn Animals (popup-based with pre-selected type)
   • Update Newborn Data (filtered for newborn animals only)
   • Search Newborn Animals (newborn-specific filtering)
   • Filter Newborn Data (configured for newborn data)
   • Delete Newborn Animals (confirmation popup)
   • External Green Close Button (#aedf32)
   • PostMessage Communication (cross-origin safe)
   • Data Consistency with Main Animal System
   • Responsive Popup Design (85vw × 90vh)
   • Preserved Charts and Graphs Functionality

🔧 Architecture:
   • NewbornPopupManager (popup management - identical to AnimalPopupManager)
   • Same OOP patterns as animal.js
   • Same iframe-based popup system
   • Same external green close button
   • Same postMessage communication
   • Same responsive design (no visible frames/borders)
   • Popups appear above page content (like animal.js)
   • Only popup and close button visible during popup display

✅ Ready for newborn animal management with identical popup functionality to animal.js!
`);