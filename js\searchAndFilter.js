/**
 * SearchAndFilter class
 * This file contains the implementation of the Search and Filter functionality
 */

class SearchAndFilter {
    constructor() {
        this.searchInput = document.querySelector('input.search');
        this.filterButton = document.querySelector('.frame-4 button:nth-child(4)');
        this.filterDropdown = null;
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Search functionality
        if (this.searchInput) {
            this.searchInput.addEventListener('input', () => this.performSearch());
        }
        
        // Filter functionality
        if (this.filterButton) {
            this.filterButton.addEventListener('click', () => this.toggleFilterDropdown());
        }
    }
    
    // Perform search on table
    performSearch() {
        const searchTerm = this.searchInput.value.toLowerCase();
        const rows = document.querySelectorAll('table tbody tr');
        
        rows.forEach(row => {
            let found = false;
            // Search in code, type, herd number, and weight columns
            for (let i = 0; i < 5; i++) {
                if (row.cells[i] && row.cells[i].textContent.toLowerCase().includes(searchTerm)) {
                    found = true;
                    break;
                }
            }
            
            row.style.display = found ? '' : 'none';
        });
    }
    
    // Toggle filter dropdown
    toggleFilterDropdown() {
        if (this.filterDropdown) {
            document.body.removeChild(this.filterDropdown);
            this.filterDropdown = null;
            return;
        }
        
        // Create dropdown
        this.filterDropdown = document.createElement('div');
        this.filterDropdown.className = 'filter-dropdown';
        this.filterDropdown.style.position = 'absolute';
        this.filterDropdown.style.top = `${this.filterButton.getBoundingClientRect().bottom}px`;
        this.filterDropdown.style.left = `${this.filterButton.getBoundingClientRect().left}px`;
        this.filterDropdown.style.backgroundColor = this.filterButton.style.backgroundColor || '#f0f0f0';
        this.filterDropdown.style.border = '1px solid #ccc';
        this.filterDropdown.style.borderRadius = '4px';
        this.filterDropdown.style.padding = '8px';
        this.filterDropdown.style.zIndex = '100';
        
        // Add filter options
        const options = ['Herd Number', 'Weight', 'Gender', 'Type'];
        options.forEach(option => {
            const optionElement = document.createElement('div');
            optionElement.textContent = option;
            optionElement.style.padding = '8px';
            optionElement.style.cursor = 'pointer';
            optionElement.addEventListener('click', () => this.applyFilter(option));
            optionElement.addEventListener('mouseover', () => {
                optionElement.style.backgroundColor = '#e0e0e0';
            });
            optionElement.addEventListener('mouseout', () => {
                optionElement.style.backgroundColor = 'transparent';
            });
            this.filterDropdown.appendChild(optionElement);
        });
        
        document.body.appendChild(this.filterDropdown);
        
        // Close dropdown when clicking outside
        document.addEventListener('click', this.handleOutsideClick.bind(this));
    }
    
    // Handle click outside dropdown
    handleOutsideClick(event) {
        if (this.filterDropdown && !this.filterDropdown.contains(event.target) && event.target !== this.filterButton) {
            document.body.removeChild(this.filterDropdown);
            this.filterDropdown = null;
            document.removeEventListener('click', this.handleOutsideClick.bind(this));
        }
    }
    
    // Apply filter
    applyFilter(filterType) {
        const rows = Array.from(document.querySelectorAll('table tbody tr'));
        
        // Determine column index based on filter type
        let columnIndex;
        switch (filterType) {
            case 'Type': columnIndex = 1; break;
            case 'Herd Number': columnIndex = 2; break;
            case 'Gender': columnIndex = 3; break;
            case 'Weight': columnIndex = 4; break;
            default: columnIndex = 0;
        }
        
        // Sort rows
        rows.sort((a, b) => {
            const aValue = a.cells[columnIndex].textContent;
            const bValue = b.cells[columnIndex].textContent;
            
            // For weight, convert to number
            if (filterType === 'Weight') {
                return parseFloat(aValue) - parseFloat(bValue);
            }
            
            return aValue.localeCompare(bValue);
        });
        
        // Update table
        const tableBody = document.querySelector('table tbody');
        rows.forEach(row => tableBody.appendChild(row));
        
        // Close dropdown
        if (this.filterDropdown) {
            document.body.removeChild(this.filterDropdown);
            this.filterDropdown = null;
        }
    }
}
