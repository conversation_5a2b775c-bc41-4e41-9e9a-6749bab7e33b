<DocTYPE html>
    <html>

    <head>
        <meta charset="utf-8" />

        <link rel="stylesheet" href="css/ingrediants.css" />
        <link rel="stylesheet" href="css/navigation-components.css" />
    </head>

    <body>

        <div class="feed">

            <!-- Include the sidebar and navbar components -->
            <div class="page-container">
                <!-- Sidebar Component -->
                <div class="side-bar">
                    <div class="s-r-a-parent-wrapper">
                        <div class="sra-parent">
                            <h1 class="sra">SRA</h1>
                            <div class="smart-raising-animal-wrapper">
                                <div class="smart-raising-animal">Smart Raising Animal</div>
                            </div>
                        </div>
                    </div>
                    <div class="side-bar-bottom">
                        <div class="side-bar-bottom-inner">
                            <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
                        </div>
                        <div class="side-bar-elements">
                            <div class="side-bar-options">
                                <div class="side-bar-option-parent">
                                    <div class="vuesaxlinearcategory-2-parent">
                                        <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt=""
                                            src="./public/vuesaxlinearcategory21.svg" />
                                        <a class="animals" href="index.html">Dashboard</a>
                                    </div>
                                </div>
                                <div class="side-bar-option-parent-inner">
                                    <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
                                </div>
                                <div class="side-bar-option-parent1">
                                    <div class="side-bar-element">
                                        <div class="side-animal">
                                            <img class="bull-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                                            <div class="animals-wrapper">
                                                <a class="animals" href="animal.html">Animals</a>
                                            </div>
                                        </div>
                                        <div class="side-animal">
                                            <img class="bull-icon" loading="lazy" alt=""
                                                src="./public/<EMAIL>" />
                                            <a class="animals" href="dairy.html">Dairy</a>
                                        </div>
                                        <div class="side-animal">
                                            <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                                            <a class="animals" href="newborn.html">New Born</a>
                                        </div>
                                        <div class="side-animal">
                                            <img class="cow-1-icon" loading="lazy" alt=""
                                                src="./public/<EMAIL>" />
                                            <a class="animals" href="feed.html">Feed</a>
                                        </div>
                                        <div class="side-animal">
                                            <img class="cow-1-icon" loading="lazy" alt=""
                                                src="./public/<EMAIL>" />
                                            <a class="animals" href="ingrediants.html">Ingredients</a>
                                        </div>
                                        <div class="side-animal">
                                            <div class="side-animal">
                                                <img class="cow-1-icon" loading="lazy" alt=""
                                                    src="./public/<EMAIL>" />
                                                <a class="animals" href="vaccination.html">Vaccination</a>
                                            </div>
                                        </div>
                                        <div class="side-animal">
                                            <img class="cow-1-icon" loading="lazy" alt=""
                                                src="./public/<EMAIL>" />
                                            <a class="animals" href="reports.html">Reports</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Navbar Component -->
                <header class="navbar">
                    <img class="notification-bell-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <div class="nav-profile" id="navProfileContainer">
                        <img class="male-avatar-portrait-of-a-youn-icon" loading="lazy" alt=""
                            src="./public/<EMAIL>" />
                        <div class="side-pregnant">
                            <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt=""
                                src="./public/vuesaxlineararrowdown.svg" />
                        </div>
                    </div>
                </header>


                <!-- Your page content goes here -->
                <main class="content-container">
                    <!-- Page specific content -->



                    <div class="frame-4">


                        <button class="frame-9">

                            <div class="btntext">Add New Ingrediants</div>
                        </button>

                        <button class="search">
                            <img class="update" src="icons/search.svg">
                            <div class="btntext1">Search</div>
                        </button>

                        <button class="frame-9">


                            <img class="update" src="icons/filter.svg">
                            <div class="btntext">Filter</div>
                        </button>

                        <button class="frame-9">

                            <div class="btntext">Edit Ingrediants</div>
                        </button>

                        <button class="frame-9">

                            <div class="btntext">Delete Ingrediants</div>
                        </button>



                    </div>

                    <div class="parent1">
                        <div class="frame-5">
                            <img class="update" src="icons/ingrediant icon.png">

                            <div class="text-wrapper-5">Ingrediants</div>
                        </div>


                        <div class="frame-7">
                            <div class="text-wrapper-6">season</div>
                            <form class="frame-11">
                                <div class="raddiv"> <input type="radio" name="access" id="winter" class="radio">
                                    <label for="winter">Winter</label>
                                </div>
                                <div class="raddiv"> <input type="radio" name="access" id="summer" class="radio">
                                    <label for="summer">Summer</label>
                                </div>

                            </form>
                        </div>                        <div class="tablecontainer">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Count</th>
                                        <th>Name</th>
                                        <th>Type</th>
                                        <th>Proteins</th>
                                        <th>CF</th>
                                        <th>TDN</th>
                                        <th>ME</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Table content will be dynamically populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
            <!-- Copy the profile popup component here -->
            <!-- Profile Popup -->
            <div id="navProfilePopup" class="popup-overlay1" style="display: none">
                <div class="nav-profile1">
                    <div class="component-13">
                        <a class="log-out" href="logout.html">Log Out</a>
                    </div>
                    <div class="component-131">
                        <a class="log-out1" href="setting.html">Settings</a>
                    </div>
                    <div class="component-14">
                        <a class="log-out2" href="about-us.html">About</a>
                    </div>
                </div>
            </div>


            <script src="js/navigation-components.js"></script>

            <script src="js/ingrediants.js"></script>

    </body>

    </html>