document.addEventListener('DOMContentLoaded', function() {
  // Get form elements
  const currentPasswordInput = document.querySelector('input[placeholder="Current Password"]');
  const newPasswordInput = document.querySelector('input[placeholder="New Password"]');
  const confirmPasswordInput = document.querySelector('input[placeholder="Confirm Password"]');
  const backButton = document.getElementById('backLeftContainer');
  const saveButton = document.getElementById('saveContainer');
  
  // Add password visibility toggles
  addPasswordToggles();
  
  // Add password strength indicator
  addPasswordStrengthIndicator();
  
  // Set up event listeners
  setupEventListeners();
  
  /**
   * Add password visibility toggle buttons to password fields
   */
  function addPasswordToggles() {
    const passwordFields = [currentPasswordInput, newPasswordInput, confirmPasswordInput];
    
    passwordFields.forEach(field => {
      if (!field) return;
      
      // Create container for the field and toggle button
      const container = document.createElement('div');
      container.style.position = 'relative';
      container.style.width = '100%';
      
      // Get the parent of the field
      const parent = field.parentNode;
      
      // Replace the field with the container
      parent.replaceChild(container, field);
      
      // Add the field to the container
      container.appendChild(field);
      
      // Create toggle button
      const toggleButton = document.createElement('button');
      toggleButton.type = 'button';
      toggleButton.innerHTML = '👁️';
      toggleButton.style.position = 'absolute';
      toggleButton.style.right = '10px';
      toggleButton.style.top = '50%';
      toggleButton.style.transform = 'translateY(-50%)';
      toggleButton.style.background = 'none';
      toggleButton.style.border = 'none';
      toggleButton.style.cursor = 'pointer';
      toggleButton.style.fontSize = '16px';
      toggleButton.style.opacity = '0.6';
      toggleButton.style.padding = '0';
      
      // Add toggle button to container
      container.appendChild(toggleButton);
      
      // Add event listener to toggle button
      toggleButton.addEventListener('click', function() {
        if (field.type === 'password') {
          field.type = 'text';
          toggleButton.style.opacity = '1';
        } else {
          field.type = 'password';
          toggleButton.style.opacity = '0.6';
        }
      });
    });
  }
  
  /**
   * Add password strength indicator for the new password field
   */
  function addPasswordStrengthIndicator() {
    if (!newPasswordInput) return;
    
    // Create strength indicator elements
    const strengthContainer = document.createElement('div');
    strengthContainer.style.marginTop = '5px';
    strengthContainer.style.fontSize = '12px';
    
    const strengthText = document.createElement('span');
    strengthText.textContent = 'Password strength: ';
    
    const strengthValue = document.createElement('span');
    strengthValue.textContent = 'Type a password';
    strengthValue.style.fontWeight = 'bold';
    
    const strengthBar = document.createElement('div');
    strengthBar.style.height = '5px';
    strengthBar.style.width = '100%';
    strengthBar.style.backgroundColor = '#e0e0e0';
    strengthBar.style.marginTop = '3px';
    strengthBar.style.borderRadius = '2px';
    
    const strengthBarFill = document.createElement('div');
    strengthBarFill.style.height = '100%';
    strengthBarFill.style.width = '0%';
    strengthBarFill.style.backgroundColor = '#ccc';
    strengthBarFill.style.borderRadius = '2px';
    strengthBarFill.style.transition = 'width 0.3s, background-color 0.3s';
    
    // Assemble the elements
    strengthContainer.appendChild(strengthText);
    strengthContainer.appendChild(strengthValue);
    strengthContainer.appendChild(strengthBar);
    strengthBar.appendChild(strengthBarFill);
    
    // Add the strength indicator after the new password field
    newPasswordInput.parentNode.parentNode.appendChild(strengthContainer);
    
    // Add event listener to update strength indicator
    newPasswordInput.addEventListener('input', function() {
      const password = this.value;
      const strength = calculatePasswordStrength(password);
      
      // Update strength bar
      strengthBarFill.style.width = `${strength.score * 25}%`;
      
      // Update strength text and color
      strengthValue.textContent = strength.label;
      
      switch (strength.label) {
        case 'Weak':
          strengthBarFill.style.backgroundColor = '#ff4d4d';
          strengthValue.style.color = '#ff4d4d';
          break;
        case 'Medium':
          strengthBarFill.style.backgroundColor = '#ffa64d';
          strengthValue.style.color = '#ffa64d';
          break;
        case 'Strong':
          strengthBarFill.style.backgroundColor = '#4dff4d';
          strengthValue.style.color = '#4dff4d';
          break;
        case 'Very Strong':
          strengthBarFill.style.backgroundColor = '#4d4dff';
          strengthValue.style.color = '#4d4dff';
          break;
        default:
          strengthBarFill.style.backgroundColor = '#ccc';
          strengthValue.style.color = '#ccc';
      }
    });
  }
  
  /**
   * Calculate password strength
   * @param {string} password - The password to check
   * @returns {Object} Object containing score (0-4) and label
   */
  function calculatePasswordStrength(password) {
    if (!password) {
      return { score: 0, label: 'Type a password' };
    }
    
    let score = 0;
    
    // Length check
    if (password.length >= 8) score += 1;
    if (password.length >= 12) score += 1;
    
    // Character variety checks
    if (/[A-Z]/.test(password)) score += 1;
    if (/[a-z]/.test(password)) score += 1;
    if (/[0-9]/.test(password)) score += 1;
    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    
    // Normalize score to 0-4 range
    score = Math.min(Math.floor(score / 1.5), 4);
    
    // Get label based on score
    let label;
    switch (score) {
      case 0:
        label = 'Too Weak';
        break;
      case 1:
        label = 'Weak';
        break;
      case 2:
        label = 'Medium';
        break;
      case 3:
        label = 'Strong';
        break;
      case 4:
        label = 'Very Strong';
        break;
      default:
        label = 'Unknown';
    }
    
    return { score, label };
  }
  
  /**
   * Set up event listeners for the change password page
   */
  function setupEventListeners() {
    // Back button click handler
    if (backButton) {
      backButton.addEventListener('click', function() {
        // Check if any fields have been filled
        if (currentPasswordInput.value || newPasswordInput.value || confirmPasswordInput.value) {
          const confirmLeave = confirm('You have unsaved changes. Are you sure you want to leave?');
          if (!confirmLeave) {
            return; // Stay on the page
          }
        }
        
        // Navigate back to edit profile page
        window.location.href = 'edit1.html';
      });
    }
    
    // Save button click handler
    if (saveButton) {
      saveButton.addEventListener('click', function() {
        if (validatePasswordForm()) {
          changePassword();
        }
      });
    }
    
    // Enter key handler for form fields
    [currentPasswordInput, newPasswordInput, confirmPasswordInput].forEach((input, index, inputs) => {
      if (!input) return;
      
      input.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault();
          
          // Move to next field or submit form
          if (index < inputs.length - 1 && inputs[index + 1]) {
            inputs[index + 1].focus();
          } else if (saveButton) {
            saveButton.click();
          }
        }
      });
    });
  }
  
  /**
   * Validate the password form
   * @returns {boolean} True if form is valid, false otherwise
   */
  function validatePasswordForm() {
    // Check if current password is entered
    if (!currentPasswordInput.value) {
      showMessage('Please enter your current password', 'error');
      currentPasswordInput.focus();
      return false;
    }
    
    // Check if new password is entered
    if (!newPasswordInput.value) {
      showMessage('Please enter a new password', 'error');
      newPasswordInput.focus();
      return false;
    }
    
    // Check if new password is at least 8 characters
    if (newPasswordInput.value.length < 8) {
      showMessage('New password must be at least 8 characters long', 'error');
      newPasswordInput.focus();
      return false;
    }
    
    // Check if confirm password is entered
    if (!confirmPasswordInput.value) {
      showMessage('Please confirm your new password', 'error');
      confirmPasswordInput.focus();
      return false;
    }
    
    // Check if new password and confirm password match
    if (newPasswordInput.value !== confirmPasswordInput.value) {
      showMessage('New password and confirm password do not match', 'error');
      confirmPasswordInput.focus();
      return false;
    }
    
    // Check if new password is different from current password
    if (newPasswordInput.value === currentPasswordInput.value) {
      showMessage('New password must be different from current password', 'error');
      newPasswordInput.focus();
      return false;
    }
    
    return true;
  }
  
  /**
   * Change the user's password
   */
  function changePassword() {
    // Get user data from sessionStorage or localStorage
    const userData = JSON.parse(sessionStorage.getItem('currentUser') || localStorage.getItem('userData') || '{}');
    
    // Check if current password matches stored password
    if (userData.password !== currentPasswordInput.value) {
      showMessage('Current password is incorrect', 'error');
      currentPasswordInput.focus();
      return;
    }
    
    // Update password in user data
    userData.password = newPasswordInput.value;
    userData.lastPasswordChange = new Date().toISOString();
    
    try {
      // Save updated user data
      sessionStorage.setItem('currentUser', JSON.stringify(userData));
      localStorage.setItem('userData', JSON.stringify(userData));
      
      // Show success message
      showMessage('Password changed successfully!', 'success');
      
      // Clear form fields
      currentPasswordInput.value = '';
      newPasswordInput.value = '';
      confirmPasswordInput.value = '';
      
      // Redirect to settings page after a delay
      setTimeout(() => {
        window.location.href = 'setting.html';
      }, 2000);
    } catch (error) {
      console.error('Error changing password:', error);
      showMessage('Error changing password. Please try again.', 'error');
    }
  }
  
  /**
   * Show a message to the user
   * @param {string} message - The message to show
   * @param {string} type - The type of message (success, error, info)
   */
  function showMessage(message, type = 'info') {
    // Create message element
    const messageElement = document.createElement('div');
    messageElement.textContent = message;
    messageElement.style.position = 'fixed';
    messageElement.style.top = '20px';
    messageElement.style.left = '50%';
    messageElement.style.transform = 'translateX(-50%)';
    messageElement.style.padding = '10px 20px';
    messageElement.style.borderRadius = '5px';
    messageElement.style.zIndex = '1000';
    
    // Set styles based on message type
    switch (type) {
      case 'success':
        messageElement.style.backgroundColor = '#4CAF50';
        messageElement.style.color = 'white';
        break;
      case 'error':
        messageElement.style.backgroundColor = '#f44336';
        messageElement.style.color = 'white';
        break;
      default:
        messageElement.style.backgroundColor = '#2196F3';
        messageElement.style.color = 'white';
    }
    
    // Add to document
    document.body.appendChild(messageElement);
    
    // Remove after delay
    setTimeout(() => {
      document.body.removeChild(messageElement);
    }, 3000);
  }
});