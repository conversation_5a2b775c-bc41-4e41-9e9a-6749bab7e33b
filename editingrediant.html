<DocTYPE html>
    <html>

    <head>
        <meta charset="utf-8" />

        <link rel="stylesheet" href="css/edietingrediant.css" />
        
    </head>

    <body>

        <div class="choose-update-milch">
             <div class="frame-4">
                        <div class="frame-5">
                            <img class="update" src="icons/ingrediant icon.png">

                            <div class="text-wrapper-5">Edit Added Ingrediant</div>
                        </div>

                        <div class="frame-6">                            <div class="frame-7">
                                <div class="text-wrapper-6">Name</div>
                                <input class="data-filled" id="ingredientName"></input>
                            </div>                            <div class="frame-71">
                                <div class="dropdown-container">
                                    <div class="text-wrapper-6" id="ingredientTypeBtn">Select Ingredient Type</div>
                                    <div class="dropdown-content" id="ingredientTypeDropdown">
                                        <a href="#" data-value="concentrates">Concentrates</a>
                                        <a href="#" data-value="roughages">Roughages</a>
                                        <a href="#" data-value="millByProduct">Mill by Product</a>
                                        <a href="#" data-value="oilseedByProduct">Oilseed by Product</a>
                                        <a href="#" data-value="forages">Forages</a>
                                    </div>
                                </div>
                            </div>

                             <div class="frame-7">
                                <div class="text-wrapper-6">Proteins</div>
                                <input class="data-filled" id="ingredientProteins"></input>
                            </div>

                            <div class="frame-7">
                                <div class="text-wrapper-6">crude Fiber </div>
                                <input class="data-filled" id="ingredientCF"></input>
                            </div>

                           

                            <div class="frame-7">
                                <div class="text-wrapper-6">Total Digestable Nutrients(TDN) </div>
                                <input class="data-filled" id="ingredientTDN"></input>
                            </div>
                            
                            <div class="frame-7">
                                <div class="text-wrapper-6">Metabolizable Energy(ME)</div>
                                <input class="data-filled" id="ingredientME"></input>
                            </div>

                           

                            <div class="frame-8">
                               


                                <button class="frame-9">
                                     <img class="update" src="icons/save.png">
                                    <div class="btntext">Save Edit</div>
                                </button>
                            </div>
                        </div>

                    </div>
        </div>        <div id="notification-container"></div>
        <script src="js/editingrediant.js"></script>
    </body>

    </html>