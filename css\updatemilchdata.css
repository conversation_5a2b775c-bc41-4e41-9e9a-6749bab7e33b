@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0%;
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}

.choose-update-milch {

    background-color: transparent;
    display: flex;
    flex-direction: row;
    justify-content: center;
    /* padding: 2%; */
    width: 100%;
    height: 100%;
}


.choose-update-milch .text-wrapper-4 {
    position: relative;
    text-align: center;
    width: 227px;
    margin-left: -1.00px;
    margin-right: -1.00px;
    font-family: "Roboto-Medium", Helvetica;
    font-weight: 500;
    color: #ffffff;
    font-size: 8px;
    letter-spacing: 0;


    position: relative;
line-height: 12px;
}





.choose-update-milch .frame-4 {
    position: absolute;
    width: 100%;
    height: 90%;
    top:10%;
    /* left: 24%; */
    background-color:  #ffffff;
    border-radius: 16px;
    display: flex;
    direction: column;

    align-items: flex-start;
}

.choose-update-milch .frame-5 {
    display: flex;
    width: 10%;
    
    height: 15%;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    padding: 10px;
    position: relative;
    top: 5px;
    left: 1%;

}

.choose-update-milch .update {
    position: relative;
    width: 30px;
    height: 30px;

}


 .text-wrapper-5 {
    position: relative;
    width: fit-content;

    font-weight:bold;
    color: #0b291a;
    font-size: 20px;
    letter-spacing: var(--highlights-letter-spacing);
    line-height: var(--highlights-line-height);
    white-space: nowrap;
    font-style: var(--highlights-font-style);
}

.choose-update-milch .frame-6 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 90%;
    align-items: center;
    position: absolute;
    top: 10%;
}

.frame-111 {
    position: relative;


    height: 55px;
   width: 65%;
    font-size: 15px;
    border-radius: 12px;

    display: flex;
    gap:1%;
   justify-content:center;
   left: 20%;
   top: 0%;
   background-color: #f1f1f1;

}

.row{
    width: 96%;
    padding-top: 3%;
    display: flex;
    justify-content: space-between;
}
 .frame-7 {
    display: flex;
    align-items: center;
   flex-direction: row;

    width: 100%;


}

 .frame-79 {
    display: flex;
    align-items: center;
   flex-direction: row;

    width: 100%;


}



 .text-wrapper-6 {
    position: relative;
    width: 15%;

    font-family: "Roboto-Medium", Helvetica;

    color: #000000;
    font-size: 15px;
    letter-spacing: 0;
    line-height: 18px;
    white-space: nowrap;
    left: 0%;

}

 .data-filled {
    position: relative;
    width: 37%;
    height: 50px;
    background-color:#f1f1f1;
    border-radius: 12px;
   border:hidden;
   left: 38%;
}

.frame-11 {
    position:relative;
   background: #f1f1f1;
   border-radius: 12px;
    height: 50px;
   width: 37%;
    font-size: 14px;


    display: flex;
    gap:4%;
left:38%;

}


.frame-12 {
    position:relative;
   background: #f1f1f1;
   border-radius: 12px;
    height: 50px;
   width: 45%;
    font-size: 12px;


    display: flex;
    gap:2%;
left:38%;

}

.raddiv{
    display: inline-flex;
align-items: center;
justify-content: center;
}
.radio{

   width: 20px;
   height: 20px;
   background: #e3e4e4;
   margin-right: 5px;
   accent-color: black;
}

.textarea{
    position: relative;
    width: 45%;
    height: 60px;
    border-radius: 12px;
    border: hidden;

    background-color: #f1f1f1;
    left: 38%;
}


.choose-update-milch .frame-8 {
    display: flex;
    align-items: center;

    padding: 0px 10px;
    position: relative;
    width: 100%;
    height: 50%;
}


.choose-update-milch .double-left {
    position: relative;
    width: 40px;
    height: 40px;
    left: 3%;

}

.choose-update-milch .frame-79 {
     display: flex;
    position: relative;
    width: 81%;
    height: 55px;
    left:10%;
    align-items: center;
    justify-content: center;
   border: hidden;
    position: relative;
    background-color: #aedf32;
    border-radius: 12px;

}

.btntext{
    font-size: 20px;
    font-weight: 40px;
    color: #ffffff;
}

/* Close button removed - handled by external close button in animal.js */

