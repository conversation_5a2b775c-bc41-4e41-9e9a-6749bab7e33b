/* Sidebar Styles */
.side-bar {
  position: fixed;
  left: 1%;
  width: 17%;
  border-radius: 20px;
  background-color: #0b291a;
  overflow: hidden;
  flex-shrink: 0;
  box-sizing: border-box;
  margin: 10px;
  padding: 10px;
  height: 94%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  text-align: center;
}

.side-bar a {
  text-decoration: none;
  color: white;
}

.sra {
  margin: 0;
  font-size: 54px;
  font-weight: 400;
  font-family: ABeeZee;
  color: #fbfaf0;
}

.smart-raising-animal {
  position: relative;
  line-height: 11px;
  font-size: 13px;
}

.smart-raising-animal-wrapper {
  position: absolute;
  top: 40%;
  left: 25%;
  background-color: #0b291a;
  height: 11px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  text-align: left;
  font-size: 13px;
  color: white;
  font-family: 'Source Code Pro', monospace;
}

.sra-parent {
  height: 100%;
  width: 100%;
  position: relative;
  margin: 0 auto;
}

.s-r-a-parent-wrapper {
  width: 100%;
  height: 65px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
}

.item-separator-child {
  height: 2px;
  width: 90%;
  position: relative;
  object-fit: cover;
}

.side-bar-bottom-inner {
  width: 100%;
  height: 2px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
}

.vuesaxlinearcategory-2-icon {
  height: 30px;
  width: 30px;
  position: relative;
}

.animals {
  /* position: relative;
  font-size: 17px;
  font-weight: 400;
  display: inline-block;
  margin: 0; */
  position: relative;
  font-size: 20px; 
  font-weight: 350;
  font-family: inherit;
}

.vuesaxlinearcategory-2-parent {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
}

.side-bar-option-parent {
  /* width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 10px 0;
  box-sizing: border-box; */
  width: 100%;
  height: 57px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  padding: 0 0 0 8px;
  box-sizing: border-box;
}

.side-bar-option-parent-inner {
  width: 100%;
  height: 2px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 0;
  box-sizing: border-box;
}

.side-bar-element {
  width:100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-around;
  padding: 14px 0px 14px 8px;
  box-sizing: border-box;
}

.animals-wrapper {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.side-animal {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
}

.bull-icon,
.cow-1-icon {
  height: 30px;
  width: 30px;
  position: relative;
  object-fit: cover;
}

.side-bar-options {
  height: 666px;
  display: flex;
  width: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  z-index: 1;
}

.side-bar-elements {
   display: flex;
  align-items: flex-start;
  width: 100%;
  flex-direction: row;
  justify-content: center;
    height: 666px;
 
}

.side-bar-bottom {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  text-align: left;
  font-size: 25px;
}
.side-bar-option-parent1 {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

/* Navbar Styles */
.navbar {
  width: 80%;
  height: 49px;
  border-radius: 20px;
  background-color: #fff;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  top: 0;
  z-index: 99;
  position: fixed;
  top: 2%;
  left: 19%;
  max-width: 100%;
  /* padding: 0 20px; */
}

.notification-bell-icon {
  /* height: 24px;
  width: 24px;
  position: relative;
  object-fit: cover; */
  height: 40px;
  width: 40px;
  position: relative;
  flex-shrink: 0;
  object-fit: cover;
  left: 19px;
}

.male-avatar-portrait-of-a-youn-icon {
  /* height: 36px;
  width: 36px;
  position: relative;
  border-radius: 50%;
  object-fit: cover; */
  height: 40px;
  width: 40px;
  position: relative;
  border-radius: 50%;
  object-fit: cover;
}

.nav-profile {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 30px;
  cursor: pointer;
  padding-right: 16px;
}

.side-pregnant {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

/* Popup Styles */
.popup-overlay1 {
  /* position: fixed;
  top: 60px;
  right: 20px;
  z-index: 1000;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
   position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  z-index: 100;
}

.popup-overlay1 a {
  text-decoration: none;
  color: black;
}

/* .nav-profile1 {
  display: flex;
  flex-direction: column;
  width: 150px;
}

.component-13,
.component-131,
.component-14 {
  padding: 10px 15px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.component-13:hover,
.component-131:hover,
.component-14:hover {
  background-color: #f5f5f5;
}

.log-out,
.log-out1,
.log-out2 {
  font-size: 14px;
  font-weight: 500;
} */
 .nav-profile1 {
  position: absolute;
  top: 60px;
  right: 20px;
  width: 140px;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 5px;
  z-index: 101;
}

.component-13, 
.component-131, 
.component-14 {
  align-self: stretch;
  height: 44px;
  border-radius: 6px;
  background-color:#fbfaf0;
  overflow: hidden;
  flex-shrink: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 11px 10px;
  box-sizing: border-box;
  cursor: pointer;
  transition: background-color 0.3s;
}

.component-13:hover, 
.component-131:hover, 
.component-14:hover {
  background-color: #f0f0f0;
}

.log-out, 
.log-out1, 
.log-out2 {
  width: auto;
  position: relative;
  font-weight: 500;
  display: inline-block;
}

.popup-overlay a {
  text-decoration: none;
  color: black;
}

/* إضافة مؤشر للعنصر القابل للنقر */
#navProfileContainer {
  cursor: pointer;
}

/* Responsive Styles */
@media screen and (max-width: 900px) {
  .side-bar {
    width: 250px;
  }
}

@media screen and (max-width: 700px) {
  .side-bar {
    width: 100%;
    height: auto;
    margin-bottom: 20px;
  }
}

@media screen and (max-width: 450px) {
  .navbar {
    height: auto;
    padding: 5px 10px;
  }
  
  .animals {
    font-size: 15px;
  }
  
  .vuesaxlinearcategory-2-icon,
  .bull-icon,
  .cow-1-icon {
    height: 25px;
    width: 25px;
  }
}

/* Additional styles for responsive sidebar */
.mobile-sidebar {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 250px !important;
  height: 100vh !important;
  z-index: 1000 !important;
  transition: transform 0.3s ease-in-out !important;
  overflow-y: auto !important;
}

/* Sidebar toggle button */
.sidebar-toggle {
  display: none;
  position: fixed;
  top: 10px;
  left: 10px;
  z-index: 1001;
  background-color: #2f4858;
  color: white;
  border: none;
  border-radius: 5px;
  padding: 10px 15px;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  transition: background-color 0.3s;
}

.sidebar-toggle:hover {
  background-color: #3a5a6d;
}

/* Overlay for mobile sidebar */
.sidebar-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

/* Active state for sidebar links */
.side-bar a.active {
  color: #aedf32 !important;
  font-weight: bold !important;
}

.side-bar .side-animal.active {
  background-color: rgba(174, 223, 50, 0.1) !important;
  border-radius: 5px !important;
}

/* Improved navbar styles */
.navbar {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Improved profile dropdown */
.popup-overlay1 {
  transition: opacity 0.3s, transform 0.3s;
  transform-origin: top right;
}

.popup-overlay1.show {
  opacity: 1;
  transform: scale(1);
}

.popup-overlay1.hide {
  opacity: 0;
  transform: scale(0.9);
  pointer-events: none;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .content-container {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 10px !important;
  }
  
  .navbar {
    padding-left: 50px !important; /* Make room for the toggle button */
  }
  
  .side-bar {
    margin: 0 !important;
    border-radius: 0 !important;
  }
}
