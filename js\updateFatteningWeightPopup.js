/**
 * UpdateFatteningWeight popup class
 * This file contains the implementation of the Update Fattening Weight popup
 */

class UpdateFatteningWeightPopup extends Popup {
    constructor() {
        super('choose-update-milch'); // Use the CSS class from updatefatteningweight.css
        this.weightData = {};
    }

    // Create the popup content
    createPopupContent() {
        // Create the frame-4 div with proper styling
        const frame4 = document.createElement('div');

        // Apply common styles
        this.applyFrame4Styles(frame4);

        frame4.innerHTML = `
            <div class="frame-5">
                <img class="update" src="icons/scale.png">
                <div class="text-wrapper-5">Weight Fattening</div>
            </div>

            <div class="frame-6">
                <div class="frame-7">
                    <div class="text-wrapper-6">Code</div>
                    <input class="data-filled">
                </div>

                <div class="frame-7">
                    <div class="text-wrapper-6">Weight</div>
                    <input class="data-filled">
                </div>

                <div class="frame-7">
                    <div class="text-wrapper-6">Date of weight</div>
                    <input class="data-filled" type="date">
                </div>

                <div class="frame-7">
                    <div class="text-wrapper-6">Healthcare note</div>
                    <input class="textarea">
                </div>

                <div class="frame-8">
                    <button class="frame-9">
                        <img class="update1" src="icons/save.png">
                        <div class="btntext">Save Weight</div>
                    </button>
                </div>
            </div>
        `;

        // Add close button (X mark)
        this.addCloseButton(frame4);

        return frame4;
    }

    setupEventListeners() {
        // Get save button
        const saveButton = this.popupContainer.querySelector('.frame-9');
        if (saveButton) {
            saveButton.addEventListener('click', () => this.saveWeight());
        }

        // Get input fields
        const inputs = this.popupContainer.querySelectorAll('input.data-filled, input.textarea');
        inputs.forEach(input => {
            input.addEventListener('change', (e) => {
                const label = e.target.closest('.frame-7').querySelector('.text-wrapper-6').textContent;
                const fieldName = this.getLabelFieldName(label);
                this.weightData[fieldName] = e.target.value;
            });
        });
    }

    // Convert label text to camelCase field name
    getLabelFieldName(label) {
        const labelMap = {
            'Code': 'code',
            'Weight': 'weight',
            'Date of weight': 'dateOfWeight',
            'Healthcare note': 'healthcareNote'
        };

        return labelMap[label] || label.toLowerCase().replace(/\s(.)/g, ($1) => $1.toUpperCase()).replace(/\s/g, '');
    }

    // Save weight data
    saveWeight() {
        // Validate required fields
        if (!this.weightData.code || !this.weightData.weight) {
            alert('Please fill in all required fields (Code and Weight are mandatory)');
            return;
        }

        // Update animal weight in table
        AnimalTableManager.updateAnimalWeight(this.weightData);

        alert('Weight updated successfully!');
        this.closePopup();
    }
}
