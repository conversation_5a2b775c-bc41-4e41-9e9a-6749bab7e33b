@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0%;
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}

.feed {
   
    background-color: #e3e4e4;
    display: flex;
    flex-direction: row;
    justify-content: center;
    /* padding: 2%; */
    width: 100%;
    height: 100%;
}



 /* .navbar {
    position: absolute;
    width:75%;
    height: 54px;
    top: 3%;
    left: 22%;
    background-color: #ffffff;
    border-radius: 20px;
    overflow: hidden;
}

.notification-bell {
    position: absolute;
    width: 55px;
    height: 55px;
    top: 6px;
    left: 19px;
    background-image: url(./img/notification.png);
    background-size: 100% 100%;
}

 .frame {
    position: absolute;
    width: 143px;
    height: 64px;
    top: 1px;
    left: 915px;
}

 .side-bar {
    position: absolute;
    width: 18%;
    height: 94%;
    left: 2%;
  margin-bottom: 2%;
    background-color: #0b291a;
    border-radius: 20px;
    overflow: hidden;
} */

.parent{
  display:flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 85%;
  
    
}

.child{
    height: 10%;
    
    color: #ffffff;
}





 .frame-3 {
    display: flex;
    flex-direction: column;
   
    position: relative;
    text-align: center;
justify-content: center;
align-items: center;
    width: 100%;
  height: 15%;


font-size: 40px;
color: #fbfaf0;
font-family: ABeeZee;
}

 .text-wrapper-3 {

    font-size: 4vw; /* Large font size for acronym */
    font-weight: 400;
    color: #f0f0f0; /* Light color for text */

}


 .text-wrapper-4 {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    font-size: .9vw; /* Smaller font size for full text */
    color: #f0f0f0; /* Light color for text */
    z-index: 3;
    background-color: #0b291a;
    
 }






 .frame-4 {
    position: absolute;
    width: 75%;
    height:6%;
    top:12%;
    left: 22%;
    background-color: transparent;
    border-radius: 16px;
    display: flex;
    direction: row;
    gap: 2%;
    justify-content: flex-start;
    
}

 .frame-5 {
    display: flex;
    width: 10%;
    height: 10%;
    justify-content: flex-start;
  margin-left: 1%;
}

.update {
    position: relative;
    width: 25px;
    height: 25px;
    
}
    

 .text-wrapper-5 {
    position: relative;
    width: fit-content;
   
    font-weight:bold;
    color: #0b291a;
    font-size: 20px;
    letter-spacing: var(--highlights-letter-spacing);
    line-height: var(--highlights-line-height);
    white-space: nowrap;
    font-style: var(--highlights-font-style);
}

 .frame-6 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 85%;
    align-items: center; 
    position: absolute;
    top: 15%;
}

 .frame-7 {
    display: flex;
    align-items: center;
   
    padding: 0px 10px;
    position: relative;
    width: 100%;
    height: 50%;
    
}



 .text-wrapper-6 {
    position: relative;
    width: 20%;
   
    font-family: "Roboto-Medium", Helvetica;
    font-weight: bold;
    color: #000000;
    font-size: 25px;
    letter-spacing: 0;
    line-height: 18px;
    white-space: nowrap;
    left: 10%;
  
}

.data-filled {
    position: relative;
    width: 35%;
    height: 45px;
    background-color: #ffffff;
    border-radius: 16px;
   border: 1px solid;
    border-color: #0b291a;
    right: -25%;
    
}

.textarea{
    position: relative;
    width: 40%;
    height: 90px;
    border-radius: 16px;
    border: 1px solid;
     border-color: #0b291a;
     right: -25%;
}






 .frame-9 {
    display: flex;
    position: relative;
    width: 24%;
    height: 45px;
    align-items: center;
    justify-content: center;
   border: hidden;
    position: relative;
    background-color: #aedf32;
    border-radius: 14px;
   
}

 

.btntext{
    font-size: 20px;
    font-weight: 40px;
    color: #ffffff;
}

.search{
    display: flex;
    position: relative;
    width: 24%;
    height: 45px;
    align-items: center;
    justify-content: center;
   border: hidden;
    position: relative;
    
    border-radius: 14px;
    background-color: #c1d8b9;
   
}
.btntext1{
    font-size: 25px;
    font-weight: 40px;
    color: #a9a9a9  ;
}

.parent1{
    position: absolute;
    width: 75%;
    height: 75%;
    top:19%;
    left: 22%;
    background-color: #ffffff;
    border-radius: 16px;
    display: flex;
    direction: column;
   justify-content: flex-start;

    padding-top: 1%;
    
}


.frame-7 {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
   top: 8%;
   position: absolute;
   
   left: -8%;
}



 .text-wrapper-6 {
    position: absolute;
   width: auto;
    font-family: "Roboto-Medium", Helvetica;
    font-weight: bold;
    color: #000000;
    font-size: 17px;
    letter-spacing: 0;
    line-height: 18px;
    white-space: nowrap;
   
   
  
}

.frame-11 {
    position: absolute;
 left: 17%;
   
 
    height: 10px;
  
    font-size: 15px;
    font-weight: 150;
   
}

.raddiv{
    display: inline-flex;
align-items: center;
justify-content: center;

}
.radio{
  
   width: 17px;
   height: 17px;
   background: #e3e4e4;
   
   accent-color: #aedf31;
}


/* Table with vertical scrollbar */
.tablecontainer {
    margin-top: 5%;
    width: 100%;
    height: 90%;
    position: absolute;
   
    overflow: hidden; /* Hide overflow on the container itself */
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    border-radius: 8px;
    overflow: hidden;
}

thead {
    
}
  
thead tr {
    display: table;
    width: 100%;
    table-layout: fixed;
    border-bottom: .5px solid rgb(51, 51, 51) ; /* Green line matching your theme */
  }

tbody {
    display: block;
    max-height: 400px; /* Set the height you want for the scrollable area */
    overflow-y: auto; /* Enable vertical scrolling */
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: #aedf32 #f1f1f1; /* For Firefox - thumb and track colors */
    width: 100%;
}

/* Styling the scrollbar for WebKit browsers */
tbody::-webkit-scrollbar {
    width: 8px;
}

tbody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

tbody::-webkit-scrollbar-thumb {
    background: #aedf32; /* Match your button color */
    border-radius: 4px;
}

tbody::-webkit-scrollbar-thumb:hover {
    background: #8fb728; /* Darker shade for hover */
}

tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

th, td {
    padding: 12px 15px;
    text-align: left;
    font-size: 16px;
}

th {
    font-weight: bold;
    color: #333;
}

tbody tr:hover {
    background-color: #f9f9f9;
}

.checkbox {
    margin-right: 8px;
    cursor: pointer;
}

.selectbox {
    display: flex;
    align-items: center;
    cursor: pointer;
}

/* Add animation for new rows */
@keyframes highlightNew {
    from { background-color: #aedf32; }
    to { background-color: transparent; }
}

.new-row {
    animation: highlightNew 1s ease-out;
}
