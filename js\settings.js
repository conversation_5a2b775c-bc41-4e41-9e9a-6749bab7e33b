document.addEventListener('DOMContentLoaded', function() {
  // Get user data from sessionStorage
  const currentUser = JSON.parse(sessionStorage.getItem('currentUser') || '{}');
  
  // Populate user data in the settings page
  populateUserData(currentUser);
  
  // Set up event listeners
  setupEventListeners();
  
  // Initialize date picker
  initializeDatePicker();
});

/**
 * Populate user data in the settings page
 * @param {Object} user - The current user object
 */
function populateUserData(user) {
  // Set user name
  const nameElement = document.querySelector('.accessibility-container .profile-settings-values .photo');
  if (nameElement && user.name) {
    nameElement.textContent = user.name;
  }
  
  // Set user email
  const emailElement = document.querySelector('.profile-settings-fields3 .profile-settings-values .photo');
  if (emailElement && user.email) {
    emailElement.textContent = user.email;
  }
  
  // Set user role (Owner/Engineer)
  const roleElement = document.querySelector('.owner-container .animals-title-container .photo');
  if (roleElement && user.role) {
    roleElement.textContent = user.role.charAt(0).toUpperCase() + user.role.slice(1);
  }
}

/**
 * Set up event listeners for the settings page
 */
function setupEventListeners() {
  // Edit button click handler
  const editButton = document.querySelector('.edite-buttom');
  if (editButton) {
    editButton.addEventListener('click', function() {
      window.location.href = 'edit1.html';
    });
  }
  
  // Time zone button click handler
  const timeZoneButton = document.querySelector('.time-zone-value-container');
  if (timeZoneButton) {
    timeZoneButton.addEventListener('click', function() {
      const timeZones = [
        'UTC+0', 'UTC+1', 'UTC+2', 'UTC+3', 'UTC+4', 'UTC+5',
        'UTC-1', 'UTC-2', 'UTC-3', 'UTC-4', 'UTC-5'
      ];
      
      const currentZone = this.textContent.trim();
      const currentIndex = timeZones.indexOf(currentZone);
      const nextIndex = (currentIndex + 1) % timeZones.length;
      
      this.textContent = timeZones[nextIndex];
      
      // Save the selected time zone to localStorage
      localStorage.setItem('userTimeZone', timeZones[nextIndex]);
    });
  }
  
  // Profile photo click handler for changing photo
  const profilePhoto = document.querySelector('.photo-child');
  if (profilePhoto) {
    profilePhoto.addEventListener('click', function() {
      // In a real application, this would open a file picker
      alert('This would open a file picker to change your profile photo');
    });
  }
}

/**
 * Initialize the date picker with the user's preferred format
 */
function initializeDatePicker() {
  const dateInput = document.querySelector('.input-data');
  if (dateInput) {
    // Set current date as default
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    
    dateInput.value = `${year}-${month}-${day}`;
    
    // Add change event listener
    dateInput.addEventListener('change', function() {
      // Save the selected date format preference
      localStorage.setItem('userDateFormat', this.value);
    });
  }
}

// Add animation effects to settings elements
function animateSettingsElements() {
  const settingsElements = document.querySelectorAll('.profile-settings, .user-photo-container');
  
  if (settingsElements.length > 0) {
    settingsElements.forEach(element => {
      element.style.transition = "transform 0.3s ease, box-shadow 0.3s ease";
      
      element.addEventListener("mouseover", function() {
        this.style.transform = "translateY(-5px)";
        this.style.boxShadow = "0 10px 20px rgba(0, 0, 0, 0.1)";
      });
      
      element.addEventListener("mouseout", function() {
        this.style.transform = "translateY(0)";
        this.style.boxShadow = "none";
      });
    });
  }
  
  // Animate settings content on load
  const settingsContent = document.querySelector('.user-settings-content');
  if (settingsContent) {
    settingsContent.style.opacity = "0";
    settingsContent.style.transform = "translateY(20px)";
    settingsContent.style.transition = "opacity 0.5s ease, transform 0.5s ease";
    
    setTimeout(() => {
      settingsContent.style.opacity = "1";
      settingsContent.style.transform = "translateY(0)";
    }, 300);
  }
}

// Call the animation function
animateSettingsElements();