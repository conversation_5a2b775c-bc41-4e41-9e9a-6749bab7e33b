<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1, width=device-width" />

  <link rel="stylesheet" href="./css/general.css" />
  <link rel="stylesheet" href="./css/setting.css" />
  <link rel="stylesheet" href="css/navigation-components.css" />

  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=ABeeZee:wght@400&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Source Code Pro:wght@400&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@200;300;400;500&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto Serif:wght@400&display=swap" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=REM:wght@400&display=swap" />
</head>

<body>
  <div class="setting">
    <!-- Include the sidebar and navbar components -->
    <div class="page-container">
      <!-- Sidebar Component -->
      <div class="side-bar">
        <div class="s-r-a-parent-wrapper">
          <div class="sra-parent">
            <h1 class="sra">SRA</h1>
            <div class="smart-raising-animal-wrapper">
              <div class="smart-raising-animal">Smart Raising Animal</div>
            </div>
          </div>
        </div>
        <div class="side-bar-bottom">
          <div class="side-bar-bottom-inner">
            <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
          </div>
          <div class="side-bar-elements">
            <div class="side-bar-options">
              <div class="side-bar-option-parent">
                <div class="vuesaxlinearcategory-2-parent">
                  <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt=""
                    src="./public/vuesaxlinearcategory21.svg" />
                  <a class="animals" href="index.html">Dashboard</a>
                </div>
              </div>
              <div class="side-bar-option-parent-inner">
                <img class="item-separator-child" loading="lazy" alt="" src="./public/line-1.svg" />
              </div>
              <div class="side-bar-option-parent1">
                <div class="side-bar-element">
                  <div class="side-animal">
                    <img class="bull-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <div class="animals-wrapper">
                      <a class="animals" href="animal.html">Animals</a>
                    </div>
                  </div>
                  <div class="side-animal">
                    <img class="bull-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="dairy.html">Milch</a>
                  </div>
                  <div class="side-animal">
                    <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="newborn.html">New Born</a>
                  </div>
                  <div class="side-animal">
                    <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="feed.html">Feed</a>
                  </div>
                  <div class="side-animal">
                    <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="ingrediants.html">Ingredients</a>
                  </div>
                  <div class="side-animal">
                    <div class="side-animal">
                      <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                      <a class="animals" href="vaccination.html">Vaccination</a>
                    </div>
                  </div>
                  <div class="side-animal">
                    <img class="cow-1-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
                    <a class="animals" href="reports.html">Reports</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navbar Component -->
      <header class="navbar">
        <img class="notification-bell-icon" loading="lazy" alt="" src="./public/<EMAIL>" />
        <div class="nav-profile" id="navProfileContainer">
          <img class="male-avatar-portrait-of-a-youn-icon" loading="lazy" alt=""
            src="./public/<EMAIL>" />
          <div class="side-pregnant">
            <img class="vuesaxlinearcategory-2-icon" loading="lazy" alt="" src="./public/vuesaxlineararrowdown.svg" />
          </div>
        </div>
      </header>


      <!-- Your page content goes here -->
      <main class="content-container">
        <!-- Page specific content -->


        <div class="settings2">
          <div class="user-settings-top">
            <div class="user-settings-title-container">
              <img class="services-icon" loading="lazy" alt="" src="./public/<EMAIL>" />

              <div class="settings">Settings</div>
            </div>
          </div>
          <div class="user-settings-content">
            <div class="user-settings-content-child"></div>
            <div class="profile-settings-container">
              <div class="profile-settings">
                <div class="profile-settings-layout">
                  <div class="basic-settings-container">
                    <div class="basic-settings">
                      <div class="settings">Basic</div>
                      <button class="edite-buttom">
                        <a class="edit" href="edit.html">Edit</a>
                        <!-- <div class="edit">Edit</div> -->
                      </button>
                    </div>
                    <div class="user-photo-container">
                      <div class="user-photo-title-container">
                        <div class="photo">Photo</div>
                      </div>
                      <div class="photo1">
                        <img class="photo-child" loading="lazy" alt="" src="./public/<EMAIL>" />
                      </div>
                    </div>
                  </div>
                  <div class="profile-settings-fields"></div>
                  <div class="accessibility-container">
                    <div class="user-photo-title-container">
                      <div class="photo">Name</div>
                    </div>
                    <div class="profile-settings-values">
                      <div class="photo">Layla Hassan</div>
                    </div>
                  </div>
                  <div class="profile-settings-fields"></div>
                  <div class="profile-settings-fields3">
                    <div class="email-address-wrapper">
                      <div class="email-address">Email Address</div>
                    </div>
                    <div class="profile-settings-values">
                      <div class="photo"><EMAIL></div>
                    </div>
                  </div>
                  <div class="profile-settings-fields"></div>
                  <div class="profile-settings-fields5">
                    <div class="email-address-wrapper">
                      <div class="password">Password</div>
                    </div>
                    <div class="wrapper">
                      <div class="div5">**********</div>
                    </div>
                  </div>
                  <div class="profile-settings-fields"></div>
                  <div class="accessibility-container">
                    <div class="email-address-wrapper">
                      <div class="photo">Accessibility</div>
                    </div>
                    <div class="owner-container">
                      <div class="owner-title-container">
                        <div class="animals-title-container">
                          <div class="photo">Owner</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="profile-settings-fields"></div>
                </div>
                <div class="performance-container">
                  <div class="settings">Performance</div>
                  <div class="date-format-container">
                    <div class="date-format-settings">
                      <div class="date-format-field">
                        <div class="email-address-wrapper">
                          <div class="photo">Date Format</div>
                        </div>
                        <!-- <button class="date"> -->
                        <!-- <div class="ddmmyyyy-wrapper"> -->
                        <!-- <div class="ddmmyyyy">DD/MM/YYYY</div> -->
                        <input class="input-data" type="date" name="data format">
                        <!-- </div> -->
                        <!-- </button> -->
                      </div>
                    </div>
                    <div class="time-zone-container">
                      <div class="time-zone-container-child"></div>
                      <div class="time-zone-settings">
                        <div class="time-zone-field">
                          <div class="email-address">
                            Automatic Time Zone
                          </div>
                          <button class="time-zone-value-container">
                            <div>UTC+2</div>
                          </button>
                        </div>
                      </div>
                    </div>
                    <div class="time-zone-container-child"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </main>
    </div>
  </div>

  <!-- القائمة المنسدلة -->
  <div id="navProfilePopup" class="popup-overlay1" style="display: none">
    <div class="nav-profile1">
      <div class="component-13">
        <a class="log-out" href="logout.html">Log Out</a>
      </div>
      <div class="component-131">
        <a class="log-out1" href="setting.html">Settings</a>
      </div>
      <div class="component-14">
        <a class="log-out2" href="about-us.html">About</a>
      </div>
    </div>
  </div>

  <!-- تضمين ملف JavaScript الموحد -->
  <!-- <script src="./js/main1.js"></script> -->
  <script src="js/navigation-components.js"></script>
  <script src="js/settings.js"></script>
</body>

</html>
