<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Final Search Test - Animal Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #007bff;
        }
        .test-button {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,123,255,0.4);
        }
        .status {
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            font-weight: 500;
        }
        .success { background: #d4edda; color: #155724; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; color: #721c24; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; color: #0c5460; border-left: 4px solid #17a2b8; }
        .warning { background: #fff3cd; color: #856404; border-left: 4px solid #ffc107; }
        
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .instructions {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #007bff;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-left: 3px solid #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Final Search Functionality Test</h1>
            <p>Testing the fixed search functionality on the Animal page</p>
        </div>

        <div class="test-section">
            <h2>📋 Test Instructions</h2>
            <div class="instructions">
                <h3>How to Test the Search Functionality:</h3>
                <div class="step">
                    <strong>Step 1:</strong> Click the "Open Animal Page" button below to load the actual animal.html page
                </div>
                <div class="step">
                    <strong>Step 2:</strong> In the animal page, click the "Search" button (green button with search icon)
                </div>
                <div class="step">
                    <strong>Step 3:</strong> You should see a dropdown menu with search options:
                    <ul>
                        <li>🏷️ By Code</li>
                        <li>🐄 By Type</li>
                        <li>🏠 By Herd Number</li>
                        <li>🔄 Clear Search</li>
                    </ul>
                </div>
                <div class="step">
                    <strong>Step 4:</strong> Click on any search option to see the input field appear
                </div>
                <div class="step">
                    <strong>Step 5:</strong> Enter a search term and click "Search" or press Enter
                </div>
                <div class="step">
                    <strong>Step 6:</strong> The table should filter to show matching results
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 Quick Test Actions</h2>
            <button class="test-button" onclick="openAnimalPage()">
                🐄 Open Animal Page
            </button>
            <button class="test-button" onclick="openMinimalTest()">
                🧪 Open Minimal Test
            </button>
            <button class="test-button" onclick="runQuickTest()">
                ⚡ Run Quick Test
            </button>
            <button class="test-button" onclick="createTestData()">
                📊 Create Test Data
            </button>
            <button class="test-button" onclick="clearResults()">
                🗑️ Clear Results
            </button>
        </div>

        <div class="test-section">
            <h2>📊 Test Results</h2>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>🖥️ Animal Page Preview</h2>
            <p>The animal page will load below. Test the search functionality directly in this iframe:</p>
            <div class="iframe-container">
                <iframe id="animalPageFrame" src="animal.html"></iframe>
            </div>
        </div>

        <div class="test-section">
            <h2>✅ Expected Results</h2>
            <div class="instructions">
                <h3>What Should Happen:</h3>
                <div class="step">
                    ✅ Search button should be visible and clickable
                </div>
                <div class="step">
                    ✅ Clicking search button should show a dropdown menu
                </div>
                <div class="step">
                    ✅ Dropdown should have 4 options: By Code, By Type, By Herd Number, Clear Search
                </div>
                <div class="step">
                    ✅ Clicking an option should show an input field
                </div>
                <div class="step">
                    ✅ Entering text and clicking Search should filter the table
                </div>
                <div class="step">
                    ✅ Clear Search should show all animals again
                </div>
            </div>
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `status ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            addResult('🗑️ Results cleared', 'info');
        }

        function openAnimalPage() {
            window.open('animal.html', '_blank');
            addResult('🐄 Opened animal page in new tab', 'success');
        }

        function openMinimalTest() {
            window.open('search-test-minimal.html', '_blank');
            addResult('🧪 Opened minimal test page in new tab', 'success');
        }

        function createTestData() {
            try {
                const testAnimals = [
                    {
                        code: 'A001',
                        type: 'dairy',
                        herdNumber: 'H001',
                        gender: 'female',
                        weight: 450,
                        dateOfWeight: '2024-01-15',
                        healthcareNotes: 'Healthy dairy cow'
                    },
                    {
                        code: 'A002',
                        type: 'newborn',
                        herdNumber: 'H002',
                        gender: 'male',
                        weight: 35,
                        dateOfWeight: '2024-01-20',
                        healthcareNotes: 'Newborn calf in good condition'
                    },
                    {
                        code: 'B001',
                        type: 'fattening',
                        herdNumber: 'H003',
                        gender: 'male',
                        weight: 300,
                        dateOfWeight: '2024-01-18',
                        healthcareNotes: 'Fattening bull gaining weight well'
                    },
                    {
                        code: 'C001',
                        type: 'dairy',
                        herdNumber: 'H004',
                        gender: 'female',
                        weight: 420,
                        dateOfWeight: '2024-01-22',
                        healthcareNotes: 'High milk production'
                    }
                ];

                localStorage.setItem('animals', JSON.stringify(testAnimals));
                addResult('📊 Created test data with 4 animals (A001, A002, B001, C001)', 'success');
                
                // Try to refresh the iframe
                const iframe = document.getElementById('animalPageFrame');
                if (iframe) {
                    iframe.contentWindow.location.reload();
                    addResult('🔄 Refreshed animal page iframe', 'info');
                }
            } catch (error) {
                addResult(`❌ Error creating test data: ${error.message}`, 'error');
            }
        }

        function runQuickTest() {
            addResult('⚡ Running quick test...', 'info');
            
            // Test localStorage
            try {
                localStorage.setItem('test', 'working');
                const test = localStorage.getItem('test');
                if (test === 'working') {
                    addResult('✅ localStorage is working', 'success');
                    localStorage.removeItem('test');
                } else {
                    addResult('❌ localStorage test failed', 'error');
                }
            } catch (error) {
                addResult(`❌ localStorage error: ${error.message}`, 'error');
            }

            // Test iframe access
            try {
                const iframe = document.getElementById('animalPageFrame');
                if (iframe) {
                    addResult('✅ Animal page iframe is loaded', 'success');
                    
                    // Try to access iframe content (may fail due to same-origin policy)
                    try {
                        const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                        if (iframeDoc) {
                            const searchButton = iframeDoc.querySelector('#searchButton') || iframeDoc.querySelector('.search');
                            if (searchButton) {
                                addResult('✅ Search button found in iframe', 'success');
                            } else {
                                addResult('⚠️ Search button not found in iframe', 'warning');
                            }
                        }
                    } catch (crossOriginError) {
                        addResult('ℹ️ Cannot access iframe content (normal for file:// URLs)', 'info');
                    }
                } else {
                    addResult('❌ Animal page iframe not found', 'error');
                }
            } catch (error) {
                addResult(`❌ Iframe test error: ${error.message}`, 'error');
            }

            addResult('⚡ Quick test completed', 'info');
        }

        // Auto-run initial test
        window.addEventListener('load', () => {
            addResult('🚀 Final search test page loaded', 'success');
            addResult('📋 Ready to test search functionality', 'info');
            
            setTimeout(() => {
                createTestData();
            }, 1000);
        });
    </script>
</body>
</html>
