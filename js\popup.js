/**
 * Base Popup class for common popup functionality
 * This file contains the base popup class that all other popups will extend
 */

class Popup {
    constructor(cssClass = 'choose-update-milch') {
        this.popupContainer = null;
        this.cssClass = cssClass;
    }

    // Create popup container
    createPopupContainer() {
        if (!this.popupContainer) {
            // Create overlay container
            this.popupContainer = document.createElement('div');
            this.popupContainer.className = this.cssClass;
            document.body.appendChild(this.popupContainer);

            // Add close functionality when clicking outside the popup
            this.popupContainer.addEventListener('click', (e) => {
                if (e.target === this.popupContainer) {
                    this.closePopup();
                }
            });
        }
        return this.popupContainer;
    }

    // Create popup content - to be implemented by child classes
    createPopupContent() {
        throw new Error('createPopupContent must be implemented by child classes');
    }

    // Add X mark to close popup
    addCloseButton(frame4) {
        // Create close button
        const closeButton = document.createElement('div');
        closeButton.className = 'close-button';
        closeButton.style.position = 'absolute';
        closeButton.style.top = '15px';
        closeButton.style.right = '15px';
        closeButton.style.width = '30px';
        closeButton.style.height = '30px';
        closeButton.style.cursor = 'pointer';
        closeButton.style.zIndex = '1002';
        closeButton.style.display = 'flex';
        closeButton.style.justifyContent = 'center';
        closeButton.style.alignItems = 'center';
        closeButton.style.borderRadius = '50%';
        closeButton.style.backgroundColor = '#f1f1f1';
        closeButton.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';

        // Create X mark
        closeButton.innerHTML = `
            <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L14 14M1 14L14 1" stroke="#333" stroke-width="2" stroke-linecap="round"/>
            </svg>
        `;

        // Add event listener
        closeButton.addEventListener('click', () => this.closePopup());

        // Add to frame
        frame4.appendChild(closeButton);
    }

    // Apply common styles to frame-4 - using CSS classes instead of inline styles
    applyFrame4Styles(frame4) {
        // No need to set styles directly, the CSS class will handle it
        // Just ensure the class is applied
        frame4.className = 'frame-4';
    }

    // Apply common styles to inputs - using CSS classes instead of inline styles
    applyInputStyles() {
        // No need to apply styles directly, as they should come from CSS
        // But we can ensure proper classes are applied

        // Make sure all data-filled inputs have the correct class
        const inputs = this.popupContainer.querySelectorAll('input[type="text"], input:not([type])');
        inputs.forEach(input => {
            if (!input.classList.contains('data-filled') && !input.classList.contains('textarea')) {
                input.classList.add('data-filled');
            }
        });

        // Make sure all textareas have the correct class
        const textareas = this.popupContainer.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            if (!textarea.classList.contains('textarea')) {
                textarea.classList.add('textarea');
            }
        });
    }

    // Show popup
    showPopup() {
        this.createPopupContainer();
        const content = this.createPopupContent();

        // No need to override styles, the CSS classes will handle it
        this.popupContainer.appendChild(content);
        this.setupEventListeners();

        return true;
    }

    // Close the popup
    closePopup() {
        if (this.popupContainer) {
            document.body.removeChild(this.popupContainer);
            this.popupContainer = null;
        }
    }

    // Setup event listeners - to be implemented by child classes
    setupEventListeners() {
        // Default implementation does nothing
    }
}
