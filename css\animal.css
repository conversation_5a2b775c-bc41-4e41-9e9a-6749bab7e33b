@import url("https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css");
* {
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}
html,
body {
  margin: 0px;
  height: 100%;
  /**/
  padding: 0%;
  width: 100%;
  /**/
}
/* a blue color as a generic focus style */
button:focus-visible {
  outline: 2px solid #4a90e2 !important;
  outline: -webkit-focus-ring-color auto 5px !important;
}
a {
  text-decoration: none;
}

.feed {

    background-color: #e3e4e4;
    display: flex;
    flex-direction: row;
    justify-content: center;
    /* padding: 2%; */
    width: 100%;
    height: 100%;
}



 .navbar {
    position: absolute;
    width:75%;
    height: 54px;
    top: 3%;
    left: 22%;
    background-color: #ffffff;
    border-radius: 20px;
    overflow: hidden;
}

.notification-bell {
    position: absolute;
    width: 55px;
    height: 55px;
    top: 6px;
    left: 19px;
    background-image: url(./img/notification.png);
    background-size: 100% 100%;
}

 .frame {
    position: absolute;
    width: 143px;
    height: 64px;
    top: 1px;
    left: 915px;
}

 .side-bar {
    position: absolute;
    width: 18%;
    height: 94%;
    left: 2%;
  margin-bottom: 2%;
    background-color: #0b291a;
    border-radius: 20px;

}

.parent{
  display:flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
  width: 100%;
  height: 85%;


}

.child{
    height: 10%;

    color: #ffffff;
}





 .frame-3 {
    display: flex;
    flex-direction: column;

    position: relative;
    text-align: center;
justify-content: center;
align-items: center;
    width: 100%;
  height: 15%;


font-size: 40px;
color: #fbfaf0;
font-family: ABeeZee;
}

 .text-wrapper-3 {

    font-size: 4vw; /* Large font size for acronym */
    font-weight: 400;
    color: #f0f0f0; /* Light color for text */

}


 .text-wrapper-4 {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    transform: translate(-50%, -50%);
    font-size: .9vw; /* Smaller font size for full text */
    color: #f0f0f0; /* Light color for text */
    z-index: 3;
    background-color: #0b291a;

 }






 .frame-4 {
    position: absolute;
    width: 76%;
    height:6%;
    top:11%;
    left: 21%;
    background-color: transparent;
    border-radius: 16px;
    display: flex;
    direction: row;
    gap: 2%;
    justify-content: flex-start;

}

 .frame-5 {
    display: flex;
    width: 10%;
    height: 10%;
    justify-content: flex-start;
  margin-left: 1%;
}

.update {
    position: relative;
    width: 30px;
    height: 30px;

}


 .text-wrapper-5 {
    position: relative;
    width: fit-content;

    font-weight:bold;
    color: #0b291a;
    font-size: 20px;
    letter-spacing: var(--highlights-letter-spacing);
    line-height: var(--highlights-line-height);
    white-space: nowrap;
    font-style: var(--highlights-font-style);
}

 .frame-6 {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 85%;
    align-items: center;
    position: absolute;
    top: 15%;
}

 .frame-7 {
    display: flex;
    align-items: center;

    padding: 0px 10px;
    position: relative;
    width: 100%;
    height: 50%;

}



 .text-wrapper-6 {
    position: relative;
    width: 20%;

    font-family: "Roboto-Medium", Helvetica;
    font-weight: bold;
    color: #000000;
    font-size: 25px;
    letter-spacing: 0;
    line-height: 18px;
    white-space: nowrap;
    left: 10%;

}

.data-filled {
    position: relative;
    width: 35%;
    height: 45px;
    background-color: #ffffff;
    border-radius: 16px;
   border: 1px solid;
    border-color: #0b291a;
    right: -25%;

}

.textarea{
    position: relative;
    width: 40%;
    height: 90px;
    border-radius: 16px;
    border: 1px solid;
     border-color: #0b291a;
     right: -25%;
}






 .frame-9 {
    display: flex;
    position: relative;
    width: 24%;
    height: 45px;
    align-items: center;
    justify-content: center;
   border: hidden;
    position: relative;
    background-color: #aedf32;
    border-radius: 14px;

}



.btntext{
    font-size: 20px;
    font-weight: 40px;
    color: #ffffff;
}

/* Search dropdown container */
.search-dropdown-container {
    position: relative;
    display: inline-block;
    width: 24%;
    height: 45px;
}

.search{
    display: flex;
    position: relative;
    width: 100%;
    height: 45px;
    align-items: center;
    justify-content: center;
    border: hidden;
    border-radius: 14px;
    background-color: #c1d8b9;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search:hover {
    background-color: #a8c49a;
}

/* Search dropdown arrow */
.search-dropdown-arrow {
    font-size: 12px;
    color: #a9a9a9;
    transition: transform 0.3s ease;
    margin-left: 8px;
}

.search-dropdown-container.active .search-dropdown-arrow {
    transform: rotate(180deg);
}

.search-dropdown-container.active .search {
    background-color: #a8c49a;
    transform: translateY(2px);
}
.double-left1 {
    position: relative;
    width: 20px;
    height: 20px;


}
.btntext1{
    font-size: 25px;
    font-weight: 40px;
    color: #a9a9a9  ;
}

.parent1{
    position: absolute;
    width: 74%;
    height: 77%;
    top:19%;
    left: 22%;
    background-color: #ffffff;
    border-radius: 16px;
    display: flex;
    direction: column;
   justify-content: flex-start;
    align-items:space-evenly;
    padding-top: 1%;

}

.checkbox{
    width: 15px;
    height: 15px;
   accent-color: #dfdddd66;
 }

/* Table with vertical scrollbar */
.tablecontainer {
    margin-top: 5%;
    width: 100%;
    height: 90%;
    position: absolute;

    overflow: hidden; /* Hide overflow on the container itself */
}

table {
    width: 100%;
    border-collapse: collapse;
    position: relative;
}

thead {
    display: table;
    width: calc(100% - 8px); /* Adjust for scrollbar width */
    table-layout: fixed;
    background-color: #ffffff; /* Keep header visible */
}

thead tr {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-bottom: .5px solid rgb(51, 51, 51) ; /* Green line matching your theme */
}

tbody {
    display: block;
    max-height: 400px; /* Set the height you want for the scrollable area */
    overflow-y: auto; /* Enable vertical scrolling */
    scrollbar-width: thin; /* For Firefox */
    scrollbar-color: #aedf32 #f1f1f1; /* For Firefox - thumb and track colors */
    width: 100%;
}

/* Styling the scrollbar for WebKit browsers */
tbody::-webkit-scrollbar {
    width: 8px;
}

tbody::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

tbody::-webkit-scrollbar-thumb {
    background: #aedf32; /* Match your button color */
    border-radius: 4px;
}

tbody::-webkit-scrollbar-thumb:hover {
    background: #8fb728; /* Darker shade for hover */
}

tr {
    display: table;
    width: 100%;
    table-layout: fixed;
}

th, td {
    padding: 8px;
    text-align: left;
    font-size: 16px;
}

th {
   font-weight: bold;
}








 /* Media Queries for Responsive Design */

/* Large screens (desktops) */
@media screen and (max-width: 1200px) {
    .navbar, .parent1 {
      width: 72%;
      left: 25%;
    }

    .side-bar {
      width: 22%;
    }

    .frame {
      left: 85%;
    }

    .data-filled, .textarea {
      width: 30%;
    }

    .frame-4 {
      width: 72%;
      left: 25%;
    }
  }

  /* Medium screens (tablets) */
  @media screen and (max-width: 992px) {
    .navbar, .parent1, .frame-4 {
      width: 68%;
      left: 30%;
    }

    .side-bar {
      width: 25%;
    }

    .frame {
      left: 80%;
    }

    .text-wrapper-6 {
      width: 25%;
      font-size: 20px;
    }

    .data-filled, .textarea {
      width: 40%;
      right: -20%;
    }

    .frame-9 {
      width: 30%;
    }

    table {
      font-size: 12px;
    }

    .text-wrapper-3 {
      font-size: 3.5vw;
    }

    .text-wrapper-4 {
      font-size: 1vw;
    }
  }

  /* Small screens (landscape phones) */
  @media screen and (max-width: 768px) {
    .navbar, .parent1, .frame-4 {
      width: 65%;
      left: 32%;
    }

    .side-bar {
      width: 28%;
    }

    .text-wrapper-3 {
      font-size: 6vw;
    }

    .text-wrapper-4 {
      font-size: 1.2vw;
    }

    .frame-7 {
      flex-direction: column;
      align-items: flex-start;
      height: auto;
      padding: 15px;
    }

    .text-wrapper-6 {
      width: 100%;
      left: 0;
      margin-bottom: 10px;
    }

    .data-filled, .textarea {
      width: 100%;
      right: 0;
      margin-bottom: 15px;
    }

    .frame-9 {
      width: 50%;
      margin: 0 auto;
    }

    .tablecontainer {
      margin-top: 5%;
    }

    .double-left1 {
      width: 25px;
      height: 25px;
    }
  }

  /* Extra small screens (portrait phones) */
  @media screen and (max-width: 576px) {
    .feed {
      padding: 0;
    }

    .navbar {
      width: 90%;
      left: 5%;
      top: 2%;
    }

    .side-bar {
      width: 0;
      left: -100%;
      display: none;
    }

    .parent1, .frame-4 {
      width: 90%;
      left: 5%;
    }

    .parent1 {
      top: 12%;
    }

    .frame-4 {
      top: 8%;
    }

    .frame-5 {
      width: 20%;
    }

    .text-wrapper-5 {
      font-size: 16px;
    }

    .frame-9 {
      width: 70%;
    }

    .btntext {
      font-size: 14px;
    }

    .tablecontainer {
      margin-top: 8%;
    }

    th, td {
      padding: 5px;
    }

    th {
      font-size: 16px;
    }

    td {
      font-size: 11px;
    }

    tbody {
      max-height: 300px;
    }

    .search-dropdown-container {
      width: 40%;
    }

    .search {
      width: 100%;
    }

    .btntext1 {
      font-size: 18px;
    }
  }

  /* For very small screens */
  @media screen and (max-width: 400px) {
    .navbar {
      height: 45px;
    }

    .notification-bell {
      width: 40px;
      height: 40px;
    }

    .frame {
      width: 100px;
      height: 45px;
    }

    .parent1 {
      height: 80%;
    }

    .frame-9 {
      height: 40px;
    }

    .btntext {
      font-size: 12px;
    }

    .text-wrapper-3 {
      font-size: 8vw;
    }

    .text-wrapper-4 {
      font-size: 1.5vw;
    }

    .double-left1 {
      width: 20px;
      height: 20px;
    }

    .frame-6 {
      height: 90%;
    }
  }