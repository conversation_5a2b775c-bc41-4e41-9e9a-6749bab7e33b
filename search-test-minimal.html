<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Minimal Search Test</title>
    <link rel="stylesheet" href="css/animal.css" />
    <style>
        body {
            padding: 20px;
            background-color: #e3e4e4;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 16px;
            margin-bottom: 20px;
        }
        .button-container {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 8px;
            margin: 5px 0;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 Search Functionality Test</h1>
        
        <div class="button-container">
            <!-- This is the exact search button from animal.html -->
            <button class="search" id="searchButton">
                <img class="double-left1" src="icons/search.svg">
                <div class="btntext1">Search</div>
                <span class="search-dropdown-arrow">▼</span>
            </button>
            
            <button onclick="testSearch()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px;">
                Test Search
            </button>
            
            <button onclick="createTestData()" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px;">
                Create Test Data
            </button>
            
            <button onclick="clearStatus()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px;">
                Clear Status
            </button>
        </div>
        
        <div id="status"></div>
        
        <div class="test-container">
            <h3>Animal Table</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 8px; border: 1px solid #ddd;">Code</th>
                        <th style="padding: 8px; border: 1px solid #ddd;">Type</th>
                        <th style="padding: 8px; border: 1px solid #ddd;">Herd Number</th>
                        <th style="padding: 8px; border: 1px solid #ddd;">Gender</th>
                        <th style="padding: 8px; border: 1px solid #ddd;">Weight</th>
                    </tr>
                </thead>
                <tbody id="animalTableBody">
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                            No animals loaded yet. Click "Create Test Data" to add some animals.
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Include the animal.js script -->
    <script src="js/animal.js"></script>
    
    <script>
        function addStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const statusItem = document.createElement('div');
            statusItem.className = `status ${type}`;
            statusItem.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            statusDiv.appendChild(statusItem);
        }
        
        function clearStatus() {
            document.getElementById('status').innerHTML = '';
        }
        
        function createTestData() {
            try {
                const testAnimals = [
                    {
                        code: 'A001',
                        type: 'dairy',
                        herdNumber: 'H001',
                        gender: 'female',
                        weight: 450,
                        dateOfWeight: '2024-01-15',
                        healthcareNotes: 'Healthy'
                    },
                    {
                        code: 'A002',
                        type: 'newborn',
                        herdNumber: 'H002',
                        gender: 'male',
                        weight: 35,
                        dateOfWeight: '2024-01-20',
                        healthcareNotes: 'Good condition'
                    },
                    {
                        code: 'B001',
                        type: 'fattening',
                        herdNumber: 'H003',
                        gender: 'male',
                        weight: 300,
                        dateOfWeight: '2024-01-18',
                        healthcareNotes: 'Gaining weight'
                    }
                ];
                
                localStorage.setItem('animals', JSON.stringify(testAnimals));
                addStatus('✅ Test data created successfully', 'success');
                
                // Update table
                updateTable();
                
                // Refresh animal controller table if available
                if (window.animalPageController && window.animalPageController.refreshAnimalTable) {
                    window.animalPageController.refreshAnimalTable();
                    addStatus('✅ Animal controller table refreshed', 'success');
                }
            } catch (error) {
                addStatus(`❌ Error creating test data: ${error.message}`, 'error');
            }
        }
        
        function updateTable() {
            try {
                const animals = JSON.parse(localStorage.getItem('animals') || '[]');
                const tbody = document.getElementById('animalTableBody');
                
                if (animals.length === 0) {
                    tbody.innerHTML = `
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #666;">
                                No animals found
                            </td>
                        </tr>
                    `;
                } else {
                    tbody.innerHTML = animals.map(animal => `
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">${animal.code}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${animal.type}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${animal.herdNumber}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${animal.gender}</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">${animal.weight} kg</td>
                        </tr>
                    `).join('');
                }
            } catch (error) {
                addStatus(`❌ Error updating table: ${error.message}`, 'error');
            }
        }
        
        function testSearch() {
            addStatus('🔍 Testing search functionality...', 'info');
            
            // Test 1: Check if animal controller exists
            if (window.animalPageController) {
                addStatus('✅ Animal page controller found', 'success');
                
                // Test 2: Check if search dropdown manager exists
                if (window.animalPageController.searchDropdownManager) {
                    addStatus('✅ Search dropdown manager found', 'success');
                    
                    // Test 3: Try to show dropdown
                    try {
                        const searchButton = document.getElementById('searchButton');
                        if (searchButton) {
                            addStatus('✅ Search button found', 'success');
                            window.animalPageController.searchDropdownManager.showDropdown(searchButton);
                            addStatus('✅ Search dropdown shown successfully', 'success');
                        } else {
                            addStatus('❌ Search button not found', 'error');
                        }
                    } catch (error) {
                        addStatus(`❌ Error showing dropdown: ${error.message}`, 'error');
                    }
                } else {
                    addStatus('❌ Search dropdown manager not found', 'error');
                }
            } else {
                addStatus('❌ Animal page controller not found', 'error');
            }
            
            // Test 4: Test global search functions
            if (typeof window.performAnimalSearch === 'function') {
                addStatus('✅ performAnimalSearch function available', 'success');
            } else {
                addStatus('❌ performAnimalSearch function not available', 'error');
            }
            
            if (typeof window.searchAnimals === 'function') {
                addStatus('✅ searchAnimals function available', 'success');
                
                // Test search with data
                try {
                    const results = window.searchAnimals('A001');
                    addStatus(`✅ Search test: found ${results.length} results for "A001"`, 'success');
                } catch (error) {
                    addStatus(`❌ Error testing search: ${error.message}`, 'error');
                }
            } else {
                addStatus('❌ searchAnimals function not available', 'error');
            }
        }
        
        // Wait for scripts to load then update table
        window.addEventListener('load', () => {
            setTimeout(() => {
                updateTable();
                addStatus('🚀 Page loaded and ready for testing', 'info');
            }, 500);
        });
    </script>
</body>
</html>
