<DocTYPE html>
    <html>

    <head>
        <meta charset="utf-8" />

        <link rel="stylesheet" href="css/addnewingrediant.css" />
       
    </head>

    <body>
        <div id="notification-container"></div>
        <div class="choose-update-milch">
             <div class="frame-4">
                        <div class="frame-5">
                            <img class="update" src="icons/ingrediant icon.png">

                            <div class="text-wrapper-5">Add New Ingrediant</div>
                        </div>

                        <div class="frame-6">
                            <div class="frame-7">
                                <div class="text-wrapper-6">Name</div>
                                <input class="data-filled" id="ingredientName"></input>
                            </div>                            <div class="frame-71">
                               
                                <div class="dropdown-container">
                                    <div class="text-wrapper-6" id="ingredientTypeBtn">Select Ingredient Type</div>
                                    <div class="dropdown-content" id="ingredientTypeDropdown">
                                        <a href="#" data-value="concentrates">Concentrates</a>
                                        <a href="#" data-value="roughages">Roughages</a>
                                        <a href="#" data-value="millByProduct">Mill by Product</a>
                                        <a href="#" data-value="oilseedByProduct">Oilseed by Product</a>
                                        <a href="#" data-value="forages">Forages</a>
                                    </div>
                                </div>
                            </div>

                             <div class="frame-7">
                                <div class="text-wrapper-6">Proteins</div>
                                <input class="data-filled" id="ingredientProtein"></input>
                            </div>

                            <div class="frame-7">
                                <div class="text-wrapper-6">crude Fiber(CF) </div>
                                <input class="data-filled" id="ingredientCF"></input>
                            </div>

                            <div class="frame-7">
                                <div class="text-wrapper-6">Total Digestable Nutrients(TDN) </div>
                                <input class="data-filled" id="ingredientTDN"></input>
                            </div>
                            
                            <div class="frame-7">
                                <div class="text-wrapper-6">Metabolizable Energy(ME)</div>
                                <input class="data-filled" id="ingredientME"></input>
                            </div>

                           

                            <div class="frame-8">
                                <button class="frame-9" id="saveIngredientBtn">
                                     <img class="update" src="icons/save.png">
                                    <div class="btntext">Save Ingrediant</div>
                                </button>
                            </div>
                        </div>

                    </div>
        </div>        <script src="js/addnewingrediant.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const dropBtn = document.getElementById('ingredientTypeBtn');
                const dropContent = document.getElementById('ingredientTypeDropdown');

                // Toggle dropdown on button click
                dropBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    dropContent.classList.toggle('show');
                });

                // Handle option selection
                dropContent.querySelectorAll('a').forEach(option => {
                    option.addEventListener('click', function(e) {
                        e.preventDefault();
                        dropBtn.textContent = this.textContent;
                        dropContent.classList.remove('show');
                    });
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.matches('.dropbtn')) {
                        dropContent.classList.remove('show');
                    }
                });
            });
        </script>
    </body>

    </html>